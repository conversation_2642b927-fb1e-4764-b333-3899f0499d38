/*! jQuery v3.7.1 | (c) OpenJS Foundation and other contributors | jquery.org/license */

/*! layer-v3.5.5 Web 通用弹出层组件 MIT License  http://layer.layui.com/  By 贤心 */

/*
 * Toastr
 * Copyright 2012-2015
 * Authors: <AUTHORS>
 * All Rights Reserved.
 * Use, reproduction, distribution, and modification of this code is subject to the terms and
 * conditions of the MIT license, available at http://www.opensource.org/licenses/mit-license.php
 *
 * ARIA Support: G<PERSON>
 *
 * Project: https://github.com/CodeSeven/toastr
 */

/*!art-template - Template Engine | http://aui.github.com/artTemplate/*/

//! moment.js
//! version : 2.30.1
//! authors : <PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors
//! license : MIT
//! momentjs.com

/*
* bootstrap-table - v1.11.10 - 2023-06-14
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/

/*
* bootstrap-table - v1.11.12 - 2024-03-28
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/

// License: http://dragsort.codeplex.com/license

/**
 * @summary     SelectPage
 * @desc        Simple and powerful selection plugin
 * @file        selectpage.js
 * @version     2.21
 * <AUTHOR>
 * @contact     https://terryz.github.io/
 * @license     MIT License
 *
 */

if(function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";function i(t,e,i){var n,o,a=(i=i||ut).createElement("script");if(a.text=t,e)for(n in pt)(o=e[n]||e.getAttribute&&e.getAttribute(n))&&a.setAttribute(n,o);i.head.appendChild(a).parentNode.removeChild(a)}function n(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?nt[ot.call(t)]||"object":typeof t}function o(t){var e=!!t&&"length"in t&&t.length,i=n(t);return!ct(t)&&!dt(t)&&("array"===i||0===e||"number"==typeof e&&0<e&&e-1 in t)}function a(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}function r(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}function s(t,e,i){return ct(e)?mt.grep(t,function(t,n){return!!e.call(t,n,t)!==i}):e.nodeType?mt.grep(t,function(t){return t===e!==i}):"string"!=typeof e?mt.grep(t,function(t){return-1<it.call(e,t)!==i}):mt.filter(e,t,i)}function l(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}function c(t){return t}function d(t){throw t}function u(t,e,i,n){var o;try{t&&ct(o=t.promise)?o.call(t).done(e).fail(i):t&&ct(o=t.then)?o.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}function p(){ut.removeEventListener("DOMContentLoaded",p),t.removeEventListener("load",p),mt.ready()}function h(t,e){return e.toUpperCase()}function f(t){return t.replace(Rt,"ms-").replace(Pt,h)}function m(){this.expando=mt.expando+m.uid++}function g(t,e,i){var n,o;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(Yt,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i="true"===(o=i)||"false"!==o&&("null"===o?null:o===+o+""?+o:zt.test(o)?JSON.parse(o):o)}catch(t){}Ht.set(t,e,i)}else i=void 0;return i}function v(t,e,i,n){var o,a,r=20,s=n?function(){return n.cur()}:function(){return mt.css(t,e,"")},l=s(),c=i&&i[3]||(mt.cssNumber[e]?"":"px"),d=t.nodeType&&(mt.cssNumber[e]||"px"!==c&&+l)&&Ut.exec(mt.css(t,e));if(d&&d[3]!==c){for(l/=2,c=c||d[3],d=+l||1;r--;)mt.style(t,e,d+c),(1-a)*(1-(a=s()/l||.5))<=0&&(r=0),d/=a;d*=2,mt.style(t,e,d+c),i=i||[]}return i&&(d=+d||+l||0,o=i[1]?d+(i[1]+1)*i[2]:+i[2],n&&(n.unit=c,n.start=d,n.end=o)),o}function y(t,e){for(var i,n,o,a,r,s,l,c=[],d=0,u=t.length;d<u;d++)(n=t[d]).style&&(i=n.style.display,e?("none"===i&&(c[d]=jt.get(n,"display")||null,c[d]||(n.style.display="")),""===n.style.display&&Xt(n)&&(c[d]=(l=r=a=void 0,r=(o=n).ownerDocument,s=o.nodeName,(l=Qt[s])||(a=r.body.appendChild(r.createElement(s)),l=mt.css(a,"display"),a.parentNode.removeChild(a),"none"===l&&(l="block"),Qt[s]=l)))):"none"!==i&&(c[d]="none",jt.set(n,"display",i)));for(d=0;d<u;d++)null!=c[d]&&(t[d].style.display=c[d]);return t}function b(t,e){var i;return i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&a(t,e)?mt.merge([t],i):i}function x(t,e){for(var i=0,n=t.length;i<n;i++)jt.set(t[i],"globalEval",!e||jt.get(e[i],"globalEval"))}function w(t,e,i,o,a){for(var r,s,l,c,d,u,p=e.createDocumentFragment(),h=[],f=0,m=t.length;f<m;f++)if((r=t[f])||0===r)if("object"===n(r))mt.merge(h,r.nodeType?[r]:r);else if(ne.test(r)){for(s=s||p.appendChild(e.createElement("div")),l=(te.exec(r)||["",""])[1].toLowerCase(),c=ie[l]||ie._default,s.innerHTML=c[1]+mt.htmlPrefilter(r)+c[2],u=c[0];u--;)s=s.lastChild;mt.merge(h,s.childNodes),(s=p.firstChild).textContent=""}else h.push(e.createTextNode(r));for(p.textContent="",f=0;r=h[f++];)if(o&&-1<mt.inArray(r,o))a&&a.push(r);else if(d=Vt(r),s=b(p.appendChild(r),"script"),d&&x(s),i)for(u=0;r=s[u++];)ee.test(r.type||"")&&i.push(r);return p}function _(){return!0}function k(){return!1}function C(t,e,i,n,o,a){var r,s;if("object"==typeof e){for(s in"string"!=typeof i&&(n=n||i,i=void 0),e)C(t,s,i,n,e[s],a);return t}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=k;else if(!o)return t;return 1===a&&(r=o,(o=function(t){return mt().off(t),r.apply(this,arguments)}).guid=r.guid||(r.guid=mt.guid++)),t.each(function(){mt.event.add(this,e,o,n,i)})}function S(t,e,i){i?(jt.set(t,e,!1),mt.event.add(t,e,{namespace:!1,handler:function(t){var i,n=jt.get(this,e);if(1&t.isTrigger&&this[e]){if(n)(mt.event.special[e]||{}).delegateType&&t.stopPropagation();else if(n=J.call(arguments),jt.set(this,e,n),this[e](),i=jt.get(this,e),jt.set(this,e,!1),n!==i)return t.stopImmediatePropagation(),t.preventDefault(),i}else n&&(jt.set(this,e,mt.event.trigger(n[0],n.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=_)}})):void 0===jt.get(t,e)&&mt.event.add(t,e,_)}function T(t,e){return a(t,"table")&&a(11!==e.nodeType?e:e.firstChild,"tr")&&mt(t).children("tbody")[0]||t}function D(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function $(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function E(t,e){var i,n,o,a,r,s;if(1===e.nodeType){if(jt.hasData(t)&&(s=jt.get(t).events))for(o in jt.remove(e,"handle events"),s)for(i=0,n=s[o].length;i<n;i++)mt.event.add(e,o,s[o][i]);Ht.hasData(t)&&(a=Ht.access(t),r=mt.extend({},a),Ht.set(e,r))}}function F(t,e,n,o){e=tt(e);var a,r,s,l,c,d,u=0,p=t.length,h=p-1,f=e[0],m=ct(f);if(m||1<p&&"string"==typeof f&&!lt.checkClone&&re.test(f))return t.each(function(i){var a=t.eq(i);m&&(e[0]=f.call(this,i,a.html())),F(a,e,n,o)});if(p&&(r=(a=w(e,t[0].ownerDocument,!1,t,o)).firstChild,1===a.childNodes.length&&(a=r),r||o)){for(l=(s=mt.map(b(a,"script"),D)).length;u<p;u++)c=a,u!==h&&(c=mt.clone(c,!0,!0),l&&mt.merge(s,b(c,"script"))),n.call(t[u],c,u);if(l)for(d=s[s.length-1].ownerDocument,mt.map(s,$),u=0;u<l;u++)c=s[u],ee.test(c.type||"")&&!jt.access(c,"globalEval")&&mt.contains(d,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?mt._evalUrl&&!c.noModule&&mt._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},d):i(c.textContent.replace(se,""),c,d))}return t}function A(t,e,i){for(var n,o=e?mt.filter(e,t):t,a=0;null!=(n=o[a]);a++)i||1!==n.nodeType||mt.cleanData(b(n)),n.parentNode&&(i&&Vt(n)&&x(b(n,"script")),n.parentNode.removeChild(n));return t}function O(t,e,i){var n,o,a,r,s=ce.test(e),l=t.style;return(i=i||de(t))&&(r=i.getPropertyValue(e)||i[e],s&&r&&(r=r.replace(xt,"$1")||void 0),""!==r||Vt(t)||(r=mt.style(t,e)),!lt.pixelBoxStyles()&&le.test(r)&&pe.test(e)&&(n=l.width,o=l.minWidth,a=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=i.width,l.width=n,l.minWidth=o,l.maxWidth=a)),void 0!==r?r+"":r}function M(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}function L(t){return mt.cssProps[t]||me[t]||(t in fe?t:me[t]=function(t){for(var e=t[0].toUpperCase()+t.slice(1),i=he.length;i--;)if((t=he[i]+e)in fe)return t}(t)||t)}function N(t,e,i){var n=Ut.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function R(t,e,i,n,o,a){var r="width"===e?1:0,s=0,l=0,c=0;if(i===(n?"border":"content"))return 0;for(;r<4;r+=2)"margin"===i&&(c+=mt.css(t,i+qt[r],!0,o)),n?("content"===i&&(l-=mt.css(t,"padding"+qt[r],!0,o)),"margin"!==i&&(l-=mt.css(t,"border"+qt[r]+"Width",!0,o))):(l+=mt.css(t,"padding"+qt[r],!0,o),"padding"!==i?l+=mt.css(t,"border"+qt[r]+"Width",!0,o):s+=mt.css(t,"border"+qt[r]+"Width",!0,o));return!n&&0<=a&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-a-l-s-.5))||0),l+c}function P(t,e,i){var n=de(t),o=(!lt.boxSizingReliable()||i)&&"border-box"===mt.css(t,"boxSizing",!1,n),r=o,s=O(t,e,n),l="offset"+e[0].toUpperCase()+e.slice(1);if(le.test(s)){if(!i)return s;s="auto"}return(!lt.boxSizingReliable()&&o||!lt.reliableTrDimensions()&&a(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===mt.css(t,"display",!1,n))&&t.getClientRects().length&&(o="border-box"===mt.css(t,"boxSizing",!1,n),(r=l in t)&&(s=t[l])),(s=parseFloat(s)||0)+R(t,e,i||(o?"border":"content"),r,n,s)+"px"}function I(t,e,i,n,o){return new I.prototype.init(t,e,i,n,o)}function j(){xe&&(!1===ut.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(j):t.setTimeout(j,mt.fx.interval),mt.fx.tick())}function H(){return t.setTimeout(function(){be=void 0}),be=Date.now()}function z(t,e){var i,n=0,o={height:t};for(e=e?1:0;n<4;n+=2-e)o["margin"+(i=qt[n])]=o["padding"+i]=t;return e&&(o.opacity=o.width=t),o}function Y(t,e,i){for(var n,o=(B.tweeners[e]||[]).concat(B.tweeners["*"]),a=0,r=o.length;a<r;a++)if(n=o[a].call(i,e,t))return n}function B(t,e,i){var n,o,a=0,r=B.prefilters.length,s=mt.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var e=be||H(),i=Math.max(0,c.startTime+c.duration-e),n=1-(i/c.duration||0),a=0,r=c.tweens.length;a<r;a++)c.tweens[a].run(n);return s.notifyWith(t,[c,n,i]),n<1&&r?i:(r||s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c]),!1)},c=s.promise({elem:t,props:mt.extend({},e),opts:mt.extend(!0,{specialEasing:{},easing:mt.easing._default},i),originalProperties:e,originalOptions:i,startTime:be||H(),duration:i.duration,tweens:[],createTween:function(e,i){var n=mt.Tween(t,c.opts,e,i,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(n),n},stop:function(e){var i=0,n=e?c.tweens.length:0;if(o)return this;for(o=!0;i<n;i++)c.tweens[i].run(1);return e?(s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c,e])):s.rejectWith(t,[c,e]),this}}),d=c.props;for((!function(t,e){var i,n,o,a,r;for(i in t)if(o=e[n=f(i)],a=t[i],Array.isArray(a)&&(o=a[1],a=t[i]=a[0]),i!==n&&(t[n]=a,delete t[i]),(r=mt.cssHooks[n])&&"expand"in r)for(i in a=r.expand(a),delete t[n],a)i in t||(t[i]=a[i],e[i]=o);else e[n]=o}(d,c.opts.specialEasing));a<r;a++)if(n=B.prefilters[a].call(c,t,d,c.opts))return ct(n.stop)&&(mt._queueHooks(c.elem,c.opts.queue).stop=n.stop.bind(n)),n;return mt.map(d,Y,c),ct(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),mt.fx.timer(mt.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}function U(t){return(t.match(Ot)||[]).join(" ")}function q(t){return t.getAttribute&&t.getAttribute("class")||""}function W(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(Ot)||[]}function V(t,e,i,o){var a;if(Array.isArray(e))mt.each(e,function(e,n){i||Ne.test(t)?o(t,n):V(t+"["+("object"==typeof n&&null!=n?e:"")+"]",n,i,o)});else if(i||"object"!==n(e))o(t,e);else for(a in e)V(t+"["+a+"]",e[a],i,o)}function G(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,o=0,a=e.toLowerCase().match(Ot)||[];if(ct(i))for(;n=a[o++];)"+"===n[0]?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function X(t,e,i,n){function o(s){var l;return a[s]=!0,mt.each(t[s]||[],function(t,s){var c=s(e,i,n);return"string"!=typeof c||r||a[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),o(c),!1)}),l}var a={},r=t===We;return o(e.dataTypes[0])||!a["*"]&&o("*")}function Q(t,e){var i,n,o=mt.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((o[i]?t:n||(n={}))[i]=e[i]);return n&&mt.extend(!0,t,n),t}var K=[],Z=Object.getPrototypeOf,J=K.slice,tt=K.flat?function(t){return K.flat.call(t)}:function(t){return K.concat.apply([],t)},et=K.push,it=K.indexOf,nt={},ot=nt.toString,at=nt.hasOwnProperty,rt=at.toString,st=rt.call(Object),lt={},ct=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},dt=function(t){return null!=t&&t===t.window},ut=t.document,pt={type:!0,src:!0,nonce:!0,noModule:!0},ht="3.7.1",ft=/HTML$/i,mt=function(t,e){return new mt.fn.init(t,e)};mt.fn=mt.prototype={jquery:ht,constructor:mt,length:0,toArray:function(){return J.call(this)},get:function(t){return null==t?J.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=mt.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return mt.each(this,t)},map:function(t){return this.pushStack(mt.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(J.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(mt.grep(this,function(t,e){return(e+1)%2}))},odd:function(){return this.pushStack(mt.grep(this,function(t,e){return e%2}))},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(0<=i&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:et,sort:K.sort,splice:K.splice},mt.extend=mt.fn.extend=function(){var t,e,i,n,o,a,r=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[s]||{},s++),"object"==typeof r||ct(r)||(r={}),s===l&&(r=this,s--);s<l;s++)if(null!=(t=arguments[s]))for(e in t)n=t[e],"__proto__"!==e&&r!==n&&(c&&n&&(mt.isPlainObject(n)||(o=Array.isArray(n)))?(i=r[e],a=o&&!Array.isArray(i)?[]:o||mt.isPlainObject(i)?i:{},o=!1,r[e]=mt.extend(c,a,n)):void 0!==n&&(r[e]=n));return r},mt.extend({expando:"jQuery"+(ht+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==ot.call(t)||(e=Z(t))&&("function"!=typeof(i=at.call(e,"constructor")&&e.constructor)||rt.call(i)!==st))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){i(t,{nonce:e&&e.nonce},n)},each:function(t,e){var i,n=0;if(o(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},text:function(t){var e,i="",n=0,o=t.nodeType;if(!o)for(;e=t[n++];)i+=mt.text(e);return 1===o||11===o?t.textContent:9===o?t.documentElement.textContent:3===o||4===o?t.nodeValue:i},makeArray:function(t,e){var i=e||[];return null!=t&&(o(Object(t))?mt.merge(i,"string"==typeof t?[t]:t):et.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:it.call(e,t,i)},isXMLDoc:function(t){var e=t&&t.namespaceURI,i=t&&(t.ownerDocument||t).documentElement;return!ft.test(e||i&&i.nodeName||"HTML")},merge:function(t,e){for(var i=+e.length,n=0,o=t.length;n<i;n++)t[o++]=e[n];return t.length=o,t},grep:function(t,e,i){for(var n=[],o=0,a=t.length,r=!i;o<a;o++)!e(t[o],o)!==r&&n.push(t[o]);return n},map:function(t,e,i){var n,a,r=0,s=[];if(o(t))for(n=t.length;r<n;r++)null!=(a=e(t[r],r,i))&&s.push(a);else for(r in t)null!=(a=e(t[r],r,i))&&s.push(a);return tt(s)},guid:1,support:lt}),"function"==typeof Symbol&&(mt.fn[Symbol.iterator]=K[Symbol.iterator]),mt.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){nt["[object "+e+"]"]=e.toLowerCase()});var gt=K.pop,vt=K.sort,yt=K.splice,bt="[\\x20\\t\\r\\n\\f]",xt=new RegExp("^"+bt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+bt+"+$","g");mt.contains=function(t,e){var i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(t.contains?t.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))};var wt=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;mt.escapeSelector=function(t){return(t+"").replace(wt,r)};var _t=ut,kt=et;!function(){function e(t,i,n,o){var a,r,s,d,h,f,m,g=i&&i.ownerDocument,v=i?i.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==v&&9!==v&&11!==v)return n;if(!o&&(c(i),i=i||S,D)){if(11!==v&&(h=tt.exec(t)))if(a=h[1]){if(9===v){if(!(s=i.getElementById(a)))return n;if(s.id===a)return F.call(n,s),n}else if(g&&(s=g.getElementById(a))&&e.contains(i,s)&&s.id===a)return F.call(n,s),n}else{if(h[2])return F.apply(n,i.getElementsByTagName(t)),n;if((a=h[3])&&i.getElementsByClassName)return F.apply(n,i.getElementsByClassName(a)),n}if(!(P[t+" "]||$&&$.test(t))){if(m=t,g=i,1===v&&(W.test(t)||q.test(t))){for((g=et.test(t)&&l(i.parentNode)||i)==i&&lt.scope||((d=i.getAttribute("id"))?d=mt.escapeSelector(d):i.setAttribute("id",d=A)),r=(f=u(t)).length;r--;)f[r]=(d?"#"+d:":scope")+" "+p(f[r]);m=f.join(",")}try{return F.apply(n,g.querySelectorAll(m)),n}catch(i){P(t,!0)}finally{d===A&&i.removeAttribute("id")}}}return b(t.replace(xt,"$1"),i,n,o)}function i(){var t=[];return function e(i,n){return t.push(i+" ")>w.cacheLength&&delete e[t.shift()],e[i+" "]=n}}function n(t){return t[A]=!0,t}function o(t){var e=S.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function r(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&st(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function s(t){return n(function(e){return e=+e,n(function(i,n){for(var o,a=t([],i.length,e),r=a.length;r--;)i[o=a[r]]&&(i[o]=!(n[o]=i[o]))})})}function l(t){return t&&void 0!==t.getElementsByTagName&&t}function c(t){var i,n=t?t.ownerDocument||t:_t;return n!=S&&9===n.nodeType&&n.documentElement&&(T=(S=n).documentElement,D=!mt.isXMLDoc(S),E=T.matches||T.webkitMatchesSelector||T.msMatchesSelector,T.msMatchesSelector&&_t!=S&&(i=S.defaultView)&&i.top!==i&&i.addEventListener("unload",rt),lt.getById=o(function(t){return T.appendChild(t).id=mt.expando,!S.getElementsByName||!S.getElementsByName(mt.expando).length}),lt.disconnectedMatch=o(function(t){return E.call(t,"*")}),lt.scope=o(function(){return S.querySelectorAll(":scope")}),lt.cssHas=o(function(){try{return S.querySelector(":has(*,:jqfake)"),!1}catch(t){return!0}}),lt.getById?(w.filter.ID=function(t){var e=t.replace(nt,ot);return function(t){return t.getAttribute("id")===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&D){var i=e.getElementById(t);return i?[i]:[]}}):(w.filter.ID=function(t){var e=t.replace(nt,ot);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}},w.find.ID=function(t,e){if(void 0!==e.getElementById&&D){var i,n,o,a=e.getElementById(t);if(a){if((i=a.getAttributeNode("id"))&&i.value===t)return[a];for(o=e.getElementsByName(t),n=0;a=o[n++];)if((i=a.getAttributeNode("id"))&&i.value===t)return[a]}return[]}}),w.find.TAG=function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},w.find.CLASS=function(t,e){if(void 0!==e.getElementsByClassName&&D)return e.getElementsByClassName(t)},$=[],o(function(t){var e;T.appendChild(t).innerHTML="<a id='"+A+"' href='' disabled='disabled'></a><select id='"+A+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||$.push("\\["+bt+"*(?:value|"+j+")"),t.querySelectorAll("[id~="+A+"-]").length||$.push("~="),t.querySelectorAll("a#"+A+"+*").length||$.push(".#.+[+~]"),t.querySelectorAll(":checked").length||$.push(":checked"),(e=S.createElement("input")).setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),T.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&$.push(":enabled",":disabled"),(e=S.createElement("input")).setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||$.push("\\["+bt+"*name"+bt+"*="+bt+"*(?:''|\"\")")}),lt.cssHas||$.push(":has"),$=$.length&&new RegExp($.join("|")),I=function(t,i){if(t===i)return C=!0,0;var n=!t.compareDocumentPosition-!i.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)==(i.ownerDocument||i)?t.compareDocumentPosition(i):1)||!lt.sortDetached&&i.compareDocumentPosition(t)===n?t===S||t.ownerDocument==_t&&e.contains(_t,t)?-1:i===S||i.ownerDocument==_t&&e.contains(_t,i)?1:k?it.call(k,t)-it.call(k,i):0:4&n?-1:1)}),S}function d(){}function u(t,i){var n,o,a,r,s,l,c,d=N[t+" "];if(d)return i?0:d.slice(0);for(s=t,l=[],c=w.preFilter;s;){for(r in n&&!(o=U.exec(s))||(o&&(s=s.slice(o[0].length)||s),l.push(a=[])),n=!1,(o=q.exec(s))&&(n=o.shift(),a.push({value:n,type:o[0].replace(xt," ")}),s=s.slice(n.length)),w.filter)!(o=X[r].exec(s))||c[r]&&!(o=c[r](o))||(n=o.shift(),a.push({value:n,type:r,matches:o}),s=s.slice(n.length));if(!n)break}return i?s.length:s?e.error(t):N(t,l).slice(0)}function p(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function h(t,e,i){var n=e.dir,o=e.next,r=o||n,s=i&&"parentNode"===r,l=M++;return e.first?function(e,i,o){for(;e=e[n];)if(1===e.nodeType||s)return t(e,i,o);return!1}:function(e,i,c){var d,u,p=[O,l];if(c){for(;e=e[n];)if((1===e.nodeType||s)&&t(e,i,c))return!0}else for(;e=e[n];)if(1===e.nodeType||s)if(u=e[A]||(e[A]={}),o&&a(e,o))e=e[n]||e;else{if((d=u[r])&&d[0]===O&&d[1]===l)return p[2]=d[2];if((u[r]=p)[2]=t(e,i,c))return!0}return!1}}function f(t){return 1<t.length?function(e,i,n){for(var o=t.length;o--;)if(!t[o](e,i,n))return!1;return!0}:t[0]}function m(t,e,i,n,o){for(var a,r=[],s=0,l=t.length,c=null!=e;s<l;s++)(a=t[s])&&(i&&!i(a,n,o)||(r.push(a),c&&e.push(s)));return r}function g(t,i,o,a,r,s){return a&&!a[A]&&(a=g(a)),r&&!r[A]&&(r=g(r,s)),n(function(n,s,l,c){var d,u,p,h,f=[],g=[],v=s.length,y=n||function(t,i,n){for(var o=0,a=i.length;o<a;o++)e(t,i[o],n);return n}(i||"*",l.nodeType?[l]:l,[]),b=!t||!n&&i?y:m(y,f,t,l,c);if(o?o(b,h=r||(n?t:v||a)?[]:s,l,c):h=b,a)for(d=m(h,g),a(d,[],l,c),u=d.length;u--;)(p=d[u])&&(h[g[u]]=!(b[g[u]]=p));if(n){if(r||t){if(r){for(d=[],u=h.length;u--;)(p=h[u])&&d.push(b[u]=p);r(null,h=[],d,c)}for(u=h.length;u--;)(p=h[u])&&-1<(d=r?it.call(n,p):f[u])&&(n[d]=!(s[d]=p))}}else h=m(h===s?h.splice(v,h.length):h),r?r(null,s,h,c):F.apply(s,h)})}function v(t){for(var e,i,n,o=t.length,a=w.relative[t[0].type],r=a||w.relative[" "],s=a?1:0,l=h(function(t){return t===e},r,!0),c=h(function(t){return-1<it.call(e,t)},r,!0),d=[function(t,i,n){var o=!a&&(n||i!=_)||((e=i).nodeType?l(t,i,n):c(t,i,n));return e=null,o}];s<o;s++)if(i=w.relative[t[s].type])d=[h(f(d),i)];else{if((i=w.filter[t[s].type].apply(null,t[s].matches))[A]){for(n=++s;n<o&&!w.relative[t[n].type];n++);return g(1<s&&f(d),1<s&&p(t.slice(0,s-1).concat({value:" "===t[s-2].type?"*":""})).replace(xt,"$1"),i,s<n&&v(t.slice(s,n)),n<o&&v(t=t.slice(n)),n<o&&p(t))}d.push(i)}return f(d)}function y(t,e){var i,o,a,r,s,l,d=[],p=[],h=R[t+" "];if(!h){for(e||(e=u(t)),i=e.length;i--;)(h=v(e[i]))[A]?d.push(h):p.push(h);(h=R(t,(o=p,r=0<(a=d).length,s=0<o.length,l=function(t,e,i,n,l){var d,u,p,h=0,f="0",g=t&&[],v=[],y=_,b=t||s&&w.find.TAG("*",l),x=O+=null==y?1:Math.random()||.1,k=b.length;for(l&&(_=e==S||e||l);f!==k&&null!=(d=b[f]);f++){if(s&&d){for(u=0,e||d.ownerDocument==S||(c(d),i=!D);p=o[u++];)if(p(d,e||S,i)){F.call(n,d);break}l&&(O=x)}r&&((d=!p&&d)&&h--,t&&g.push(d))}if(h+=f,r&&f!==h){for(u=0;p=a[u++];)p(g,v,e,i);if(t){if(0<h)for(;f--;)g[f]||v[f]||(v[f]=gt.call(n));v=m(v)}F.apply(n,v),l&&!t&&0<v.length&&1<h+a.length&&mt.uniqueSort(n)}return l&&(O=x,_=y),g},r?n(l):l))).selector=t}return h}function b(t,e,i,n){var o,a,r,s,c,d="function"==typeof t&&t,h=!n&&u(t=d.selector||t);if(i=i||[],1===h.length){if(2<(a=h[0]=h[0].slice(0)).length&&"ID"===(r=a[0]).type&&9===e.nodeType&&D&&w.relative[a[1].type]){if(!(e=(w.find.ID(r.matches[0].replace(nt,ot),e)||[])[0]))return i;d&&(e=e.parentNode),t=t.slice(a.shift().value.length)}for(o=X.needsContext.test(t)?0:a.length;o--&&(r=a[o],!w.relative[s=r.type]);)if((c=w.find[s])&&(n=c(r.matches[0].replace(nt,ot),et.test(a[0].type)&&l(e.parentNode)||e))){if(a.splice(o,1),!(t=n.length&&p(a)))return F.apply(i,n),i;break}}return(d||y(t,h))(n,e,!D,i,!e||et.test(t)&&l(e.parentNode)||e),i}var x,w,_,k,C,S,T,D,$,E,F=kt,A=mt.expando,O=0,M=0,L=i(),N=i(),R=i(),P=i(),I=function(t,e){return t===e&&(C=!0),0},j="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",H="(?:\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",z="\\["+bt+"*("+H+")(?:"+bt+"*([*^$|!~]?=)"+bt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+H+"))|)"+bt+"*\\]",Y=":("+H+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+z+")*)|.*)\\)|)",B=new RegExp(bt+"+","g"),U=new RegExp("^"+bt+"*,"+bt+"*"),q=new RegExp("^"+bt+"*([>+~]|"+bt+")"+bt+"*"),W=new RegExp(bt+"|>"),V=new RegExp(Y),G=new RegExp("^"+H+"$"),X={ID:new RegExp("^#("+H+")"),CLASS:new RegExp("^\\.("+H+")"),TAG:new RegExp("^("+H+"|[*])"),ATTR:new RegExp("^"+z),PSEUDO:new RegExp("^"+Y),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+bt+"*(even|odd|(([+-]|)(\\d*)n|)"+bt+"*(?:([+-]|)"+bt+"*(\\d+)|))"+bt+"*\\)|)","i"),bool:new RegExp("^(?:"+j+")$","i"),needsContext:new RegExp("^"+bt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+bt+"*((?:-\\d)?\\d*)"+bt+"*\\)|)(?=[^-]|$)","i")},Q=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,tt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,et=/[+~]/,nt=new RegExp("\\\\[\\da-fA-F]{1,6}"+bt+"?|\\\\([^\\r\\n\\f])","g"),ot=function(t,e){var i="0x"+t.slice(1)-65536;return e||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},rt=function(){c()},st=h(function(t){return!0===t.disabled&&a(t,"fieldset")},{dir:"parentNode",next:"legend"});try{F.apply(K=J.call(_t.childNodes),_t.childNodes),K[_t.childNodes.length].nodeType}catch(x){F={apply:function(t,e){kt.apply(t,J.call(e))},call:function(t){kt.apply(t,J.call(arguments,1))}}}for(x in e.matches=function(t,i){return e(t,null,null,i)},e.matchesSelector=function(t,i){if(c(t),D&&!P[i+" "]&&(!$||!$.test(i)))try{var n=E.call(t,i);if(n||lt.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){P(i,!0)}return 0<e(i,S,null,[t]).length},e.contains=function(t,e){return(t.ownerDocument||t)!=S&&c(t),mt.contains(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!=S&&c(t);var i=w.attrHandle[e.toLowerCase()],n=i&&at.call(w.attrHandle,e.toLowerCase())?i(t,e,!D):void 0;return void 0!==n?n:t.getAttribute(e)},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},mt.uniqueSort=function(t){var e,i=[],n=0,o=0;if(C=!lt.sortStable,k=!lt.sortStable&&J.call(t,0),vt.call(t,I),C){for(;e=t[o++];)e===t[o]&&(n=i.push(o));for(;n--;)yt.call(t,i[n],1)}return k=null,t},mt.fn.uniqueSort=function(){return this.pushStack(mt.uniqueSort(J.apply(this)))},(w=mt.expr={cacheLength:50,createPseudo:n,match:X,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(nt,ot),t[3]=(t[3]||t[4]||t[5]||"").replace(nt,ot),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return X.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&V.test(i)&&(e=u(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(nt,ot).toLowerCase();return"*"===t?function(){return!0}:function(t){return a(t,e)}},CLASS:function(t){var e=L[t+" "];return e||(e=new RegExp("(^|"+bt+")"+t+"("+bt+"|$)"))&&L(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,i,n){return function(o){var a=e.attr(o,t);return null==a?"!="===i:!i||(a+="","="===i?a===n:"!="===i?a!==n:"^="===i?n&&0===a.indexOf(n):"*="===i?n&&-1<a.indexOf(n):"$="===i?n&&a.slice(-n.length)===n:"~="===i?-1<(" "+a.replace(B," ")+" ").indexOf(n):"|="===i&&(a===n||a.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,i,n,o){var r="nth"!==t.slice(0,3),s="last"!==t.slice(-4),l="of-type"===e;return 1===n&&0===o?function(t){return!!t.parentNode}:function(e,i,c){var d,u,p,h,f,m=r!==s?"nextSibling":"previousSibling",g=e.parentNode,v=l&&e.nodeName.toLowerCase(),y=!c&&!l,b=!1;if(g){if(r){for(;m;){for(p=e;p=p[m];)if(l?a(p,v):1===p.nodeType)return!1;f=m="only"===t&&!f&&"nextSibling"}return!0}if(f=[s?g.firstChild:g.lastChild],s&&y){for(b=(h=(d=(u=g[A]||(g[A]={}))[t]||[])[0]===O&&d[1])&&d[2],p=h&&g.childNodes[h];p=++h&&p&&p[m]||(b=h=0)||f.pop();)if(1===p.nodeType&&++b&&p===e){u[t]=[O,h,b];break}}else if(y&&(b=h=(d=(u=e[A]||(e[A]={}))[t]||[])[0]===O&&d[1]),!1===b)for(;(p=++h&&p&&p[m]||(b=h=0)||f.pop())&&((l?!a(p,v):1!==p.nodeType)||!++b||(y&&((u=p[A]||(p[A]={}))[t]=[O,b]),p!==e)););return(b-=o)===n||b%n==0&&0<=b/n}}},PSEUDO:function(t,i){var o,a=w.pseudos[t]||w.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return a[A]?a(i):1<a.length?(o=[t,t,"",i],w.setFilters.hasOwnProperty(t.toLowerCase())?n(function(t,e){for(var n,o=a(t,i),r=o.length;r--;)t[n=it.call(t,o[r])]=!(e[n]=o[r])}):function(t){return a(t,0,o)}):a}},pseudos:{not:n(function(t){var e=[],i=[],o=y(t.replace(xt,"$1"));return o[A]?n(function(t,e,i,n){for(var a,r=o(t,null,n,[]),s=t.length;s--;)(a=r[s])&&(t[s]=!(e[s]=a))}):function(t,n,a){return e[0]=t,o(e,null,a,i),e[0]=null,!i.pop()}}),has:n(function(t){return function(i){return 0<e(t,i).length}}),contains:n(function(t){return t=t.replace(nt,ot),function(e){return-1<(e.textContent||mt.text(e)).indexOf(t)}}),lang:n(function(t){return G.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(nt,ot).toLowerCase(),function(e){var i;do{if(i=D?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===T},focus:function(t){return t===function(){try{return S.activeElement}catch(t){}}()&&S.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:r(!1),disabled:r(!0),checked:function(t){return a(t,"input")&&!!t.checked||a(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return Z.test(t.nodeName)},input:function(t){return Q.test(t.nodeName)},button:function(t){return a(t,"input")&&"button"===t.type||a(t,"button")},text:function(t){var e;return a(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:s(function(){return[0]}),last:s(function(t,e){return[e-1]}),eq:s(function(t,e,i){return[i<0?i+e:i]}),even:s(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:s(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:s(function(t,e,i){var n;for(n=i<0?i+e:e<i?e:i;0<=--n;)t.push(n);return t}),gt:s(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[x]=function(t){return function(e){return a(e,"input")&&e.type===t}}(x);for(x in{submit:!0,reset:!0})w.pseudos[x]=function(t){return function(e){return(a(e,"input")||a(e,"button"))&&e.type===t}}(x);d.prototype=w.filters=w.pseudos,w.setFilters=new d,lt.sortStable=A.split("").sort(I).join("")===A,c(),lt.sortDetached=o(function(t){return 1&t.compareDocumentPosition(S.createElement("fieldset"))}),mt.find=e,mt.expr[":"]=mt.expr.pseudos,mt.unique=mt.uniqueSort,e.compile=y,e.select=b,e.setDocument=c,e.tokenize=u,e.escape=mt.escapeSelector,e.getText=mt.text,e.isXML=mt.isXMLDoc,e.selectors=mt.expr,e.support=mt.support,e.uniqueSort=mt.uniqueSort}();var Ct=function(t,e,i){for(var n=[],o=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(o&&mt(t).is(i))break;n.push(t)}return n},St=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},Tt=mt.expr.match.needsContext,Dt=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;mt.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?mt.find.matchesSelector(n,t)?[n]:[]:mt.find.matches(t,mt.grep(e,function(t){return 1===t.nodeType}))},mt.fn.extend({find:function(t){var e,i,n=this.length,o=this;if("string"!=typeof t)return this.pushStack(mt(t).filter(function(){for(e=0;e<n;e++)if(mt.contains(o[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)mt.find(t,o[e],i);return 1<n?mt.uniqueSort(i):i},filter:function(t){return this.pushStack(s(this,t||[],!1))},not:function(t){return this.pushStack(s(this,t||[],!0))},is:function(t){return!!s(this,"string"==typeof t&&Tt.test(t)?mt(t):t||[],!1).length}})
;var $t,Et=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(mt.fn.init=function(t,e,i){var n,o;if(!t)return this;if(i=i||$t,"string"==typeof t){if(!(n="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:Et.exec(t))||!n[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof mt?e[0]:e,mt.merge(this,mt.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:ut,!0)),Dt.test(n[1])&&mt.isPlainObject(e))for(n in e)ct(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}return(o=ut.getElementById(n[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):ct(t)?void 0!==i.ready?i.ready(t):t(mt):mt.makeArray(t,this)}).prototype=mt.fn,$t=mt(ut);var Ft=/^(?:parents|prev(?:Until|All))/,At={children:!0,contents:!0,next:!0,prev:!0};mt.fn.extend({has:function(t){var e=mt(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(mt.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,o=this.length,a=[],r="string"!=typeof t&&mt(t);if(!Tt.test(t))for(;n<o;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(r?-1<r.index(i):1===i.nodeType&&mt.find.matchesSelector(i,t))){a.push(i);break}return this.pushStack(1<a.length?mt.uniqueSort(a):a)},index:function(t){return t?"string"==typeof t?it.call(mt(t),this[0]):it.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(mt.uniqueSort(mt.merge(this.get(),mt(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),mt.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return Ct(t,"parentNode")},parentsUntil:function(t,e,i){return Ct(t,"parentNode",i)},next:function(t){return l(t,"nextSibling")},prev:function(t){return l(t,"previousSibling")},nextAll:function(t){return Ct(t,"nextSibling")},prevAll:function(t){return Ct(t,"previousSibling")},nextUntil:function(t,e,i){return Ct(t,"nextSibling",i)},prevUntil:function(t,e,i){return Ct(t,"previousSibling",i)},siblings:function(t){return St((t.parentNode||{}).firstChild,t)},children:function(t){return St(t.firstChild)},contents:function(t){return null!=t.contentDocument&&Z(t.contentDocument)?t.contentDocument:(a(t,"template")&&(t=t.content||t),mt.merge([],t.childNodes))}},function(t,e){mt.fn[t]=function(i,n){var o=mt.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(o=mt.filter(n,o)),1<this.length&&(At[t]||mt.uniqueSort(o),Ft.test(t)&&o.reverse()),this.pushStack(o)}});var Ot=/[^\x20\t\r\n\f]+/g;mt.Callbacks=function(t){var e,i;t="string"==typeof t?(e=t,i={},mt.each(e.match(Ot)||[],function(t,e){i[e]=!0}),i):mt.extend({},t);var o,a,r,s,l=[],c=[],d=-1,u=function(){for(s=s||t.once,r=o=!0;c.length;d=-1)for(a=c.shift();++d<l.length;)!1===l[d].apply(a[0],a[1])&&t.stopOnFalse&&(d=l.length,a=!1);t.memory||(a=!1),o=!1,s&&(l=a?[]:"")},p={add:function(){return l&&(a&&!o&&(d=l.length-1,c.push(a)),function e(i){mt.each(i,function(i,o){ct(o)?t.unique&&p.has(o)||l.push(o):o&&o.length&&"string"!==n(o)&&e(o)})}(arguments),a&&!o&&u()),this},remove:function(){return mt.each(arguments,function(t,e){for(var i;-1<(i=mt.inArray(e,l,i));)l.splice(i,1),i<=d&&d--}),this},has:function(t){return t?-1<mt.inArray(t,l):0<l.length},empty:function(){return l&&(l=[]),this},disable:function(){return s=c=[],l=a="",this},disabled:function(){return!l},lock:function(){return s=c=[],a||o||(l=a=""),this},locked:function(){return!!s},fireWith:function(t,e){return s||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),o||u()),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!r}};return p},mt.extend({Deferred:function(e){var i=[["notify","progress",mt.Callbacks("memory"),mt.Callbacks("memory"),2],["resolve","done",mt.Callbacks("once memory"),mt.Callbacks("once memory"),0,"resolved"],["reject","fail",mt.Callbacks("once memory"),mt.Callbacks("once memory"),1,"rejected"]],n="pending",o={state:function(){return n},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return o.then(null,t)},pipe:function(){var t=arguments;return mt.Deferred(function(e){mt.each(i,function(i,n){var o=ct(t[n[4]])&&t[n[4]];a[n[1]](function(){var t=o&&o.apply(this,arguments);t&&ct(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[n[0]+"With"](this,o?[t]:arguments)})}),t=null}).promise()},then:function(e,n,o){function a(e,i,n,o){return function(){var s=this,l=arguments,u=function(){var t,u;if(!(e<r)){if((t=n.apply(s,l))===i.promise())throw new TypeError("Thenable self-resolution");u=t&&("object"==typeof t||"function"==typeof t)&&t.then,ct(u)?o?u.call(t,a(r,i,c,o),a(r,i,d,o)):(r++,u.call(t,a(r,i,c,o),a(r,i,d,o),a(r,i,c,i.notifyWith))):(n!==c&&(s=void 0,l=[t]),(o||i.resolveWith)(s,l))}},p=o?u:function(){try{u()}catch(t){mt.Deferred.exceptionHook&&mt.Deferred.exceptionHook(t,p.error),r<=e+1&&(n!==d&&(s=void 0,l=[t]),i.rejectWith(s,l))}};e?p():(mt.Deferred.getErrorHook?p.error=mt.Deferred.getErrorHook():mt.Deferred.getStackHook&&(p.error=mt.Deferred.getStackHook()),t.setTimeout(p))}}var r=0;return mt.Deferred(function(t){i[0][3].add(a(0,t,ct(o)?o:c,t.notifyWith)),i[1][3].add(a(0,t,ct(e)?e:c)),i[2][3].add(a(0,t,ct(n)?n:d))}).promise()},promise:function(t){return null!=t?mt.extend(t,o):o}},a={};return mt.each(i,function(t,e){var r=e[2],s=e[5];o[e[1]]=r.add,s&&r.add(function(){n=s},i[3-t][2].disable,i[3-t][3].disable,i[0][2].lock,i[0][3].lock),r.add(e[3].fire),a[e[0]]=function(){return a[e[0]+"With"](this===a?void 0:this,arguments),this},a[e[0]+"With"]=r.fireWith}),o.promise(a),e&&e.call(a,a),a},when:function(t){var e=arguments.length,i=e,n=Array(i),o=J.call(arguments),a=mt.Deferred(),r=function(t){return function(i){n[t]=this,o[t]=1<arguments.length?J.call(arguments):i,--e||a.resolveWith(n,o)}};if(e<=1&&(u(t,a.done(r(i)).resolve,a.reject,!e),"pending"===a.state()||ct(o[i]&&o[i].then)))return a.then();for(;i--;)u(o[i],r(i),a.reject);return a.promise()}});var Mt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;mt.Deferred.exceptionHook=function(e,i){t.console&&t.console.warn&&e&&Mt.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,i)},mt.readyException=function(e){t.setTimeout(function(){throw e})};var Lt=mt.Deferred();mt.fn.ready=function(t){return Lt.then(t).catch(function(t){mt.readyException(t)}),this},mt.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--mt.readyWait:mt.isReady)||(mt.isReady=!0)!==t&&0<--mt.readyWait||Lt.resolveWith(ut,[mt])}}),mt.ready.then=Lt.then,"complete"===ut.readyState||"loading"!==ut.readyState&&!ut.documentElement.doScroll?t.setTimeout(mt.ready):(ut.addEventListener("DOMContentLoaded",p),t.addEventListener("load",p));var Nt=function(t,e,i,o,a,r,s){var l=0,c=t.length,d=null==i;if("object"===n(i))for(l in a=!0,i)Nt(t,e,l,i[l],!0,r,s);else if(void 0!==o&&(a=!0,ct(o)||(s=!0),d&&(s?(e.call(t,o),e=null):(d=e,e=function(t,e,i){return d.call(mt(t),i)})),e))for(;l<c;l++)e(t[l],i,s?o:o.call(t[l],l,e(t[l],i)));return a?t:d?e.call(t):c?e(t[0],i):r},Rt=/^-ms-/,Pt=/-([a-z])/g,It=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};m.uid=1,m.prototype={cache:function(t){var e=t[this.expando];return e||(e={},It(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,o=this.cache(t);if("string"==typeof e)o[f(e)]=i;else for(n in e)o[f(n)]=e[n];return o},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][f(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(f):(e=f(e))in n?[e]:e.match(Ot)||[]).length;for(;i--;)delete n[e[i]]}(void 0===e||mt.isEmptyObject(n))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!mt.isEmptyObject(e)}};var jt=new m,Ht=new m,zt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Yt=/[A-Z]/g;mt.extend({hasData:function(t){return Ht.hasData(t)||jt.hasData(t)},data:function(t,e,i){return Ht.access(t,e,i)},removeData:function(t,e){Ht.remove(t,e)},_data:function(t,e,i){return jt.access(t,e,i)},_removeData:function(t,e){jt.remove(t,e)}}),mt.fn.extend({data:function(t,e){var i,n,o,a=this[0],r=a&&a.attributes;if(void 0===t){if(this.length&&(o=Ht.get(a),1===a.nodeType&&!jt.get(a,"hasDataAttrs"))){for(i=r.length;i--;)r[i]&&0===(n=r[i].name).indexOf("data-")&&(n=f(n.slice(5)),g(a,n,o[n]));jt.set(a,"hasDataAttrs",!0)}return o}return"object"==typeof t?this.each(function(){Ht.set(this,t)}):Nt(this,function(e){var i;if(a&&void 0===e)return void 0!==(i=Ht.get(a,t))?i:void 0!==(i=g(a,t))?i:void 0;this.each(function(){Ht.set(this,t,e)})},null,e,1<arguments.length,null,!0)},removeData:function(t){return this.each(function(){Ht.remove(this,t)})}}),mt.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=jt.get(t,e),i&&(!n||Array.isArray(i)?n=jt.access(t,e,mt.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=mt.queue(t,e),n=i.length,o=i.shift(),a=mt._queueHooks(t,e);"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===e&&i.unshift("inprogress"),delete a.stop,o.call(t,function(){mt.dequeue(t,e)},a)),!n&&a&&a.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return jt.get(t,i)||jt.access(t,i,{empty:mt.Callbacks("once memory").add(function(){jt.remove(t,[e+"queue",i])})})}}),mt.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?mt.queue(this[0],t):void 0===e?this:this.each(function(){var i=mt.queue(this,t,e);mt._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&mt.dequeue(this,t)})},dequeue:function(t){return this.each(function(){mt.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,o=mt.Deferred(),a=this,r=this.length,s=function(){--n||o.resolveWith(a,[a])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)(i=jt.get(a[r],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(s));return s(),o.promise(e)}});var Bt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ut=new RegExp("^(?:([+-])=|)("+Bt+")([a-z%]*)$","i"),qt=["Top","Right","Bottom","Left"],Wt=ut.documentElement,Vt=function(t){return mt.contains(t.ownerDocument,t)},Gt={composed:!0};Wt.getRootNode&&(Vt=function(t){return mt.contains(t.ownerDocument,t)||t.getRootNode(Gt)===t.ownerDocument});var Xt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&Vt(t)&&"none"===mt.css(t,"display")},Qt={};mt.fn.extend({show:function(){return y(this,!0)},hide:function(){return y(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){Xt(this)?mt(this).show():mt(this).hide()})}});var Kt,Zt,Jt=/^(?:checkbox|radio)$/i,te=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,ee=/^$|^module$|\/(?:java|ecma)script/i;Kt=ut.createDocumentFragment().appendChild(ut.createElement("div")),(Zt=ut.createElement("input")).setAttribute("type","radio"),Zt.setAttribute("checked","checked"),Zt.setAttribute("name","t"),Kt.appendChild(Zt),lt.checkClone=Kt.cloneNode(!0).cloneNode(!0).lastChild.checked,Kt.innerHTML="<textarea>x</textarea>",lt.noCloneChecked=!!Kt.cloneNode(!0).lastChild.defaultValue,Kt.innerHTML="<option></option>",lt.option=!!Kt.lastChild;var ie={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};ie.tbody=ie.tfoot=ie.colgroup=ie.caption=ie.thead,ie.th=ie.td,lt.option||(ie.optgroup=ie.option=[1,"<select multiple='multiple'>","</select>"]);var ne=/<|&#?\w+;/,oe=/^([^.]*)(?:\.(.+)|)/;mt.event={global:{},add:function(t,e,i,n,o){var a,r,s,l,c,d,u,p,h,f,m,g=jt.get(t);if(It(t))for(i.handler&&(i=(a=i).handler,o=a.selector),o&&mt.find.matchesSelector(Wt,o),i.guid||(i.guid=mt.guid++),(l=g.events)||(l=g.events=Object.create(null)),(r=g.handle)||(r=g.handle=function(e){return void 0!==mt&&mt.event.triggered!==e.type?mt.event.dispatch.apply(t,arguments):void 0}),c=(e=(e||"").match(Ot)||[""]).length;c--;)h=m=(s=oe.exec(e[c])||[])[1],f=(s[2]||"").split(".").sort(),h&&(u=mt.event.special[h]||{},h=(o?u.delegateType:u.bindType)||h,u=mt.event.special[h]||{},d=mt.extend({type:h,origType:m,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&mt.expr.match.needsContext.test(o),namespace:f.join(".")},a),(p=l[h])||((p=l[h]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,n,f,r)||t.addEventListener&&t.addEventListener(h,r)),u.add&&(u.add.call(t,d),d.handler.guid||(d.handler.guid=i.guid)),o?p.splice(p.delegateCount++,0,d):p.push(d),mt.event.global[h]=!0)},remove:function(t,e,i,n,o){var a,r,s,l,c,d,u,p,h,f,m,g=jt.hasData(t)&&jt.get(t);if(g&&(l=g.events)){for(c=(e=(e||"").match(Ot)||[""]).length;c--;)if(h=m=(s=oe.exec(e[c])||[])[1],f=(s[2]||"").split(".").sort(),h){for(u=mt.event.special[h]||{},p=l[h=(n?u.delegateType:u.bindType)||h]||[],s=s[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),r=a=p.length;a--;)d=p[a],!o&&m!==d.origType||i&&i.guid!==d.guid||s&&!s.test(d.namespace)||n&&n!==d.selector&&("**"!==n||!d.selector)||(p.splice(a,1),d.selector&&p.delegateCount--,u.remove&&u.remove.call(t,d));r&&!p.length&&(u.teardown&&!1!==u.teardown.call(t,f,g.handle)||mt.removeEvent(t,h,g.handle),delete l[h])}else for(h in l)mt.event.remove(t,h+e[c],i,n,!0);mt.isEmptyObject(l)&&jt.remove(t,"handle events")}},dispatch:function(t){var e,i,n,o,a,r,s=new Array(arguments.length),l=mt.event.fix(t),c=(jt.get(this,"events")||Object.create(null))[l.type]||[],d=mt.event.special[l.type]||{};for(s[0]=l,e=1;e<arguments.length;e++)s[e]=arguments[e];if(l.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,l)){for(r=mt.event.handlers.call(this,l,c),e=0;(o=r[e++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,i=0;(a=o.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==a.namespace&&!l.rnamespace.test(a.namespace)||(l.handleObj=a,l.data=a.data,void 0!==(n=((mt.event.special[a.origType]||{}).handle||a.handler).apply(o.elem,s))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,l),l.result}},handlers:function(t,e){var i,n,o,a,r,s=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&1<=t.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(a=[],r={},i=0;i<l;i++)void 0===r[o=(n=e[i]).selector+" "]&&(r[o]=n.needsContext?-1<mt(o,this).index(c):mt.find(o,this,null,[c]).length),r[o]&&a.push(n);a.length&&s.push({elem:c,handlers:a})}return c=this,l<e.length&&s.push({elem:c,handlers:e.slice(l)}),s},addProp:function(t,e){Object.defineProperty(mt.Event.prototype,t,{enumerable:!0,configurable:!0,get:ct(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[mt.expando]?t:new mt.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click",!0),!1},trigger:function(t){var e=this||t;return Jt.test(e.type)&&e.click&&a(e,"input")&&S(e,"click"),!0},_default:function(t){var e=t.target;return Jt.test(e.type)&&e.click&&a(e,"input")&&jt.get(e,"click")||a(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},mt.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},mt.Event=function(t,e){if(!(this instanceof mt.Event))return new mt.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?_:k,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&mt.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[mt.expando]=!0},mt.Event.prototype={constructor:mt.Event,isDefaultPrevented:k,isPropagationStopped:k,isImmediatePropagationStopped:k,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=_,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=_,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=_,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},mt.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},mt.event.addProp),mt.each({focus:"focusin",blur:"focusout"},function(t,e){function i(t){if(ut.documentMode){var i=jt.get(this,"handle"),n=mt.event.fix(t);n.type="focusin"===t.type?"focus":"blur",n.isSimulated=!0,i(t),n.target===n.currentTarget&&i(n)}else mt.event.simulate(e,t.target,mt.event.fix(t))}mt.event.special[t]={setup:function(){var n;if(S(this,t,!0),!ut.documentMode)return!1;(n=jt.get(this,e))||this.addEventListener(e,i),jt.set(this,e,(n||0)+1)},trigger:function(){return S(this,t),!0},teardown:function(){var t;if(!ut.documentMode)return!1;(t=jt.get(this,e)-1)?jt.set(this,e,t):(this.removeEventListener(e,i),jt.remove(this,e))},_default:function(e){return jt.get(e.target,t)},delegateType:e},mt.event.special[e]={setup:function(){var n=this.ownerDocument||this.document||this,o=ut.documentMode?this:n,a=jt.get(o,e);a||(ut.documentMode?this.addEventListener(e,i):n.addEventListener(t,i,!0)),jt.set(o,e,(a||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,o=ut.documentMode?this:n,a=jt.get(o,e)-1;a?jt.set(o,e,a):(ut.documentMode?this.removeEventListener(e,i):n.removeEventListener(t,i,!0),jt.remove(o,e))}}}),mt.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){mt.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,o=t.handleObj;return n&&(n===this||mt.contains(this,n))||(t.type=o.origType,i=o.handler.apply(this,arguments),t.type=e),i}}}),mt.fn.extend({on:function(t,e,i,n){return C(this,t,e,i,n)},one:function(t,e,i,n){return C(this,t,e,i,n,1)},off:function(t,e,i){var n,o;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,mt(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(o in t)this.off(o,e,t[o]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=k),this.each(function(){mt.event.remove(this,t,i,e)})}});var ae=/<script|<style|<link/i,re=/checked\s*(?:[^=]|=\s*.checked.)/i,se=/^\s*<!\[CDATA\[|\]\]>\s*$/g;mt.extend({htmlPrefilter:function(t){return t},clone:function(t,e,i){var n,o,a,r,s,l,c,d=t.cloneNode(!0),u=Vt(t);if(!(lt.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||mt.isXMLDoc(t)))for(r=b(d),n=0,o=(a=b(t)).length;n<o;n++)s=a[n],l=r[n],"input"===(c=l.nodeName.toLowerCase())&&Jt.test(s.type)?l.checked=s.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=s.defaultValue);if(e)if(i)for(a=a||b(t),r=r||b(d),n=0,o=a.length;n<o;n++)E(a[n],r[n]);else E(t,d);return 0<(r=b(d,"script")).length&&x(r,!u&&b(t,"script")),d},cleanData:function(t){for(var e,i,n,o=mt.event.special,a=0;void 0!==(i=t[a]);a++)if(It(i)){if(e=i[jt.expando]){if(e.events)for(n in e.events)o[n]?mt.event.remove(i,n):mt.removeEvent(i,n,e.handle);i[jt.expando]=void 0}i[Ht.expando]&&(i[Ht.expando]=void 0)}}}),mt.fn.extend({detach:function(t){return A(this,t,!0)},remove:function(t){return A(this,t)},text:function(t){return Nt(this,function(t){return void 0===t?mt.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return F(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||T(this,t).appendChild(t)})},prepend:function(){return F(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=T(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return F(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return F(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(mt.cleanData(b(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return mt.clone(this,t,e)})},html:function(t){return Nt(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!ae.test(t)&&!ie[(te.exec(t)||["",""])[1].toLowerCase()]){t=mt.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(mt.cleanData(b(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return F(this,arguments,function(e){var i=this.parentNode;mt.inArray(this,t)<0&&(mt.cleanData(b(this)),i&&i.replaceChild(e,this))},t)}}),mt.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){mt.fn[t]=function(t){for(var i,n=[],o=mt(t),a=o.length-1,r=0;r<=a;r++)i=r===a?this:this.clone(!0),mt(o[r])[e](i),et.apply(n,i.get());return this.pushStack(n)}});var le=new RegExp("^("+Bt+")(?!px)[a-z%]+$","i"),ce=/^--/,de=function(e){var i=e.ownerDocument.defaultView;return i&&i.opener||(i=t),i.getComputedStyle(e)},ue=function(t,e,i){var n,o,a={};for(o in e)a[o]=t.style[o],t.style[o]=e[o];for(o in n=i.call(t),e)t.style[o]=a[o];return n},pe=new RegExp(qt.join("|"),"i");!function(){function e(){if(d){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",d.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Wt.appendChild(c).appendChild(d);var e=t.getComputedStyle(d);n="1%"!==e.top,l=12===i(e.marginLeft),d.style.right="60%",r=36===i(e.right),o=36===i(e.width),d.style.position="absolute",a=12===i(d.offsetWidth/3),Wt.removeChild(c),d=null}}function i(t){return Math.round(parseFloat(t))}var n,o,a,r,s,l,c=ut.createElement("div"),d=ut.createElement("div");d.style&&(d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",lt.clearCloneStyle="content-box"===d.style.backgroundClip,mt.extend(lt,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var e,i,n,o;return null==s&&(e=ut.createElement("table"),i=ut.createElement("tr"),n=ut.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",i.style.cssText="box-sizing:content-box;border:1px solid",i.style.height="1px",n.style.height="9px",n.style.display="block",Wt.appendChild(e).appendChild(i).appendChild(n),o=t.getComputedStyle(i),s=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===i.offsetHeight,Wt.removeChild(e)),s}}))}();var he=["Webkit","Moz","ms"],fe=ut.createElement("div").style,me={},ge=/^(none|table(?!-c[ea]).+)/,ve={position:"absolute",visibility:"hidden",display:"block"},ye={letterSpacing:"0",fontWeight:"400"};mt.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=O(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var o,a,r,s=f(e),l=ce.test(e),c=t.style;if(l||(e=L(s)),r=mt.cssHooks[e]||mt.cssHooks[s],void 0===i)return r&&"get"in r&&void 0!==(o=r.get(t,!1,n))?o:c[e];"string"==(a=typeof i)&&(o=Ut.exec(i))&&o[1]&&(i=v(t,e,o),a="number"),null!=i&&i==i&&("number"!==a||l||(i+=o&&o[3]||(mt.cssNumber[s]?"":"px")),lt.clearCloneStyle||""!==i||0!==e.indexOf("background")||(c[e]="inherit"),r&&"set"in r&&void 0===(i=r.set(t,i,n))||(l?c.setProperty(e,i):c[e]=i))}},css:function(t,e,i,n){var o,a,r,s=f(e);return ce.test(e)||(e=L(s)),(r=mt.cssHooks[e]||mt.cssHooks[s])&&"get"in r&&(o=r.get(t,!0,i)),void 0===o&&(o=O(t,e,n)),"normal"===o&&e in ye&&(o=ye[e]),""===i||i?(a=parseFloat(o),!0===i||isFinite(a)?a||0:o):o}}),mt.each(["height","width"],function(t,e){mt.cssHooks[e]={get:function(t,i,n){if(i)return!ge.test(mt.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?P(t,e,n):ue(t,ve,function(){return P(t,e,n)})},set:function(t,i,n){var o,a=de(t),r=!lt.scrollboxSize()&&"absolute"===a.position,s=(r||n)&&"border-box"===mt.css(t,"boxSizing",!1,a),l=n?R(t,e,n,s,a):0;return s&&r&&(l-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(a[e])-R(t,e,"border",!1,a)-.5)),l&&(o=Ut.exec(i))&&"px"!==(o[3]||"px")&&(t.style[e]=i,i=mt.css(t,e)),N(0,i,l)}}}),mt.cssHooks.marginLeft=M(lt.reliableMarginLeft,function(t,e){if(e)return(parseFloat(O(t,"marginLeft"))||t.getBoundingClientRect().left-ue(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),mt.each({margin:"",padding:"",border:"Width"},function(t,e){mt.cssHooks[t+e]={expand:function(i){for(var n=0,o={},a="string"==typeof i?i.split(" "):[i];n<4;n++)o[t+qt[n]+e]=a[n]||a[n-2]||a[0];return o}},"margin"!==t&&(mt.cssHooks[t+e].set=N)}),mt.fn.extend({css:function(t,e){return Nt(this,function(t,e,i){var n,o,a={},r=0;if(Array.isArray(e)){for(n=de(t),o=e.length;r<o;r++)a[e[r]]=mt.css(t,e[r],!1,n);return a}return void 0!==i?mt.style(t,e,i):mt.css(t,e)},t,e,1<arguments.length)}}),((mt.Tween=I).prototype={constructor:I,init:function(t,e,i,n,o,a){this.elem=t,this.prop=i,this.easing=o||mt.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=a||(mt.cssNumber[i]?"":"px")},cur:function(){var t=I.propHooks[this.prop];return t&&t.get?t.get(this):I.propHooks._default.get(this)},run:function(t){var e,i=I.propHooks[this.prop];return this.options.duration?this.pos=e=mt.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):I.propHooks._default.set(this),this}}).init.prototype=I.prototype,(I.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=mt.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){mt.fx.step[t.prop]?mt.fx.step[t.prop](t):1!==t.elem.nodeType||!mt.cssHooks[t.prop]&&null==t.elem.style[L(t.prop)]?t.elem[t.prop]=t.now:mt.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=I.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},mt.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},mt.fx=I.prototype.init,mt.fx.step={};var be,xe,we,_e,ke=/^(?:toggle|show|hide)$/,Ce=/queueHooks$/;mt.Animation=mt.extend(B,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return v(i.elem,t,Ut.exec(e),i),i}]},tweener:function(t,e){ct(t)?(e=t,t=["*"]):t=t.match(Ot);for(var i,n=0,o=t.length;n<o;n++)i=t[n],B.tweeners[i]=B.tweeners[i]||[],B.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,o,a,r,s,l,c,d,u="width"in e||"height"in e,p=this,h={},f=t.style,m=t.nodeType&&Xt(t),g=jt.get(t,"fxshow");for(n in i.queue||(null==(r=mt._queueHooks(t,"fx")).unqueued&&(r.unqueued=0,s=r.empty.fire,r.empty.fire=function(){r.unqueued||s()}),r.unqueued++,p.always(function(){p.always(function(){r.unqueued--,mt.queue(t,"fx").length||r.empty.fire()})})),e)if(o=e[n],ke.test(o)){if(delete e[n],a=a||"toggle"===o,o===(m?"hide":"show")){if("show"!==o||!g||void 0===g[n])continue;m=!0}h[n]=g&&g[n]||mt.style(t,n)}if((l=!mt.isEmptyObject(e))||!mt.isEmptyObject(h))for(n in u&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=jt.get(t,"display")),"none"===(d=mt.css(t,"display"))&&(c?d=c:(y([t],!0),c=t.style.display||c,d=mt.css(t,"display"),y([t]))),("inline"===d||"inline-block"===d&&null!=c)&&"none"===mt.css(t,"float")&&(l||(p.done(function(){f.display=c}),null==c&&(d=f.display,c="none"===d?"":d)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",p.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,h)l||(g?"hidden"in g&&(m=g.hidden):g=jt.access(t,"fxshow",{display:c}),a&&(g.hidden=!m),m&&y([t],!0),p.done(function(){for(n in m||y([t]),jt.remove(t,"fxshow"),h)mt.style(t,n,h[n])})),l=Y(m?g[n]:0,n,p),n in g||(g[n]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?B.prefilters.unshift(t):B.prefilters.push(t)}}),mt.speed=function(t,e,i){var n=t&&"object"==typeof t?mt.extend({},t):{complete:i||!i&&e||ct(t)&&t,duration:t,easing:i&&e||e&&!ct(e)&&e};return mt.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in mt.fx.speeds?n.duration=mt.fx.speeds[n.duration]:n.duration=mt.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){ct(n.old)&&n.old.call(this),n.queue&&mt.dequeue(this,n.queue)},n},mt.fn.extend({fadeTo:function(t,e,i,n){return this.filter(Xt).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var o=mt.isEmptyObject(t),a=mt.speed(e,i,n),r=function(){var e=B(this,mt.extend({},t),a);(o||jt.get(this,"finish"))&&e.stop(!0)};return r.finish=r,o||!1===a.queue?this.each(r):this.queue(a.queue,r)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each(function(){var e=!0,o=null!=t&&t+"queueHooks",a=mt.timers,r=jt.get(this);if(o)r[o]&&r[o].stop&&n(r[o]);else for(o in r)r[o]&&r[o].stop&&Ce.test(o)&&n(r[o]);for(o=a.length;o--;)a[o].elem!==this||null!=t&&a[o].queue!==t||(a[o].anim.stop(i),e=!1,a.splice(o,1));!e&&i||mt.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,i=jt.get(this),n=i[t+"queue"],o=i[t+"queueHooks"],a=mt.timers,r=n?n.length:0;for(i.finish=!0,mt.queue(this,t,[]),o&&o.stop&&o.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),mt.each(["toggle","show","hide"],function(t,e){var i=mt.fn[e];mt.fn[e]=function(t,n,o){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(z(e,!0),t,n,o)}}),mt.each({slideDown:z("show"),slideUp:z("hide"),slideToggle:z("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){mt.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),mt.timers=[],mt.fx.tick=function(){var t,e=0,i=mt.timers;for(be=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||mt.fx.stop(),be=void 0},mt.fx.timer=function(t){mt.timers.push(t),mt.fx.start()},mt.fx.interval=13,mt.fx.start=function(){xe||(xe=!0,j())},mt.fx.stop=function(){xe=null},mt.fx.speeds={slow:600,fast:200,_default:400},mt.fn.delay=function(e,i){return e=mt.fx&&mt.fx.speeds[e]||e,i=i||"fx",
this.queue(i,function(i,n){var o=t.setTimeout(i,e);n.stop=function(){t.clearTimeout(o)}})},we=ut.createElement("input"),_e=ut.createElement("select").appendChild(ut.createElement("option")),we.type="checkbox",lt.checkOn=""!==we.value,lt.optSelected=_e.selected,(we=ut.createElement("input")).value="t",we.type="radio",lt.radioValue="t"===we.value;var Se,Te=mt.expr.attrHandle;mt.fn.extend({attr:function(t,e){return Nt(this,mt.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each(function(){mt.removeAttr(this,t)})}}),mt.extend({attr:function(t,e,i){var n,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return void 0===t.getAttribute?mt.prop(t,e,i):(1===a&&mt.isXMLDoc(t)||(o=mt.attrHooks[e.toLowerCase()]||(mt.expr.match.bool.test(e)?Se:void 0)),void 0!==i?null===i?void mt.removeAttr(t,e):o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:(t.setAttribute(e,i+""),i):o&&"get"in o&&null!==(n=o.get(t,e))?n:null==(n=mt.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!lt.radioValue&&"radio"===e&&a(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,o=e&&e.match(Ot);if(o&&1===t.nodeType)for(;i=o[n++];)t.removeAttribute(i)}}),Se={set:function(t,e,i){return!1===e?mt.removeAttr(t,i):t.setAttribute(i,i),i}},mt.each(mt.expr.match.bool.source.match(/\w+/g),function(t,e){var i=Te[e]||mt.find.attr;Te[e]=function(t,e,n){var o,a,r=e.toLowerCase();return n||(a=Te[r],Te[r]=o,o=null!=i(t,e,n)?r:null,Te[r]=a),o}});var De=/^(?:input|select|textarea|button)$/i,$e=/^(?:a|area)$/i;mt.fn.extend({prop:function(t,e){return Nt(this,mt.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each(function(){delete this[mt.propFix[t]||t]})}}),mt.extend({prop:function(t,e,i){var n,o,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return 1===a&&mt.isXMLDoc(t)||(e=mt.propFix[e]||e,o=mt.propHooks[e]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(t,i,e))?n:t[e]=i:o&&"get"in o&&null!==(n=o.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=mt.find.attr(t,"tabindex");return e?parseInt(e,10):De.test(t.nodeName)||$e.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),lt.optSelected||(mt.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),mt.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){mt.propFix[this.toLowerCase()]=this}),mt.fn.extend({addClass:function(t){var e,i,n,o,a,r;return ct(t)?this.each(function(e){mt(this).addClass(t.call(this,e,q(this)))}):(e=W(t)).length?this.each(function(){if(n=q(this),i=1===this.nodeType&&" "+U(n)+" "){for(a=0;a<e.length;a++)o=e[a],i.indexOf(" "+o+" ")<0&&(i+=o+" ");r=U(i),n!==r&&this.setAttribute("class",r)}}):this},removeClass:function(t){var e,i,n,o,a,r;return ct(t)?this.each(function(e){mt(this).removeClass(t.call(this,e,q(this)))}):arguments.length?(e=W(t)).length?this.each(function(){if(n=q(this),i=1===this.nodeType&&" "+U(n)+" "){for(a=0;a<e.length;a++)for(o=e[a];-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");r=U(i),n!==r&&this.setAttribute("class",r)}}):this:this.attr("class","")},toggleClass:function(t,e){var i,n,o,a,r=typeof t,s="string"===r||Array.isArray(t);return ct(t)?this.each(function(i){mt(this).toggleClass(t.call(this,i,q(this),e),e)}):"boolean"==typeof e&&s?e?this.addClass(t):this.removeClass(t):(i=W(t),this.each(function(){if(s)for(a=mt(this),o=0;o<i.length;o++)n=i[o],a.hasClass(n)?a.removeClass(n):a.addClass(n);else void 0!==t&&"boolean"!==r||((n=q(this))&&jt.set(this,"__className__",n),this.setAttribute&&this.setAttribute("class",n||!1===t?"":jt.get(this,"__className__")||""))}))},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&-1<(" "+U(q(i))+" ").indexOf(e))return!0;return!1}});var Ee=/\r/g;mt.fn.extend({val:function(t){var e,i,n,o=this[0];return arguments.length?(n=ct(t),this.each(function(i){var o;1===this.nodeType&&(null==(o=n?t.call(this,i,mt(this).val()):t)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=mt.map(o,function(t){return null==t?"":t+""})),(e=mt.valHooks[this.type]||mt.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,o,"value")||(this.value=o))})):o?(e=mt.valHooks[o.type]||mt.valHooks[o.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(o,"value"))?i:"string"==typeof(i=o.value)?i.replace(Ee,""):null==i?"":i:void 0}}),mt.extend({valHooks:{option:{get:function(t){var e=mt.find.attr(t,"value");return null!=e?e:U(mt.text(t))}},select:{get:function(t){var e,i,n,o=t.options,r=t.selectedIndex,s="select-one"===t.type,l=s?null:[],c=s?r+1:o.length;for(n=r<0?c:s?r:0;n<c;n++)if(((i=o[n]).selected||n===r)&&!i.disabled&&(!i.parentNode.disabled||!a(i.parentNode,"optgroup"))){if(e=mt(i).val(),s)return e;l.push(e)}return l},set:function(t,e){for(var i,n,o=t.options,a=mt.makeArray(e),r=o.length;r--;)((n=o[r]).selected=-1<mt.inArray(mt.valHooks.option.get(n),a))&&(i=!0);return i||(t.selectedIndex=-1),a}}}}),mt.each(["radio","checkbox"],function(){mt.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=-1<mt.inArray(mt(t).val(),e)}},lt.checkOn||(mt.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var Fe=t.location,Ae={guid:Date.now()},Oe=/\?/;mt.parseXML=function(e){var i,n;if(!e||"string"!=typeof e)return null;try{i=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=i&&i.getElementsByTagName("parsererror")[0],i&&!n||mt.error("Invalid XML: "+(n?mt.map(n.childNodes,function(t){return t.textContent}).join("\n"):e)),i};var Me=/^(?:focusinfocus|focusoutblur)$/,Le=function(t){t.stopPropagation()};mt.extend(mt.event,{trigger:function(e,i,n,o){var a,r,s,l,c,d,u,p,h=[n||ut],f=at.call(e,"type")?e.type:e,m=at.call(e,"namespace")?e.namespace.split("."):[];if(r=p=s=n=n||ut,3!==n.nodeType&&8!==n.nodeType&&!Me.test(f+mt.event.triggered)&&(-1<f.indexOf(".")&&(f=(m=f.split(".")).shift(),m.sort()),c=f.indexOf(":")<0&&"on"+f,(e=e[mt.expando]?e:new mt.Event(f,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),i=null==i?[e]:mt.makeArray(i,[e]),u=mt.event.special[f]||{},o||!u.trigger||!1!==u.trigger.apply(n,i))){if(!o&&!u.noBubble&&!dt(n)){for(l=u.delegateType||f,Me.test(l+f)||(r=r.parentNode);r;r=r.parentNode)h.push(r),s=r;s===(n.ownerDocument||ut)&&h.push(s.defaultView||s.parentWindow||t)}for(a=0;(r=h[a++])&&!e.isPropagationStopped();)p=r,e.type=1<a?l:u.bindType||f,(d=(jt.get(r,"events")||Object.create(null))[e.type]&&jt.get(r,"handle"))&&d.apply(r,i),(d=c&&r[c])&&d.apply&&It(r)&&(e.result=d.apply(r,i),!1===e.result&&e.preventDefault());return e.type=f,o||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(h.pop(),i)||!It(n)||c&&ct(n[f])&&!dt(n)&&((s=n[c])&&(n[c]=null),mt.event.triggered=f,e.isPropagationStopped()&&p.addEventListener(f,Le),n[f](),e.isPropagationStopped()&&p.removeEventListener(f,Le),mt.event.triggered=void 0,s&&(n[c]=s)),e.result}},simulate:function(t,e,i){var n=mt.extend(new mt.Event,i,{type:t,isSimulated:!0});mt.event.trigger(n,null,e)}}),mt.fn.extend({trigger:function(t,e){return this.each(function(){mt.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return mt.event.trigger(t,e,i,!0)}});var Ne=/\[\]$/,Re=/\r?\n/g,Pe=/^(?:submit|button|image|reset|file)$/i,Ie=/^(?:input|select|textarea|keygen)/i;mt.param=function(t,e){var i,n=[],o=function(t,e){var i=ct(e)?e():e;n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!mt.isPlainObject(t))mt.each(t,function(){o(this.name,this.value)});else for(i in t)V(i,t[i],e,o);return n.join("&")},mt.fn.extend({serialize:function(){return mt.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=mt.prop(this,"elements");return t?mt.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!mt(this).is(":disabled")&&Ie.test(this.nodeName)&&!Pe.test(t)&&(this.checked||!Jt.test(t))}).map(function(t,e){var i=mt(this).val();return null==i?null:Array.isArray(i)?mt.map(i,function(t){return{name:e.name,value:t.replace(Re,"\r\n")}}):{name:e.name,value:i.replace(Re,"\r\n")}}).get()}});var je=/%20/g,He=/#.*$/,ze=/([?&])_=[^&]*/,Ye=/^(.*?):[ \t]*([^\r\n]*)$/gm,Be=/^(?:GET|HEAD)$/,Ue=/^\/\//,qe={},We={},Ve="*/".concat("*"),Ge=ut.createElement("a");Ge.href=Fe.href,mt.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Fe.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Fe.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ve,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":mt.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Q(Q(t,mt.ajaxSettings),e):Q(mt.ajaxSettings,t)},ajaxPrefilter:G(qe),ajaxTransport:G(We),ajax:function(e,i){function n(e,i,n,s){var c,p,h,x,w,_=i;d||(d=!0,l&&t.clearTimeout(l),o=void 0,r=s||"",k.readyState=0<e?4:0,c=200<=e&&e<300||304===e,n&&(x=function(t,e,i){for(var n,o,a,r,s=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(o in s)if(s[o]&&s[o].test(n)){l.unshift(o);break}if(l[0]in i)a=l[0];else{for(o in i){if(!l[0]||t.converters[o+" "+l[0]]){a=o;break}r||(r=o)}a=a||r}if(a)return a!==l[0]&&l.unshift(a),i[a]}(f,k,n)),!c&&-1<mt.inArray("script",f.dataTypes)&&mt.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(t,e,i,n){var o,a,r,s,l,c={},d=t.dataTypes.slice();if(d[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(a=d.shift();a;)if(t.responseFields[a]&&(i[t.responseFields[a]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=d.shift())if("*"===a)a=l;else if("*"!==l&&l!==a){if(!(r=c[l+" "+a]||c["* "+a]))for(o in c)if((s=o.split(" "))[1]===a&&(r=c[l+" "+s[0]]||c["* "+s[0]])){!0===r?r=c[o]:!0!==c[o]&&(a=s[0],d.unshift(s[1]));break}if(!0!==r)if(r&&t.throws)e=r(e);else try{e=r(e)}catch(t){return{state:"parsererror",error:r?t:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}(f,x,k,c),c?(f.ifModified&&((w=k.getResponseHeader("Last-Modified"))&&(mt.lastModified[a]=w),(w=k.getResponseHeader("etag"))&&(mt.etag[a]=w)),204===e||"HEAD"===f.type?_="nocontent":304===e?_="notmodified":(_=x.state,p=x.data,c=!(h=x.error))):(h=_,!e&&_||(_="error",e<0&&(e=0))),k.status=e,k.statusText=(i||_)+"",c?v.resolveWith(m,[p,_,k]):v.rejectWith(m,[k,_,h]),k.statusCode(b),b=void 0,u&&g.trigger(c?"ajaxSuccess":"ajaxError",[k,f,c?p:h]),y.fireWith(m,[k,_]),u&&(g.trigger("ajaxComplete",[k,f]),--mt.active||mt.event.trigger("ajaxStop")))}"object"==typeof e&&(i=e,e=void 0),i=i||{};var o,a,r,s,l,c,d,u,p,h,f=mt.ajaxSetup({},i),m=f.context||f,g=f.context&&(m.nodeType||m.jquery)?mt(m):mt.event,v=mt.Deferred(),y=mt.Callbacks("once memory"),b=f.statusCode||{},x={},w={},_="canceled",k={readyState:0,getResponseHeader:function(t){var e;if(d){if(!s)for(s={};e=Ye.exec(r);)s[e[1].toLowerCase()+" "]=(s[e[1].toLowerCase()+" "]||[]).concat(e[2]);e=s[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return d?r:null},setRequestHeader:function(t,e){return null==d&&(t=w[t.toLowerCase()]=w[t.toLowerCase()]||t,x[t]=e),this},overrideMimeType:function(t){return null==d&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(d)k.always(t[k.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||_;return o&&o.abort(e),n(0,e),this}};if(v.promise(k),f.url=((e||f.url||Fe.href)+"").replace(Ue,Fe.protocol+"//"),f.type=i.method||i.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(Ot)||[""],null==f.crossDomain){c=ut.createElement("a");try{c.href=f.url,c.href=c.href,f.crossDomain=Ge.protocol+"//"+Ge.host!=c.protocol+"//"+c.host}catch(e){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=mt.param(f.data,f.traditional)),X(qe,f,i,k),d)return k;for(p in(u=mt.event&&f.global)&&0==mt.active++&&mt.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Be.test(f.type),a=f.url.replace(He,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(je,"+")):(h=f.url.slice(a.length),f.data&&(f.processData||"string"==typeof f.data)&&(a+=(Oe.test(a)?"&":"?")+f.data,delete f.data),!1===f.cache&&(a=a.replace(ze,"$1"),h=(Oe.test(a)?"&":"?")+"_="+Ae.guid+++h),f.url=a+h),f.ifModified&&(mt.lastModified[a]&&k.setRequestHeader("If-Modified-Since",mt.lastModified[a]),mt.etag[a]&&k.setRequestHeader("If-None-Match",mt.etag[a])),(f.data&&f.hasContent&&!1!==f.contentType||i.contentType)&&k.setRequestHeader("Content-Type",f.contentType),k.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Ve+"; q=0.01":""):f.accepts["*"]),f.headers)k.setRequestHeader(p,f.headers[p]);if(f.beforeSend&&(!1===f.beforeSend.call(m,k,f)||d))return k.abort();if(_="abort",y.add(f.complete),k.done(f.success),k.fail(f.error),o=X(We,f,i,k)){if(k.readyState=1,u&&g.trigger("ajaxSend",[k,f]),d)return k;f.async&&0<f.timeout&&(l=t.setTimeout(function(){k.abort("timeout")},f.timeout));try{d=!1,o.send(x,n)}catch(e){if(d)throw e;n(-1,e)}}else n(-1,"No Transport");return k},getJSON:function(t,e,i){return mt.get(t,e,i,"json")},getScript:function(t,e){return mt.get(t,void 0,e,"script")}}),mt.each(["get","post"],function(t,e){mt[e]=function(t,i,n,o){return ct(i)&&(o=o||n,n=i,i=void 0),mt.ajax(mt.extend({url:t,type:e,dataType:o,data:i,success:n},mt.isPlainObject(t)&&t))}}),mt.ajaxPrefilter(function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")}),mt._evalUrl=function(t,e,i){return mt.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){mt.globalEval(t,e,i)}})},mt.fn.extend({wrapAll:function(t){var e;return this[0]&&(ct(t)&&(t=t.call(this[0])),e=mt(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return ct(t)?this.each(function(e){mt(this).wrapInner(t.call(this,e))}):this.each(function(){var e=mt(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=ct(t);return this.each(function(i){mt(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){mt(this).replaceWith(this.childNodes)}),this}}),mt.expr.pseudos.hidden=function(t){return!mt.expr.pseudos.visible(t)},mt.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},mt.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Xe={0:200,1223:204},Qe=mt.ajaxSettings.xhr();lt.cors=!!Qe&&"withCredentials"in Qe,lt.ajax=Qe=!!Qe,mt.ajaxTransport(function(e){var i,n;if(lt.cors||Qe&&!e.crossDomain)return{send:function(o,a){var r,s=e.xhr();if(s.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)s[r]=e.xhrFields[r];for(r in e.mimeType&&s.overrideMimeType&&s.overrideMimeType(e.mimeType),e.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)s.setRequestHeader(r,o[r]);i=function(t){return function(){i&&(i=n=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===t?s.abort():"error"===t?"number"!=typeof s.status?a(0,"error"):a(s.status,s.statusText):a(Xe[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!=typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=i(),n=s.onerror=s.ontimeout=i("error"),void 0!==s.onabort?s.onabort=n:s.onreadystatechange=function(){4===s.readyState&&t.setTimeout(function(){i&&n()})},i=i("abort");try{s.send(e.hasContent&&e.data||null)}catch(o){if(i)throw o}},abort:function(){i&&i()}}}),mt.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),mt.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return mt.globalEval(t),t}}}),mt.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),mt.ajaxTransport("script",function(t){var e,i;if(t.crossDomain||t.scriptAttrs)return{send:function(n,o){e=mt("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&o("error"===t.type?404:200,t.type)}),ut.head.appendChild(e[0])},abort:function(){i&&i()}}});var Ke,Ze=[],Je=/(=)\?(?=&|$)|\?\?/;mt.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Ze.pop()||mt.expando+"_"+Ae.guid++;return this[t]=!0,t}}),mt.ajaxPrefilter("json jsonp",function(e,i,n){var o,a,r,s=!1!==e.jsonp&&(Je.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Je.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=ct(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Je,"$1"+o):!1!==e.jsonp&&(e.url+=(Oe.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return r||mt.error(o+" was not called"),r[0]},e.dataTypes[0]="json",a=t[o],t[o]=function(){r=arguments},n.always(function(){void 0===a?mt(t).removeProp(o):t[o]=a,e[o]&&(e.jsonpCallback=i.jsonpCallback,Ze.push(o)),r&&ct(a)&&a(r[0]),r=a=void 0}),"script"}),lt.createHTMLDocument=((Ke=ut.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ke.childNodes.length),mt.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(lt.createHTMLDocument?((n=(e=ut.implementation.createHTMLDocument("")).createElement("base")).href=ut.location.href,e.head.appendChild(n)):e=ut),a=!i&&[],(o=Dt.exec(t))?[e.createElement(o[1])]:(o=w([t],e,a),a&&a.length&&mt(a).remove(),mt.merge([],o.childNodes)));var n,o,a},mt.fn.load=function(t,e,i){var n,o,a,r=this,s=t.indexOf(" ");return-1<s&&(n=U(t.slice(s)),t=t.slice(0,s)),ct(e)?(i=e,e=void 0):e&&"object"==typeof e&&(o="POST"),0<r.length&&mt.ajax({url:t,type:o||"GET",dataType:"html",data:e}).done(function(t){a=arguments,r.html(n?mt("<div>").append(mt.parseHTML(t)).find(n):t)}).always(i&&function(t,e){r.each(function(){i.apply(this,a||[t.responseText,e,t])})}),this},mt.expr.pseudos.animated=function(t){return mt.grep(mt.timers,function(e){return t===e.elem}).length},mt.offset={setOffset:function(t,e,i){var n,o,a,r,s,l,c=mt.css(t,"position"),d=mt(t),u={};"static"===c&&(t.style.position="relative"),s=d.offset(),a=mt.css(t,"top"),l=mt.css(t,"left"),("absolute"===c||"fixed"===c)&&-1<(a+l).indexOf("auto")?(r=(n=d.position()).top,o=n.left):(r=parseFloat(a)||0,o=parseFloat(l)||0),ct(e)&&(e=e.call(t,i,mt.extend({},s))),null!=e.top&&(u.top=e.top-s.top+r),null!=e.left&&(u.left=e.left-s.left+o),"using"in e?e.using.call(t,u):d.css(u)}},mt.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){mt.offset.setOffset(this,t,e)});var e,i,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],o={top:0,left:0};if("fixed"===mt.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===mt.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((o=mt(t).offset()).top+=mt.css(t,"borderTopWidth",!0),o.left+=mt.css(t,"borderLeftWidth",!0))}return{top:e.top-o.top-mt.css(n,"marginTop",!0),left:e.left-o.left-mt.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===mt.css(t,"position");)t=t.offsetParent;return t||Wt})}}),mt.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i="pageYOffset"===e;mt.fn[t]=function(n){return Nt(this,function(t,n,o){var a;if(dt(t)?a=t:9===t.nodeType&&(a=t.defaultView),void 0===o)return a?a[e]:t[n];a?a.scrollTo(i?a.pageXOffset:o,i?o:a.pageYOffset):t[n]=o},t,n,arguments.length)}}),mt.each(["top","left"],function(t,e){mt.cssHooks[e]=M(lt.pixelPosition,function(t,i){if(i)return i=O(t,e),le.test(i)?mt(t).position()[e]+"px":i})}),mt.each({Height:"height",Width:"width"},function(t,e){mt.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){mt.fn[n]=function(o,a){var r=arguments.length&&(i||"boolean"!=typeof o),s=i||(!0===o||!0===a?"margin":"border");return Nt(this,function(e,i,o){var a;return dt(e)?0===n.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(a=e.documentElement,Math.max(e.body["scroll"+t],a["scroll"+t],e.body["offset"+t],a["offset"+t],a["client"+t])):void 0===o?mt.css(e,i,s):mt.style(e,i,o,s)},e,r?o:void 0,r)}})}),mt.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){mt.fn[e]=function(t){return this.on(e,t)}}),mt.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)},hover:function(t,e){return this.on("mouseenter",t).on("mouseleave",e||t)}}),mt.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){mt.fn[e]=function(t,i){return 0<arguments.length?this.on(e,null,t,i):this.trigger(e)}});var ti=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;mt.proxy=function(t,e){var i,n,o;if("string"==typeof e&&(i=t[e],e=t,t=i),ct(t))return n=J.call(arguments,2),(o=function(){return t.apply(e||this,n.concat(J.call(arguments)))}).guid=t.guid=t.guid||mt.guid++,o},mt.holdReady=function(t){t?mt.readyWait++:mt.ready(!0)},mt.isArray=Array.isArray,mt.parseJSON=JSON.parse,mt.nodeName=a,mt.isFunction=ct,mt.isWindow=dt,mt.camelCase=f,mt.type=n,mt.now=Date.now,mt.isNumeric=function(t){var e=mt.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},mt.trim=function(t){return null==t?"":(t+"").replace(ti,"$1")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return mt});var ei=t.jQuery,ii=t.$;return mt.noConflict=function(e){return t.$===mt&&(t.$=ii),e&&t.jQuery===mt&&(t.jQuery=ei),mt},void 0===e&&(t.jQuery=t.$=mt),mt}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");+function(t){"use strict";var e=t.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1==e[0]&&9==e[1]&&e[2]<1||e[0]>3)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}(jQuery),function(t){"use strict";function e(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;t(this).one("bsTransitionEnd",function(){i=!0});var o=function(){i||t(n).trigger(t.support.transition.end)};return setTimeout(o,e),this},t(function(){t.support.transition=e(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var i=t(this),o=i.data("bs.alert");o||i.data("bs.alert",o=new n(this)),"string"==typeof e&&o[e].call(i)})}var i='[data-dismiss="alert"]',n=function(e){t(e).on("click",i,this.close)};n.VERSION="3.4.1",n.TRANSITION_DURATION=150,n.prototype.close=function(e){function i(){r.detach().trigger("closed.bs.alert").remove()}var o=t(this),a=o.attr("data-target");a||(a=o.attr("href"),a=a&&a.replace(/.*(?=#[^\s]*$)/,"")),a="#"===a?[]:a;var r=t(document).find(a);e&&e.preventDefault(),r.length||(r=o.closest(".alert")),r.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(r.removeClass("in"),t.support.transition&&r.hasClass("fade")?r.one("bsTransitionEnd",i).emulateTransitionEnd(n.TRANSITION_DURATION):i())};var o=t.fn.alert;t.fn.alert=e,t.fn.alert.Constructor=n,t.fn.alert.noConflict=function(){return t.fn.alert=o,this},t(document).on("click.bs.alert.data-api",i,n.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.button"),a="object"==typeof e&&e;o||n.data("bs.button",o=new i(this,a)),"toggle"==e?o.toggle():e&&o.setState(e)})}var i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.isLoading=!1};i.VERSION="3.4.2",i.DEFAULTS={loadingText:"loading..."},i.prototype.setState=function(e){var i="disabled",n=this.$element,o=n.is("input")?"val":"text",a=n.data();e+="Text",null==a.resetText&&n.data("resetText",n[o]()),setTimeout(t.proxy(function(){n[o](null==a[e]?this.options[e]:a[e]),"loadingText"==e?(this.isLoading=!0,n.addClass(i).attr(i,i).prop(i,!0)):this.isLoading&&(this.isLoading=!1,n.removeClass(i).removeAttr(i).prop(i,!1))},this),0)},i.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var i=this.$element.find("input");"radio"==i.prop("type")?(i.prop("checked")&&(t=!1),e.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==i.prop("type")&&(i.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),i.prop("checked",this.$element.hasClass("active")),t&&i.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var n=t.fn.button;t.fn.button=e,t.fn.button.Constructor=i,t.fn.button.noConflict=function(){return t.fn.button=n,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(i){var n=t(i.target).closest(".btn");e.call(n,"toggle"),t(i.target).is('input[type="radio"], input[type="checkbox"]')||(i.preventDefault(),n.is("input,button")?n.trigger("focus"):n.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){t(e.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(e.type))})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.carousel"),a=t.extend({},i.DEFAULTS,n.data(),"object"==typeof e&&e),r="string"==typeof e?e:a.slide;o||n.data("bs.carousel",o=new i(this,a)),"number"==typeof e?o.to(e):r?o[r]():a.interval&&o.pause().cycle()})}var i=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",t.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};i.VERSION="3.4.1",i.TRANSITION_DURATION=600,i.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},i.prototype.keydown=function(t){if(!/input|textarea/i.test(t.target.tagName)){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()}},i.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},i.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},i.prototype.getItemForDirection=function(t,e){var i=this.getItemIndex(e);if(("prev"==t&&0===i||"next"==t&&i==this.$items.length-1)&&!this.options.wrap)return e;var n="prev"==t?-1:1,o=(i+n)%this.$items.length;return this.$items.eq(o)},i.prototype.to=function(t){var e=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(t>this.$items.length-1||t<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){e.to(t)}):i==t?this.pause().cycle():this.slide(t>i?"next":"prev",this.$items.eq(t))},i.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},i.prototype.next=function(){if(!this.sliding)return this.slide("next")},i.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},i.prototype.slide=function(e,n){var o=this.$element.find(".item.active"),a=n||this.getItemForDirection(e,o),r=this.interval,s="next"==e?"left":"right",l=this;if(a.hasClass("active"))return this.sliding=!1;var c=a[0],d=t.Event("slide.bs.carousel",{relatedTarget:c,direction:s});if(this.$element.trigger(d),!d.isDefaultPrevented()){if(this.sliding=!0,r&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var u=t(this.$indicators.children()[this.getItemIndex(a)]);u&&u.addClass("active")}var p=t.Event("slid.bs.carousel",{relatedTarget:c,direction:s});return t.support.transition&&this.$element.hasClass("slide")?(a.addClass(e),"object"==typeof a&&a.length&&a[0].offsetWidth,o.addClass(s),a.addClass(s),o.one("bsTransitionEnd",function(){a.removeClass([e,s].join(" ")).addClass("active"),o.removeClass(["active",s].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(p)},0)}).emulateTransitionEnd(i.TRANSITION_DURATION)):(o.removeClass("active"),a.addClass("active"),this.sliding=!1,this.$element.trigger(p)),r&&this.cycle(),this}};var n=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=i,t.fn.carousel.noConflict=function(){return t.fn.carousel=n,this};var o=function(i){i.preventDefault();var n=t(this),o=n.attr("href");o&&(o=o.replace(/.*(?=#[^\s]+$)/,""));var a=n.attr("data-target")||o,r=t(document).find(a);if(r.hasClass("carousel")){var s=t.extend({},r.data(),n.data()),l=n.attr("data-slide-to");l&&(s.interval=!1),e.call(r,s),l&&r.data("bs.carousel").to(l)}};t(document).on("click.bs.carousel.data-api","[data-slide]",o).on("click.bs.carousel.data-api","[data-slide-to]",o),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var i=t(this);e.call(i,i.data())})})}(jQuery),function(t){"use strict";function e(e){var i,n=e.attr("data-target")||(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"");return t(document).find(n)}function i(e){return this.each(function(){var i=t(this),o=i.data("bs.collapse"),a=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!o&&a.toggle&&/show|hide/.test(e)&&(a.toggle=!1),o||i.data("bs.collapse",o=new n(this,a)),"string"==typeof e&&o[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.$trigger=t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'),this.transitioning=null,
this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};n.VERSION="3.4.1",n.TRANSITION_DURATION=350,n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var e,o=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(o&&o.length&&(e=o.data("bs.collapse"))&&e.transitioning)){var a=t.Event("show.bs.collapse");if(this.$element.trigger(a),!a.isDefaultPrevented()){o&&o.length&&(i.call(o,"hide"),e||o.data("bs.collapse",null));var r=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[r](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var s=function(){this.$element.removeClass("collapsing").addClass("collapse in")[r](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return s.call(this);var l=t.camelCase(["scroll",r].join("-"));this.$element.one("bsTransitionEnd",t.proxy(s,this)).emulateTransitionEnd(n.TRANSITION_DURATION)[r](this.$element[0][l])}}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var i=this.dimension();this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var o=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};if(!t.support.transition)return o.call(this);this.$element[i](0).one("bsTransitionEnd",t.proxy(o,this)).emulateTransitionEnd(n.TRANSITION_DURATION)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},n.prototype.getParent=function(){return t(document).find(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(t.proxy(function(i,n){var o=t(n);this.addAriaAndCollapsedClass(e(o),o)},this)).end()},n.prototype.addAriaAndCollapsedClass=function(t,e){var i=t.hasClass("in");t.attr("aria-expanded",i),e.toggleClass("collapsed",!i).attr("aria-expanded",i)};var o=t.fn.collapse;t.fn.collapse=i,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=o,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var o=t(this);o.attr("data-target")||n.preventDefault();var a=e(o),r=a.data("bs.collapse"),s=r?"toggle":o.data();i.call(a,s)})}(jQuery),function(t){"use strict";function e(e){var i=e.attr("data-target");i||(i=e.attr("href"),i=i&&/#[A-Za-z]/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n="#"!==i?t(document).find(i):null;return n&&n.length?n:e.parent()}function i(i){i&&3===i.which||(t(o).remove(),t(a).each(function(){var n=t(this),o=e(n),a={relatedTarget:this};o.hasClass("open")&&(i&&"click"==i.type&&/input|textarea/i.test(i.target.tagName)&&t.contains(o[0],i.target)||(o.trigger(i=t.Event("hide.bs.dropdown",a)),i.isDefaultPrevented()||(n.attr("aria-expanded","false"),o.removeClass("open").trigger(t.Event("hidden.bs.dropdown",a)))))}))}function n(e){return this.each(function(){var i=t(this),n=i.data("bs.dropdown");n||i.data("bs.dropdown",n=new r(this)),"string"==typeof e&&n[e].call(i)})}var o=".dropdown-backdrop",a='[data-toggle="dropdown"]',r=function(e){t(e).on("click.bs.dropdown",this.toggle)};r.VERSION="3.4.1",r.prototype.toggle=function(n){var o=t(this);if(!o.is(".disabled, :disabled")){var a=e(o),r=a.hasClass("open");if(i(),!r){"ontouchstart"in document.documentElement&&!a.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",i);var s={relatedTarget:this};if(a.trigger(n=t.Event("show.bs.dropdown",s)),n.isDefaultPrevented())return;o.trigger("focus").attr("aria-expanded","true"),a.toggleClass("open").trigger(t.Event("shown.bs.dropdown",s))}return!1}},r.prototype.keydown=function(i){if(/(38|40|27|32)/.test(i.which)&&!/input|textarea/i.test(i.target.tagName)){var n=t(this);if(i.preventDefault(),i.stopPropagation(),!n.is(".disabled, :disabled")){var o=e(n),r=o.hasClass("open");if(!r&&27!=i.which||r&&27==i.which)return 27==i.which&&o.find(a).trigger("focus"),n.trigger("click");var s=o.find(".dropdown-menu li:not(.disabled):visible a");if(s.length){var l=s.index(i.target);38==i.which&&l>0&&l--,40==i.which&&l<s.length-1&&l++,~l||(l=0),s.eq(l).trigger("focus")}}}};var s=t.fn.dropdown;t.fn.dropdown=n,t.fn.dropdown.Constructor=r,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=s,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",a,r.prototype.toggle).on("keydown.bs.dropdown.data-api",a,r.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",r.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,n){return this.each(function(){var o=t(this),a=o.data("bs.modal"),r=t.extend({},i.DEFAULTS,o.data(),"object"==typeof e&&e);a||o.data("bs.modal",a=new i(this,r)),"string"==typeof e?a[e](n):r.show&&a.show(n)})}var i=function(e,i){this.options=i,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};i.VERSION="3.4.1",i.TRANSITION_DURATION=300,i.BACKDROP_TRANSITION_DURATION=150,i.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},i.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},i.prototype.show=function(e){var n=this,o=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(o),this.isShown||o.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){n.$element.one("mouseup.dismiss.bs.modal",function(e){t(e.target).is(n.$element)&&(n.ignoreBackdropClick=!0)})}),this.backdrop(function(){var o=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),o&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var a=t.Event("shown.bs.modal",{relatedTarget:e});o?n.$dialog.one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(a)}).emulateTransitionEnd(i.TRANSITION_DURATION):n.$element.trigger("focus").trigger(a)}))},i.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(i.TRANSITION_DURATION):this.hideModal())},i.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){document===t.target||this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},i.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},i.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},i.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")})},i.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},i.prototype.backdrop=function(e){var n=this,o=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var a=t.support.transition&&o;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+o).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){if(this.ignoreBackdropClick)return void(this.ignoreBackdropClick=!1);t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())},this)),a&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;a?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):r()}else e&&e()},i.prototype.handleUpdate=function(){this.adjustDialog()},i.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},i.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},i.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},i.prototype.setScrollbar=function(){var e=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";var i=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("padding-right",e+i),t(this.fixedContent).each(function(e,n){var o=n.style.paddingRight,a=t(n).css("padding-right");t(n).data("padding-right",o).css("padding-right",parseFloat(a)+i+"px")}))},i.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad),t(this.fixedContent).each(function(e,i){var n=t(i).data("padding-right");t(i).removeData("padding-right"),i.style.paddingRight=n||""})},i.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var n=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=i,t.fn.modal.noConflict=function(){return t.fn.modal=n,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(i){var n=t(this),o=n.attr("href"),a=n.attr("data-target")||o&&o.replace(/.*(?=#[^\s]+$)/,""),r=t(document).find(a),s=r.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(o)&&o},r.data(),n.data());n.is("a")&&i.preventDefault(),r.one("show.bs.modal",function(t){t.isDefaultPrevented()||r.one("hidden.bs.modal",function(){n.is(":visible")&&n.trigger("focus")})}),e.call(r,s,this)})}(jQuery),function(t){"use strict";function e(e,i){var n=e.nodeName.toLowerCase();if(-1!==t.inArray(n,i))return-1===t.inArray(n,a)||Boolean(e.nodeValue.match(l)||e.nodeValue.match(c));for(var o=t(i).filter(function(t,e){return e instanceof RegExp}),r=0,s=o.length;r<s;r++)if(n.match(o[r]))return!0;return!1}function i(i,n,o){if(0===i.length)return i;if(o&&"function"==typeof o)return o(i);if(!document.implementation||!document.implementation.createHTMLDocument)return i;var a=document.implementation.createHTMLDocument("sanitization");a.body.innerHTML=i;for(var r=t.map(n,function(t,e){return e}),s=t(a.body).find("*"),l=0,c=s.length;l<c;l++){var d=s[l],u=d.nodeName.toLowerCase();if(-1!==t.inArray(u,r))for(var p=t.map(d.attributes,function(t){return t}),h=[].concat(n["*"]||[],n[u]||[]),f=0,m=p.length;f<m;f++)e(p[f],h)||d.removeAttribute(p[f].nodeName);else d.parentNode.removeChild(d)}return a.body.innerHTML}function n(e){return this.each(function(){var i=t(this),n=i.data("bs.tooltip"),o="object"==typeof e&&e;!n&&/destroy|hide/.test(e)||(n||i.data("bs.tooltip",n=new d(this,o)),"string"==typeof e&&n[e]())})}var o=["sanitize","whiteList","sanitizeFn"],a=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],r=/^aria-[\w-]*$/i,s={"*":["class","dir","id","lang","role",r],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},l=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,c=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i,d=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};d.VERSION="3.4.1",d.TRANSITION_DURATION=150,d.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:s},d.prototype.init=function(e,i,n){if(this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&t(document).find(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var o=this.options.trigger.split(" "),a=o.length;a--;){var r=o[a];if("click"==r)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=r){var s="hover"==r?"mouseenter":"focusin",l="hover"==r?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},d.prototype.getDefaults=function(){return d.DEFAULTS},d.prototype.getOptions=function(e){var n=this.$element.data();for(var a in n)n.hasOwnProperty(a)&&-1!==t.inArray(a,o)&&delete n[a];return e=t.extend({},this.getDefaults(),n,e),e.delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e.sanitize&&(e.template=i(e.template,e.whiteList,e.sanitizeFn)),e},d.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},d.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusin"==e.type?"focus":"hover"]=!0),i.tip().hasClass("in")||"in"==i.hoverState?void(i.hoverState="in"):(clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show())},d.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},d.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusout"==e.type?"focus":"hover"]=!1),!i.isInStateTrue()){if(clearTimeout(i.timeout),i.hoverState="out",!i.options.delay||!i.options.delay.hide)return i.hide();i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)}},d.prototype.show=function(){var e=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(e);var i=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(e.isDefaultPrevented()||!i)return;var n=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var r="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,s=/\s?auto?\s?/i,l=s.test(r);l&&(r=r.replace(s,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(r).data("bs."+this.type,this),this.options.container?o.appendTo(t(document).find(this.options.container)):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var c=this.getPosition(),u=o[0].offsetWidth,p=o[0].offsetHeight;if(l){var h=r,f=this.getPosition(this.$viewport);r="bottom"==r&&c.bottom+p>f.bottom?"top":"top"==r&&c.top-p<f.top?"bottom":"right"==r&&c.right+u>f.width?"left":"left"==r&&c.left-u<f.left?"right":r,o.removeClass(h).addClass(r)}var m=this.getCalculatedOffset(r,c,u,p);this.applyPlacement(m,r);var g=function(){var t=n.hoverState;n.$element.trigger("shown.bs."+n.type),n.hoverState=null,"out"==t&&n.leave(n)};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",g).emulateTransitionEnd(d.TRANSITION_DURATION):g()}},d.prototype.applyPlacement=function(e,i){var n=this.tip(),o=n[0].offsetWidth,a=n[0].offsetHeight,r=parseInt(n.css("margin-top"),10),s=parseInt(n.css("margin-left"),10);isNaN(r)&&(r=0),isNaN(s)&&(s=0),e.top+=r,e.left+=s,t.offset.setOffset(n[0],t.extend({using:function(t){n.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),n.addClass("in");var l=n[0].offsetWidth,c=n[0].offsetHeight;"top"==i&&c!=a&&(e.top=e.top+a-c);var d=this.getViewportAdjustedDelta(i,e,l,c);d.left?e.left+=d.left:e.top+=d.top;var u=/top|bottom/.test(i),p=u?2*d.left-o+l:2*d.top-a+c,h=u?"offsetWidth":"offsetHeight";n.offset(e),this.replaceArrow(p,n[0][h],u)},d.prototype.replaceArrow=function(t,e,i){this.arrow().css(i?"left":"top",50*(1-t/e)+"%").css(i?"top":"left","")},d.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();this.options.html?(this.options.sanitize&&(e=i(e,this.options.whiteList,this.options.sanitizeFn)),t.find(".tooltip-inner").html(e)):t.find(".tooltip-inner").text(e),t.removeClass("fade in top bottom left right")},d.prototype.hide=function(e){function i(){"in"!=n.hoverState&&o.detach(),n.$element&&n.$element.removeAttr("aria-describedby").trigger("hidden.bs."+n.type),e&&e()}var n=this,o=t(this.$tip),a=t.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),t.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",i).emulateTransitionEnd(d.TRANSITION_DURATION):i(),this.hoverState=null,this},d.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},d.prototype.hasContent=function(){return this.getTitle()},d.prototype.getPosition=function(e){e=e||this.$element;var i=e[0],n="BODY"==i.tagName,o=i.getBoundingClientRect();null==o.width&&(o=t.extend({},o,{width:o.right-o.left,height:o.bottom-o.top}));var a=window.SVGElement&&i instanceof window.SVGElement,r=n?{top:0,left:0}:a?null:e.offset(),s={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},l=n?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},o,s,l,r)},d.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},d.prototype.getViewportAdjustedDelta=function(t,e,i,n){var o={top:0,left:0};if(!this.$viewport)return o;var a=this.options.viewport&&this.options.viewport.padding||0,r=this.getPosition(this.$viewport);if(/right|left/.test(t)){var s=e.top-a-r.scroll,l=e.top+a-r.scroll+n;s<r.top?o.top=r.top-s:l>r.top+r.height&&(o.top=r.top+r.height-l)}else{var c=e.left-a,d=e.left+a+i;c<r.left?o.left=r.left-c:d>r.right&&(o.left=r.left+r.width-d)}return o},d.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},d.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},d.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},d.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},d.prototype.enable=function(){this.enabled=!0},d.prototype.disable=function(){this.enabled=!1},d.prototype.toggleEnabled=function(){this.enabled=!this.enabled},d.prototype.toggle=function(e){var i=this;e&&((i=t(e.currentTarget).data("bs."+this.type))||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i))),e?(i.inState.click=!i.inState.click,i.isInStateTrue()?i.enter(i):i.leave(i)):i.tip().hasClass("in")?i.leave(i):i.enter(i)},d.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide(function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null})},d.prototype.sanitizeHtml=function(t){return i(t,this.options.whiteList,this.options.sanitizeFn)};var u=t.fn.tooltip;t.fn.tooltip=n,t.fn.tooltip.Constructor=d,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=u,this}}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.popover"),a="object"==typeof e&&e;!o&&/destroy|hide/.test(e)||(o||n.data("bs.popover",o=new i(this,a)),"string"==typeof e&&o[e]())})}var i=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");i.VERSION="3.4.1",i.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),i.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),i.prototype.constructor=i,i.prototype.getDefaults=function(){return i.DEFAULTS},i.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),i=this.getContent();if(this.options.html){var n=typeof i;this.options.sanitize&&(e=this.sanitizeHtml(e),"string"===n&&(i=this.sanitizeHtml(i))),t.find(".popover-title").html(e),t.find(".popover-content").children().detach().end()["string"===n?"html":"append"](i)}else t.find(".popover-title").text(e),t.find(".popover-content").children().detach().end().text(i);t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},i.prototype.hasContent=function(){return this.getTitle()||this.getContent()},i.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},i.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var n=t.fn.popover;t.fn.popover=e,t.fn.popover.Constructor=i,t.fn.popover.noConflict=function(){return t.fn.popover=n,this}}(jQuery),function(t){"use strict";function e(i,n){this.$body=t(document.body),this.$scrollElement=t(t(i).is(document.body)?window:i),this.options=t.extend({},e.DEFAULTS,n),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",t.proxy(this.process,this)),this.refresh(),this.process()}function i(i){return this.each(function(){var n=t(this),o=n.data("bs.scrollspy"),a="object"==typeof i&&i;o||n.data("bs.scrollspy",o=new e(this,a)),"string"==typeof i&&o[i]()})}e.VERSION="3.4.1",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e=this,i="offset",n=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),t.isWindow(this.$scrollElement[0])||(i="position",n=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var e=t(this),o=e.data("target")||e.attr("href"),a=/^#./.test(o)&&t(o);return a&&a.length&&a.is(":visible")&&[[a[i]().top+n,o]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){e.offsets.push(this[0]),e.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,i=this.getScrollHeight(),n=this.options.offset+i-this.$scrollElement.height(),o=this.offsets,a=this.targets,r=this.activeTarget;if(this.scrollHeight!=i&&this.refresh(),e>=n)return r!=(t=a[a.length-1])&&this.activate(t);if(r&&e<o[0])return this.activeTarget=null,this.clear();for(t=o.length;t--;)r!=a[t]&&e>=o[t]&&(void 0===o[t+1]||e<o[t+1])&&this.activate(a[t])},e.prototype.activate=function(e){this.activeTarget=e,this.clear();var i=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',n=t(i).parents("li").addClass("active");n.parent(".dropdown-menu").length&&(n=n.closest("li.dropdown").addClass("active")),n.trigger("activate.bs.scrollspy")},e.prototype.clear=function(){t(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var n=t.fn.scrollspy;t.fn.scrollspy=i,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=n,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);i.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.tab");o||n.data("bs.tab",o=new i(this)),"string"==typeof e&&o[e]()})}var i=function(e){this.element=t(e)};i.VERSION="3.4.1",i.TRANSITION_DURATION=150,i.prototype.show=function(){var e=this.element,i=e.closest("ul:not(.dropdown-menu)"),n=e.data("target");if(n||(n=e.attr("href"),n=n&&n.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var o=i.find(".active:last a"),a=t.Event("hide.bs.tab",{relatedTarget:e[0]}),r=t.Event("show.bs.tab",{relatedTarget:o[0]});if(o.trigger(a),e.trigger(r),!r.isDefaultPrevented()&&!a.isDefaultPrevented()){var s=t(document).find(n);this.activate(e.closest("li"),i),this.activate(s,s.parent(),function(){o.trigger({type:"hidden.bs.tab",relatedTarget:e[0]}),e.trigger({type:"shown.bs.tab",relatedTarget:o[0]})})}}},i.prototype.activate=function(e,n,o){function a(){r.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),e.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),s?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu").length&&e.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),o&&o()}var r=n.find("> .active"),s=o&&t.support.transition&&(r.length&&r.hasClass("fade")||!!n.find("> .fade").length);r.length&&s?r.one("bsTransitionEnd",a).emulateTransitionEnd(i.TRANSITION_DURATION):a(),r.removeClass("in")};var n=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this};var o=function(i){i.preventDefault(),e.call(t(this),"show")};t(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',o).on("click.bs.tab.data-api",'[data-toggle="pill"]',o)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),o=n.data("bs.affix"),a="object"==typeof e&&e;o||n.data("bs.affix",o=new i(this,a)),"string"==typeof e&&o[e]()})}var i=function(e,n){this.options=t.extend({},i.DEFAULTS,n);var o=this.options.target===i.DEFAULTS.target?t(this.options.target):t(document).find(this.options.target);this.$target=o.on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};i.VERSION="3.4.1",i.RESET="affix affix-top affix-bottom",i.DEFAULTS={offset:0,target:window},i.prototype.getState=function(t,e,i,n){var o=this.$target.scrollTop(),a=this.$element.offset(),r=this.$target.height();if(null!=i&&"top"==this.affixed)return o<i&&"top";if("bottom"==this.affixed)return null!=i?!(o+this.unpin<=a.top)&&"bottom":!(o+r<=t-n)&&"bottom";var s=null==this.affixed,l=s?o:a.top,c=s?r:e;return null!=i&&o<=i?"top":null!=n&&l+c>=t-n&&"bottom"},i.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(i.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},i.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},i.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=this.$element.height(),n=this.options.offset,o=n.top,a=n.bottom,r=Math.max(t(document).height(),t(document.body).height());"object"!=typeof n&&(a=o=n),"function"==typeof o&&(o=n.top(this.$element)),"function"==typeof a&&(a=n.bottom(this.$element));var s=this.getState(r,e,o,a);if(this.affixed!=s){null!=this.unpin&&this.$element.css("top","");var l="affix"+(s?"-"+s:""),c=t.Event(l+".bs.affix");if(this.$element.trigger(c),c.isDefaultPrevented())return;this.affixed=s,this.unpin="bottom"==s?this.getPinnedOffset():null,this.$element.removeClass(i.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==s&&this.$element.offset({top:r-e-a})}};var n=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=i,t.fn.affix.noConflict=function(){return t.fn.affix=n,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var i=t(this),n=i.data();n.offset=n.offset||{},null!=n.offsetBottom&&(n.offset.bottom=n.offsetBottom),null!=n.offsetTop&&(n.offset.top=n.offsetTop),e.call(i,n)})})}(jQuery),define("bootstrap",["jquery"],function(){}),require.config({urlArgs:"v="+requirejs.s.contexts._.config.config.site.version,packages:[{name:"moment",location:"../libs/moment",main:"moment"}],include:["css","layer","toastr","fast","frontend","frontend-init","table","form","dragsort","selectpage"],paths:{lang:"empty:",form:"require-form",table:"require-table",upload:"require-upload",dropzone:"dropzone.min",echarts:"echarts.min","echarts-theme":"echarts-theme",adminlte:"adminlte","bootstrap-table-commonsearch":"bootstrap-table-commonsearch","bootstrap-table-template":"bootstrap-table-template",jquery:"../libs/jquery/dist/jquery.min",bootstrap:"../libs/bootstrap/dist/js/bootstrap.min","bootstrap-datetimepicker":"../libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min","bootstrap-daterangepicker":"../libs/bootstrap-daterangepicker/daterangepicker","bootstrap-select":"../libs/bootstrap-select/dist/js/bootstrap-select.min","bootstrap-select-lang":"../libs/bootstrap-select/dist/js/i18n/defaults-zh_CN",
"bootstrap-table":"../libs/bootstrap-table/dist/bootstrap-table.min","bootstrap-table-export":"../libs/bootstrap-table/dist/extensions/export/bootstrap-table-export.min","bootstrap-table-fixed-columns":"../libs/bootstrap-table/dist/extensions/fixed-columns/bootstrap-table-fixed-columns","bootstrap-table-mobile":"../libs/bootstrap-table/dist/extensions/mobile/bootstrap-table-mobile","bootstrap-table-lang":"../libs/bootstrap-table/dist/locale/bootstrap-table-zh-CN","bootstrap-table-jumpto":"../libs/bootstrap-table/dist/extensions/page-jumpto/bootstrap-table-jumpto",tableexport:"../libs/tableExport.jquery.plugin/tableExport.min",dragsort:"../libs/fastadmin-dragsort/jquery.dragsort",sortable:"../libs/Sortable/Sortable.min",addtabs:"../libs/fastadmin-addtabs/jquery.addtabs",slimscroll:"../libs/jquery-slimscroll/jquery.slimscroll",validator:"../libs/nice-validator/dist/jquery.validator","validator-lang":"../libs/nice-validator/dist/local/zh-CN",toastr:"../libs/toastr/toastr",jstree:"../libs/jstree/dist/jstree.min",layer:"../libs/fastadmin-layer/dist/layer",cookie:"../libs/jquery.cookie/jquery.cookie",cxselect:"../libs/fastadmin-cxselect/js/jquery.cxselect",template:"../libs/art-template/dist/template-native",selectpage:"../libs/fastadmin-selectpage/selectpage",citypicker:"../libs/fastadmin-citypicker/dist/js/city-picker.min","citypicker-data":"../libs/fastadmin-citypicker/dist/js/city-picker.data"},shim:{addons:["frontend"],bootstrap:["jquery"],"bootstrap-table":{deps:["bootstrap"],exports:"$.fn.bootstrapTable"},"bootstrap-table-lang":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-export":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-fixed-columns":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-mobile":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-advancedsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-commonsearch":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-template":{deps:["bootstrap-table","template"],exports:"$.fn.bootstrapTable.defaults"},"bootstrap-table-jumpto":{deps:["bootstrap-table"],exports:"$.fn.bootstrapTable.defaults"},tableexport:{deps:["jquery"],exports:"$.fn.extend"},slimscroll:{deps:["jquery"],exports:"$.fn.extend"},adminlte:{deps:["bootstrap","slimscroll"],exports:"$.AdminLTE"},"bootstrap-daterangepicker":["moment/locale/zh-cn"],"bootstrap-datetimepicker":["moment/locale/zh-cn"],"bootstrap-select-lang":["bootstrap-select"],jstree:["css!../libs/jstree/dist/themes/default/style.css"],"validator-lang":["validator"],citypicker:["citypicker-data","css!../libs/fastadmin-citypicker/dist/css/city-picker.css"]},baseUrl:requirejs.s.contexts._.config.config.site.cdnurl+"/assets/js/",map:{"*":{css:"../libs/require-css/css.min"}},waitSeconds:60,charset:"utf-8"}),require(["jquery","bootstrap"],function(t,e){var i=requirejs.s.contexts._.config.config;window.Config=i;var n={};n.lang=i.moduleurl+"/ajax/lang?callback=define&controllername="+i.controllername+"&lang="+i.language,n["frontend/"]="frontend/",require.config({paths:n}),t(function(){require(["fast"],function(t){require(["frontend","frontend-init","addons"],function(t,n){i.jsname&&require([i.jsname],function(t){t[i.actionname]!=e&&t[i.actionname]()},function(t){console.error(t)})})})})}),define("require-frontend",function(){}),define("../libs/require-css/css.min",[],function(){if("undefined"==typeof window)return{load:function(t,e,i){i()}};var t=document.getElementsByTagName("head")[0],e=window.navigator.userAgent.match(/Trident\/([^ ;]*)|AppleWebKit\/([^ ;]*)|Opera\/([^ ;]*)|rv\:([^ ;]*)(.*?)Gecko\/([^ ;]*)|MSIE\s([^ ;]*)|AndroidWebKit\/([^ ;]*)/)||0,i=!1,n=!0;e[1]||e[7]?i=parseInt(e[1])<6||parseInt(e[7])<=9:e[2]||e[8]?n=!1:e[4]&&(i=parseInt(e[4])<18);var o={};o.pluginBuilder="./css-builder";var a,r,s,l=function(){a=document.createElement("style"),t.appendChild(a),r=a.styleSheet||a.sheet},c=0,d=[],u=function(t){r.addImport(t),a.onload=function(){p()},31==++c&&(l(),c=0)},p=function(){s();var t=d.shift();return t?(s=t[1],void u(t[0])):void(s=null)},h=function(t,e){if(r&&r.addImport||l(),r&&r.addImport)s?d.push([t,e]):(u(t),s=e);else{a.textContent='@import "'+t+'";';var i=setInterval(function(){try{a.sheet.cssRules,clearInterval(i),e()}catch(t){}},10)}},f=function(e,i){var o=document.createElement("link");if(o.type="text/css",o.rel="stylesheet",n)o.onload=function(){o.onload=function(){},setTimeout(i,7)};else var a=setInterval(function(){for(var t=0;t<document.styleSheets.length;t++){if(document.styleSheets[t].href==o.href)return clearInterval(a),i()}},10);o.href=e,t.appendChild(o)};return o.normalize=function(t,e){return".css"==t.substr(t.length-4,4)&&(t=t.substr(0,t.length-4)),e(t)},o.load=function(t,e,n,o){(i?h:f)(e.toUrl(t+".css"),n)},o}),function(t,e){"use strict";var i,n,o=t.layui&&layui.define,a={getPath:function(){var e=document.currentScript?document.currentScript.src:function(){for(var t,e=document.scripts,i=e.length-1,n=i;n>0;n--)if("interactive"===e[n].readyState){t=e[n].src;break}return t||e[i].src}();return(t.LAYUI_GLOBAL||{}).layer_dir||e.substring(0,e.lastIndexOf("/")+1)}(),config:{},end:{},minIndex:0,minLeft:[],btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],type:["dialog","page","iframe","loading","tips"],getStyle:function(e,i){var n=e.currentStyle?e.currentStyle:t.getComputedStyle(e,null);return n[n.getPropertyValue?"getPropertyValue":"getAttribute"](i)},link:function(e,i,n){if(r.path){var o=document.getElementsByTagName("head")[0],s=document.createElement("link");"string"==typeof i&&(n=i);var l=(n||e).replace(/\.|\//g,""),c="layuicss-"+l,d="creating",u=0;s.rel="stylesheet",s.href=r.path+e,s.id=c,document.getElementById(c)||o.appendChild(s),"function"==typeof i&&function e(n){var o=document.getElementById(c);return++u>100?t.console&&console.error(l+".css: Invalid"):void(1989===parseInt(a.getStyle(o,"width"))?(n===d&&o.removeAttribute("lay-status"),o.getAttribute("lay-status")===d?setTimeout(e,100):i()):(o.setAttribute("lay-status",d),setTimeout(function(){e(d)},100)))}()}}},r={v:"3.5.2",ie:function(){var e=navigator.userAgent.toLowerCase();return!!(t.ActiveXObject||"ActiveXObject"in t)&&((e.match(/msie\s(\d+)/)||[])[1]||"11")}(),index:t.layer&&t.layer.v?1e5:0,path:a.getPath,config:function(t,e){return t=t||{},c=r.cache=a.config=i.extend({},a.config,t),r.path=a.config.path||r.path,"string"==typeof t.extend&&(t.extend=[t.extend]),a.config.path&&r.ready(),t.extend?(o?layui.addcss("modules/layer/"+t.extend):a.link("theme/"+t.extend),this):this},ready:function(t){var e="layer",i=(o?"modules/layer/":"theme/")+"default/layer.css?v="+r.v;return o?layui.addcss(i,t,e):a.link(i,t,e),this},alert:function(t,e,n){var o="function"==typeof e;return o&&(n=e),r.open(i.extend({content:t,yes:n},o?{}:e))},confirm:function(t,e,n,o){var s="function"==typeof e;return s&&(o=n,n=e),r.open(i.extend({content:t,btn:a.btn,yes:n,btn2:o},s?{}:e))},msg:function(t,e,n){var o="function"==typeof e,s=a.config.skin,c=(s?s+" "+s+"-msg":"")||"layui-layer-msg",d=l.anim.length-1;return o&&(n=e),r.open(i.extend({content:t,time:3e3,shade:!1,skin:c,title:!1,closeBtn:!1,btn:!1,resize:!1,end:n},o&&!a.config.skin?{skin:c+" layui-layer-hui",anim:d}:function(){return e=e||{},-1!==e.icon&&void 0!==e.icon||(e.skin=c+" "+(e.skin||"layui-layer-hui")),e}()))},load:function(t,e){return r.open(i.extend({type:3,icon:t||0,resize:!1,shade:.01},e))},tips:function(t,e,n){return r.open(i.extend({type:4,content:[t,e],closeBtn:!1,time:3e3,shade:!1,resize:!1,fixed:!1,maxWidth:260},n))}},s=function(t){var e=this,n=function(){e.creat()};e.index=++r.index,e.config=i.extend({},e.config,a.config,t),document.body?n():setTimeout(function(){n()},30)};s.pt=s.prototype;var l=["layui-layer",".layui-layer-title",".layui-layer-main",".layui-layer-dialog","layui-layer-iframe","layui-layer-content","layui-layer-btn","layui-layer-close"];l.anim=["layer-anim-00","layer-anim-01","layer-anim-02","layer-anim-03","layer-anim-04","layer-anim-05","layer-anim-06"],l.SHADE="layui-layer-shade",l.MOVE="layui-layer-move",s.pt.config={type:0,shade:.3,fixed:!0,move:l[1],title:"&#x4FE1;&#x606F;",offset:"auto",area:"auto",closeBtn:1,time:0,zIndex:19891014,maxWidth:360,anim:0,isOutAnim:!0,minStack:!0,focusBtn:0,icon:-1,moveType:1,resize:!0,scrollbar:!0,tips:2},s.pt.vessel=function(t,e){var n=this,o=n.index,r=n.config,s=r.zIndex+o,c="object"==typeof r.title,d=r.maxmin&&(1===r.type||2===r.type),u=r.title?'<div class="layui-layer-title" style="'+(c?r.title[1]:"")+'">'+(c?r.title[0]:r.title)+"</div>":"";return r.zIndex=s,e([r.shade?'<div class="'+l.SHADE+'" id="'+l.SHADE+o+'" times="'+o+'" style="z-index:'+(s-1)+'; "></div>':"",'<div class="'+l[0]+" layui-layer-"+a.type[r.type]+(0!=r.type&&2!=r.type||r.shade?"":" layui-layer-border")+" "+(r.skin||"")+'" id="'+l[0]+o+'" type="'+a.type[r.type]+'" times="'+o+'" showtime="'+r.time+'" conType="'+(t?"object":"string")+'" style="z-index: '+s+"; width:"+r.area[0]+";height:"+r.area[1]+";position:"+(r.fixed?"fixed;":"absolute;")+'">'+(t&&2!=r.type?"":u)+'<div id="'+(r.id||"")+'" class="layui-layer-content'+(0==r.type&&-1!==r.icon?" layui-layer-padding":"")+(3==r.type?" layui-layer-loading"+r.icon:"")+'">'+(0==r.type&&-1!==r.icon?'<i class="layui-layer-ico layui-layer-ico'+r.icon+'"></i>':"")+(1==r.type&&t?"":r.content||"")+'</div><span class="layui-layer-setwin">'+function(){var t=d?'<a class="layui-layer-min" href="javascript:;"><cite></cite></a><a class="layui-layer-ico layui-layer-max" href="javascript:;"></a>':"";return r.closeBtn&&(t+='<a class="layui-layer-ico '+l[7]+" "+l[7]+(r.title?r.closeBtn:4==r.type?"1":"2")+'" href="javascript:;"></a>'),t}()+"</span>"+(r.btn?function(){var t="";"string"==typeof r.btn&&(r.btn=[r.btn]);for(var e=0,i=r.btn.length;e<i;e++)t+='<a class="'+l[6]+e+'" href="javascript:">'+r.btn[e]+"</a>";return'<div class="'+l[6]+" layui-layer-btn-"+(r.btnAlign||"")+'">'+t+"</div>"}():"")+(r.resize?'<span class="layui-layer-resize"></span>':"")+"</div>"],u,i('<div class="'+l.MOVE+'" id="'+l.MOVE+'"></div>')),n},s.pt.creat=function(){var t=this,e=t.config,o=t.index,s=e.content,c="object"==typeof s,d=i("body");if(!e.id||!i("#"+e.id)[0]){switch("string"==typeof e.area&&(e.area="auto"===e.area?["",""]:[e.area,""]),e.shift&&(e.anim=e.shift),6==r.ie&&(e.fixed=!1),e.type){case 0:e.btn="btn"in e?e.btn:a.btn[0],r.closeAll("dialog");break;case 2:var s=e.content=c?e.content:[e.content||"","auto"];e.content='<iframe scrolling="'+(e.content[1]||"auto")+'" allowtransparency="true" id="'+l[4]+o+'" name="'+l[4]+o+'" onload="this.className=\'\';" class="layui-layer-load" frameborder="0" src="'+e.content[0]+'"></iframe>';break;case 3:delete e.title,delete e.closeBtn,-1===e.icon&&e.icon,r.closeAll("loading");break;case 4:c||(e.content=[e.content,"body"]),e.follow=e.content[1],e.content=e.content[0]+'<i class="layui-layer-TipsG"></i>',delete e.title,e.tips="object"==typeof e.tips?e.tips:[e.tips,!0],e.tipsMore||r.closeAll("tips")}if(t.vessel(c,function(n,r,u){d.append(n[0]),c?function(){2==e.type||4==e.type?function(){i("body").append(n[1])}():function(){s.parents("."+l[0])[0]||(s.data("display",s.css("display")).show().addClass("layui-layer-wrap").wrap(n[1]),i("#"+l[0]+o).find("."+l[5]).before(r))}()}():d.append(n[1]),i("#"+l.MOVE)[0]||d.append(a.moveElem=u),t.layero=i("#"+l[0]+o),t.shadeo=i("#"+l.SHADE+o),e.scrollbar||l.html.css("overflow","hidden").attr("layer-full",o)}).auto(o),t.shadeo.css({"background-color":e.shade[1]||"#000",opacity:e.shade[0]||e.shade}),2==e.type&&6==r.ie&&t.layero.find("iframe").attr("src",s[0]),4==e.type?t.tips():function(){t.offset(),parseInt(a.getStyle(document.getElementById(l.MOVE),"z-index"))||function(){t.layero.css("visibility","hidden"),r.ready(function(){t.offset(),t.layero.css("visibility","visible")})}()}(),e.fixed&&n.on("resize",function(){t.offset(),(/^\d+%$/.test(e.area[0])||/^\d+%$/.test(e.area[1]))&&t.auto(o),4==e.type&&t.tips()}),e.time<=0||setTimeout(function(){r.close(t.index)},e.time),t.move().callback(),l.anim[e.anim]){var u="layer-anim "+l.anim[e.anim];t.layero.addClass(u).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){i(this).removeClass(u)})}e.isOutAnim&&t.layero.data("isOutAnim",!0)}},s.pt.auto=function(t){var e=this,o=e.config,a=i("#"+l[0]+t);""===o.area[0]&&o.maxWidth>0&&(r.ie&&r.ie<8&&o.btn&&a.width(a.innerWidth()),a.outerWidth()>o.maxWidth&&a.width(o.maxWidth));var s=[a.innerWidth(),a.innerHeight()],c=a.find(l[1]).outerHeight()||0,d=a.find("."+l[6]).outerHeight()||0,u=function(t){t=a.find(t),t.height(s[1]-c-d-2*(0|parseFloat(t.css("padding-top"))))};switch(o.type){case 2:u("iframe");break;default:""===o.area[1]?o.maxHeight>0&&a.outerHeight()>o.maxHeight?(s[1]=o.maxHeight,u("."+l[5])):o.fixed&&s[1]>=n.height()&&(s[1]=n.height(),u("."+l[5])):u("."+l[5])}return e},s.pt.offset=function(){var t=this,e=t.config,i=t.layero,o=[i.outerWidth(),i.outerHeight()],a="object"==typeof e.offset;t.offsetTop=(n.height()-o[1])/2,t.offsetLeft=(n.width()-o[0])/2,a?(t.offsetTop=e.offset[0],t.offsetLeft=e.offset[1]||t.offsetLeft):"auto"!==e.offset&&("t"===e.offset?t.offsetTop=0:"r"===e.offset?t.offsetLeft=n.width()-o[0]:"b"===e.offset?t.offsetTop=n.height()-o[1]:"l"===e.offset?t.offsetLeft=0:"lt"===e.offset?(t.offsetTop=0,t.offsetLeft=0):"lb"===e.offset?(t.offsetTop=n.height()-o[1],t.offsetLeft=0):"rt"===e.offset?(t.offsetTop=0,t.offsetLeft=n.width()-o[0]):"rb"===e.offset?(t.offsetTop=n.height()-o[1],t.offsetLeft=n.width()-o[0]):t.offsetTop=e.offset),e.fixed||(t.offsetTop=/%$/.test(t.offsetTop)?n.height()*parseFloat(t.offsetTop)/100:parseFloat(t.offsetTop),t.offsetLeft=/%$/.test(t.offsetLeft)?n.width()*parseFloat(t.offsetLeft)/100:parseFloat(t.offsetLeft),t.offsetTop+=n.scrollTop(),t.offsetLeft+=n.scrollLeft()),i.attr("minLeft")&&(t.offsetTop=n.height()-(i.find(l[1]).outerHeight()||0),t.offsetLeft=i.css("left")),i.css({top:t.offsetTop,left:t.offsetLeft})},s.pt.tips=function(){var t=this,e=t.config,o=t.layero,a=[o.outerWidth(),o.outerHeight()],r=i(e.follow);r[0]||(r=i("body"));var s={width:r.outerWidth(),height:r.outerHeight(),top:r.offset().top,left:r.offset().left},c=o.find(".layui-layer-TipsG"),d=e.tips[0];e.tips[1]||c.remove(),s.autoLeft=function(){s.left+a[0]-n.width()>0?(s.tipLeft=s.left+s.width-a[0],c.css({right:12,left:"auto"})):s.tipLeft=s.left},s.where=[function(){s.autoLeft(),s.tipTop=s.top-a[1]-10,c.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color",e.tips[1])},function(){s.tipLeft=s.left+s.width+10,s.tipTop=s.top,c.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color",e.tips[1])},function(){s.autoLeft(),s.tipTop=s.top+s.height+10,c.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color",e.tips[1])},function(){s.tipLeft=s.left-a[0]-10,s.tipTop=s.top,c.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color",e.tips[1])}],s.where[d-1](),1===d?s.top-(n.scrollTop()+a[1]+16)<0&&s.where[2]():2===d?n.width()-(s.left+s.width+a[0]+16)>0||s.where[3]():3===d?s.top-n.scrollTop()+s.height+a[1]+16-n.height()>0&&s.where[0]():4===d&&a[0]+16-s.left>0&&s.where[1](),o.find("."+l[5]).css({"background-color":e.tips[1],"padding-right":e.closeBtn?"30px":""}),o.css({left:s.tipLeft-(e.fixed?n.scrollLeft():0),top:s.tipTop-(e.fixed?n.scrollTop():0)})},s.pt.move=function(){var t=this,e=t.config,o=i(document),s=t.layero,l=s.find(e.move),c=s.find(".layui-layer-resize"),d={};return e.move&&l.css("cursor","move"),l.on("mousedown",function(t){t.preventDefault(),e.move&&(d.moveStart=!0,d.offset=[t.clientX-parseFloat(s.css("left")),t.clientY-parseFloat(s.css("top"))],a.moveElem.css("cursor","move").show())}),c.on("mousedown",function(t){t.preventDefault(),d.resizeStart=!0,d.offset=[t.clientX,t.clientY],d.area=[s.outerWidth(),s.outerHeight()],a.moveElem.css("cursor","se-resize").show()}),o.on("mousemove",function(i){if(d.moveStart){var o=i.clientX-d.offset[0],a=i.clientY-d.offset[1],l="fixed"===s.css("position");if(i.preventDefault(),d.stX=l?0:n.scrollLeft(),d.stY=l?0:n.scrollTop(),!e.moveOut){var c=n.width()-s.outerWidth()+d.stX,u=n.height()-s.outerHeight()+d.stY;o<d.stX&&(o=d.stX),o>c&&(o=c),a<d.stY&&(a=d.stY),a>u&&(a=u)}s.css({left:o,top:a})}if(e.resize&&d.resizeStart){var o=i.clientX-d.offset[0],a=i.clientY-d.offset[1];i.preventDefault(),r.style(t.index,{width:d.area[0]+o,height:d.area[1]+a}),d.isResize=!0,e.resizing&&e.resizing(s)}}).on("mouseup",function(t){d.moveStart&&(delete d.moveStart,a.moveElem.hide(),e.moveEnd&&e.moveEnd(s)),d.resizeStart&&(delete d.resizeStart,a.moveElem.hide())}),t},s.pt.callback=function(){function t(){!1===(o.cancel&&o.cancel(e.index,n))||r.close(e.index)}var e=this,n=e.layero,o=e.config;if(e.openLayer(),o.success&&(2==o.type?n.find("iframe").on("load",function(){o.success(n,e.index)}):o.success(n,e.index)),6==r.ie&&e.IE6(n),n.find("."+l[6]).children("a").on("click",function(){var t=i(this).index();if(0===t)o.yes?o.yes(e.index,n):o.btn1?o.btn1(e.index,n):r.close(e.index);else{!1===(o["btn"+(t+1)]&&o["btn"+(t+1)](e.index,n))||r.close(e.index)}}),"number"==typeof o.focusBtn){n.find("."+l[6]).children("a").each(function(){var t=i(this),e=i("<button type='button' />").addClass("layui-layer-confirm");t.css("position","relative").attr("tabindex",-1).append(e),e.click(function(){return t.trigger("click"),!1})});var s=n.find("."+l[6]).find("button.layui-layer-confirm").eq(o.focusBtn);s.length>0&&s.focus()}n.find("."+l[7]).on("click",t),o.shadeClose&&e.shadeo.on("click",function(){r.close(e.index)}),n.find(".layui-layer-min").on("click",function(){!1===(o.min&&o.min(n,e.index))||r.min(e.index,o)}),n.find(".layui-layer-max").on("click",function(){i(this).hasClass("layui-layer-maxmin")?(r.restore(e.index),o.restore&&o.restore(n,e.index)):(r.full(e.index,o),setTimeout(function(){o.full&&o.full(n,e.index)},100))}),o.end&&(a.end[e.index]=o.end)},a.reselect=function(){i.each(i("select"),function(t,e){var n=i(this);n.parents("."+l[0])[0]||1==n.attr("layer")&&i("."+l[0]).length<1&&n.removeAttr("layer").show(),n=null})},s.pt.IE6=function(t){i("select").each(function(t,e){var n=i(this);n.parents("."+l[0])[0]||"none"===n.css("display")||n.attr({layer:"1"}).hide(),n=null})},s.pt.openLayer=function(){var t=this;r.zIndex=t.config.zIndex,r.setTop=function(t){var e=function(){r.zIndex++,t.css("z-index",r.zIndex+1)};return r.zIndex=parseInt(t[0].style.zIndex),t.on("mousedown",e),r.zIndex}},a.record=function(t){var e=[t.width(),t.height(),t.position().top,t.position().left+parseFloat(t.css("margin-left"))];t.find(".layui-layer-max").addClass("layui-layer-maxmin"),t.attr({area:e})},a.rescollbar=function(t){l.html.attr("layer-full")==t&&(l.html[0].style.removeProperty?l.html[0].style.removeProperty("overflow"):l.html[0].style.removeAttribute("overflow"),l.html.removeAttr("layer-full"))},t.layer=r,r.getChildFrame=function(t,e){return e=e||i("."+l[4]).attr("times"),i("#"+l[0]+e).find("iframe").contents().find(t)},r.getFrameIndex=function(t){return i("#"+t).parents("."+l[4]).attr("times")},r.iframeAuto=function(t){if(t){var e=r.getChildFrame("html",t).outerHeight(),n=i("#"+l[0]+t),o=n.find(l[1]).outerHeight()||0,a=n.find("."+l[6]).outerHeight()||0;n.css({height:e+o+a}),n.find("iframe").css({height:e})}},r.iframeSrc=function(t,e){i("#"+l[0]+t).find("iframe").attr("src",e)},r.style=function(t,e,n){var o=i("#"+l[0]+t),r=o.find(".layui-layer-content"),s=o.attr("type"),c=o.find(l[1]).outerHeight()||0,d=o.find("."+l[6]).outerHeight()||0;if(o.attr("minLeft"),s!==a.type[3]&&s!==a.type[4])if(n||(parseFloat(e.width)<=260&&(e.width=260),parseFloat(e.height)-c-d<=64&&(e.height=64+c+d)),o.css(e),d=o.find("."+l[6]).outerHeight()||0,s===a.type[2])o.find("iframe").css({height:parseFloat(e.height)-c-d});else{var u="border-box"==r.css("box-sizing");r.css({height:parseFloat(e.height)-c-d-parseFloat(u?0:r.css("padding-top"))-parseFloat(u?0:r.css("padding-bottom"))})}},r.min=function(t,e){e=e||{};var o=i("#"+l[0]+t),s=i("#"+l.SHADE+t),c=o.find(l[1]).outerHeight()||0,d=o.attr("minLeft")||181*a.minIndex+"px",u=o.css("position"),p={width:180,height:c,position:"fixed",overflow:"hidden"};a.record(o),a.minLeft[0]&&(d=a.minLeft[0],a.minLeft.shift()),e.minStack&&(p.left=d,p.top=n.height()-c,o.attr("minLeft")||a.minIndex++,o.attr("minLeft",d)),o.attr("position",u),r.style(t,p,!0),o.find(".layui-layer-min").hide(),"page"===o.attr("type")&&o.find(l[4]).hide(),a.rescollbar(t),s.hide()},r.restore=function(t){var e=i("#"+l[0]+t),n=i("#"+l.SHADE+t),o=e.attr("area").split(",");e.attr("type"),r.style(t,{width:parseFloat(o[0]),height:parseFloat(o[1]),top:parseFloat(o[2]),left:parseFloat(o[3]),position:e.attr("position"),overflow:"visible"},!0),e.find(".layui-layer-max").removeClass("layui-layer-maxmin"),e.find(".layui-layer-min").show(),"page"===e.attr("type")&&e.find(l[4]).show(),a.rescollbar(t),n.show()},r.full=function(t){var e,o=i("#"+l[0]+t);a.record(o),l.html.attr("layer-full")||l.html.css("overflow","hidden").attr("layer-full",t),clearTimeout(e),e=setTimeout(function(){var e="fixed"===o.css("position");r.style(t,{top:e?0:n.scrollTop(),left:e?0:n.scrollLeft(),width:n.width(),height:n.height()},!0),o.find(".layui-layer-min").hide()},100)},r.title=function(t,e){i("#"+l[0]+(e||r.index)).find(l[1]).html(t)},r.close=function(t,e){var n=i("#"+l[0]+t),o=n.attr("type");if(n[0]){var s="layui-layer-wrap",c=function(){if(o===a.type[1]&&"object"===n.attr("conType")){n.children(":not(."+l[5]+")").remove();for(var r=n.find("."+s),c=0;c<2;c++)r.unwrap();r.css("display",r.data("display")).removeClass(s)}else{if(o===a.type[2])try{var d=i("#"+l[4]+t)[0];d.contentWindow.document.write(""),d.contentWindow.close(),n.find("."+l[5])[0].removeChild(d)}catch(t){}n[0].innerHTML="",n.remove()}"function"==typeof a.end[t]&&a.end[t](),delete a.end[t],"function"==typeof e&&e()};n.data("isOutAnim")&&n.addClass("layer-anim layer-anim-close"),i("#layui-layer-moves, #"+l.SHADE+t).remove(),6==r.ie&&a.reselect(),a.rescollbar(t),n.attr("minLeft")&&(a.minIndex--,a.minLeft.push(n.attr("minLeft"))),r.ie&&r.ie<10||!n.data("isOutAnim")?c():setTimeout(function(){c()},200)}},r.closeAll=function(t,e){"function"==typeof t&&(e=t,t=null);var n=i("."+l[0]);i.each(n,function(o){var a=i(this),s=t?a.attr("type")===t:1;s&&r.close(a.attr("times"),o===n.length-1?e:null),s=null}),0===n.length&&"function"==typeof e&&e()};var c=r.cache||{},d=function(t){return c.skin?" "+c.skin+" "+c.skin+"-"+t:""};r.prompt=function(t,e){var o="";if(t=t||{},"function"==typeof t&&(e=t),t.area){var a=t.area;o='style="width: '+a[0]+"; height: "+a[1]+';"',delete t.area}var s,l=2==t.formType?'<textarea class="layui-layer-input"'+o+"></textarea>":function(){return'<input type="'+(1==t.formType?"password":"text")+'" class="layui-layer-input">'}(),c=t.success;return delete t.success,r.open(i.extend({type:1,btn:["&#x786E;&#x5B9A;","&#x53D6;&#x6D88;"],content:l,skin:"layui-layer-prompt"+d("prompt"),maxWidth:n.width(),success:function(e){s=e.find(".layui-layer-input"),s.val(t.value||"").focus(),"function"==typeof c&&c(e)},resize:!1,yes:function(i){var n=s.val();""===n?s.focus():n.length>(t.maxlength||500)?r.tips("&#x6700;&#x591A;&#x8F93;&#x5165;"+(t.maxlength||500)+"&#x4E2A;&#x5B57;&#x6570;",s,{tips:1}):e&&e(n,i,s)}},t))},r.tab=function(t){t=t||{};var e=t.tab||{},n="layui-this",o=t.success;return delete t.success,r.open(i.extend({type:1,skin:"layui-layer-tab"+d("tab"),resize:!1,title:function(){var t=e.length,i=1,o="";if(t>0)for(o='<span class="'+n+'">'+e[0].title+"</span>";i<t;i++)o+="<span>"+e[i].title+"</span>";return o}(),content:'<ul class="layui-layer-tabmain">'+function(){var t=e.length,i=1,o="";if(t>0)for(o='<li class="layui-layer-tabli '+n+'">'+(e[0].content||"no content")+"</li>";i<t;i++)o+='<li class="layui-layer-tabli">'+(e[i].content||"no  content")+"</li>";return o}()+"</ul>",success:function(e){var a=e.find(".layui-layer-title").children(),r=e.find(".layui-layer-tabmain").children();a.on("mousedown",function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0;var o=i(this),a=o.index();o.addClass(n).siblings().removeClass(n),r.eq(a).show().siblings().hide(),"function"==typeof t.change&&t.change(a)}),"function"==typeof o&&o(e)}},t))},r.photos=function(e,n,o){var a={};if(e=e||{},e.photos){e.zoom=void 0===e.zoom||!!e.zoom;var s=!("string"==typeof e.photos||e.photos instanceof i),l=s?e.photos:{},c=l.data||[],u=l.start||0;a.imgIndex=1+(0|u),e.img=e.img||"img";var p=e.success;if(delete e.success,s){if(0===c.length)return r.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")}else{var h=i(e.photos),f=function(){c=[],h.find(e.img).each(function(t){var e=i(this);e.attr("layer-index",t),c.push({alt:e.attr("alt"),pid:e.attr("layer-pid"),src:e.attr("layer-src")||e.attr("src"),thumb:e.attr("src")})})};if(f(),0===c.length)return;if(n||h.on("click",e.img,function(){f();var t=i(this),n=t.attr("layer-index");r.photos(i.extend(e,{photos:{start:n,data:c,tab:e.tab},full:e.full}),!0)}),!n)return}a.imgprev=function(t){a.imgIndex--,a.imgIndex<1&&(a.imgIndex=c.length),a.tabimg(t)},a.imgnext=function(t,e){++a.imgIndex>c.length&&(a.imgIndex=1,e)||a.tabimg(t)},a.keyup=function(t){if(!a.end){var e=t.keyCode;t.preventDefault(),37===e?a.imgprev(!0):39===e?a.imgnext(!0):27===e&&r.close(a.index)}},a.tabimg=function(t){if(!(c.length<=1))return l.start=a.imgIndex-1,r.close(a.index),r.photos(e,!0,t)},a.event=function(){a.bigimg.find(".layui-layer-imgprev").on("click",function(t){t.preventDefault(),a.imgprev(!0)}),a.bigimg.find(".layui-layer-imgnext").on("click",function(t){t.preventDefault(),a.imgnext(!0)}),i(document).on("keyup",a.keyup),e.zoom&&a.bigimg.on("wheel mousewheel",i(">img",a.bigimg),function(t){var e=i(this).offset(),n=t.originalEvent.wheelDelta>0,o=n?"+=":"-=",a=Math.floor(12);return!(!n&&(i(this).width()<50||i(this).height()<50)||(i(this).width(o+24).height(o+24).offset({left:n?e.left-a:e.left+a,top:n?e.top-a:e.top+a}),1))})},a.loadi=r.load(1,{shade:!("shade"in e)&&.9,scrollbar:!1}),function(t,e,i){var n=new Image;return n.src=t,n.complete?e(n):(n.onload=function(){n.onload=null,e(n)},void(n.onerror=function(t){n.onerror=null,i(t)}))}(c[u].src,function(n){r.close(a.loadi),o&&(e.anim=-1),a.index=r.open(i.extend({type:1,id:"layui-layer-photos",area:function(){var o=[n.width,n.height],a=[i(t).width()-100,i(t).height()-100];if(!e.full&&(o[0]>a[0]||o[1]>a[1])){var r=[o[0]/a[0],o[1]/a[1]];r[0]>r[1]?(o[0]=o[0]/r[0],o[1]=o[1]/r[0]):r[0]<r[1]&&(o[0]=o[0]/r[1],o[1]=o[1]/r[1])}return[o[0]+"px",o[1]+"px"]}(),title:!1,shade:.9,shadeClose:!0,closeBtn:!1,move:".layui-layer-phimg img",moveType:1,scrollbar:!1,moveOut:!0,anim:5,isOutAnim:!1,skin:"layui-layer-photos"+d("photos"),content:'<div class="layui-layer-phimg"><img src="'+c[u].src+'" alt="'+(c[u].alt||"")+'" layer-pid="'+c[u].pid+'">'+function(){return c.length>1?'<div class="layui-layer-imgsee"><span class="layui-layer-imguide"><a href="javascript:;" class="layui-layer-iconext layui-layer-imgprev"></a><a href="javascript:;" class="layui-layer-iconext layui-layer-imgnext"></a></span><div class="layui-layer-imgbar" style="display:'+(o?"block":"")+'"><span class="layui-layer-imgtit"><a href="javascript:;">'+(c[u].alt||"")+"</a><em>"+a.imgIndex+" / "+c.length+"</em></span></div></div>":""}()+"</div>",success:function(t,i){a.bigimg=t.find(".layui-layer-phimg"),a.imgsee=t.find(".layui-layer-imgbar"),a.event(t),e.tab&&e.tab(c[u],t),"function"==typeof p&&p(t)},end:function(){a.end=!0,i(document).off("keyup",a.keyup)}},e))},function(){r.close(a.loadi),r.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;",{time:3e4,btn:["&#x4E0B;&#x4E00;&#x5F20;","&#x4E0D;&#x770B;&#x4E86;"],yes:function(){c.length>1&&a.imgnext(!0,!0)}})})}},a.run=function(e){i=e,n=i(t),l.html=i("html"),r.open=function(t){return new s(t).index}},t.layui&&layui.define?(r.ready(),layui.define("jquery",function(e){r.path=layui.cache.dir,a.run(layui.$),t.layer=r,e("layer",r)})):"function"==typeof define&&define.amd?define("layer",["jquery"],function(){return a.run(t.jQuery),r}):function(){r.ready(),a.run(t.jQuery)}()}(window),function(t){t("toastr",["jquery"],function(t){return function(){function e(t,e,i){return f({type:w.error,iconClass:m().iconClasses.error,message:t,optionsOverride:i,title:e})}function i(e,i){return e||(e=m()),v=t("#"+e.containerId),v.length?v:(i&&(v=u(e)),v)}function n(t,e,i){return f({type:w.info,iconClass:m().iconClasses.info,message:t,optionsOverride:i,title:e})}function o(t){y=t}function a(t,e,i){return f({type:w.success,iconClass:m().iconClasses.success,message:t,optionsOverride:i,title:e})}function r(t,e,i){return f({type:w.warning,iconClass:m().iconClasses.warning,message:t,optionsOverride:i,title:e})}function s(t,e){var n=m();v||i(n),d(t,n,e)||c(n)}function l(e){var n=m();if(v||i(n),e&&0===t(":focus",e).length)return void g(e);v.children().length&&v.remove()}function c(e){for(var i=v.children(),n=i.length-1;n>=0;n--)d(t(i[n]),e)}function d(e,i,n){var o=!(!n||!n.force)&&n.force;return!(!e||!o&&0!==t(":focus",e).length)&&(e[i.hideMethod]({duration:i.hideDuration,easing:i.hideEasing,complete:function(){g(e)}}),!0)}function u(e){return v=t("<div/>").attr("id",e.containerId).addClass(e.positionClass),v.appendTo(t(e.target)),v}function p(){return{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1}}function h(t){y&&y(t)}function f(e){function n(t){return null==t&&(t=""),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function o(){var t="";switch(e.iconClass){case"toast-success":case"toast-info":t="polite";break;default:t="assertive"}S.attr("aria-live",t)}function a(){e.iconClass&&S.addClass(_.toastClass).addClass(k)}function r(){_.newestOnTop?v.prepend(S):v.append(S)}function s(){if(e.title){var t=e.title;_.escapeHtml&&(t=n(e.title)),T.append(t).addClass(_.titleClass),S.append(T)}}function l(){if(e.message){var t=e.message;_.escapeHtml&&(t=n(e.message)),D.append(t).addClass(_.messageClass),S.append(D)}}function c(){_.closeButton&&(E.addClass(_.closeClass).attr("role","button"),S.prepend(E))}function d(){_.progressBar&&($.addClass(_.progressClass),S.prepend($))}function u(){_.rtl&&S.addClass("rtl")}function p(e){var i=e&&!1!==_.closeMethod?_.closeMethod:_.hideMethod,n=e&&!1!==_.closeDuration?_.closeDuration:_.hideDuration,o=e&&!1!==_.closeEasing?_.closeEasing:_.hideEasing;if(!t(":focus",S).length||e)return clearTimeout(F.intervalId),S[i]({duration:n,easing:o,complete:function(){g(S),clearTimeout(C),_.onHidden&&"hidden"!==A.state&&_.onHidden(),A.state="hidden",A.endTime=new Date,h(A)}})}function f(){(_.timeOut>0||_.extendedTimeOut>0)&&(C=setTimeout(p,_.extendedTimeOut),F.maxHideTime=parseFloat(_.extendedTimeOut),F.hideEta=(new Date).getTime()+F.maxHideTime)}function y(){clearTimeout(C),F.hideEta=0,S.stop(!0,!0)[_.showMethod]({duration:_.showDuration,easing:_.showEasing})}function w(){var t=(F.hideEta-(new Date).getTime())/F.maxHideTime*100;$.width(t+"%")}var _=m(),k=e.iconClass||_.iconClass;if(void 0!==e.optionsOverride&&(_=t.extend(_,e.optionsOverride),k=e.optionsOverride.iconClass||k),!function(t,e){if(t.preventDuplicates){if(e.message===b)return!0
;b=e.message}return!1}(_,e)){x++,v=i(_,!0);var C=null,S=t("<div/>"),T=t("<div/>"),D=t("<div/>"),$=t("<div/>"),E=t(_.closeHtml),F={intervalId:null,hideEta:null,maxHideTime:null},A={toastId:x,state:"visible",startTime:new Date,options:_,map:e};return function(){a(),s(),l(),c(),d(),u(),r(),o()}(),function(){S.hide(),S[_.showMethod]({duration:_.showDuration,easing:_.showEasing,complete:_.onShown}),_.timeOut>0&&(C=setTimeout(p,_.timeOut),F.maxHideTime=parseFloat(_.timeOut),F.hideEta=(new Date).getTime()+F.maxHideTime,_.progressBar&&(F.intervalId=setInterval(w,10)))}(),function(){_.closeOnHover&&S.hover(y,f),!_.onclick&&_.tapToDismiss&&S.click(p),_.closeButton&&E&&E.click(function(t){t.stopPropagation?t.stopPropagation():void 0!==t.cancelBubble&&!0!==t.cancelBubble&&(t.cancelBubble=!0),_.onCloseClick&&_.onCloseClick(t),p(!0)}),_.onclick&&S.click(function(t){_.onclick(t),p()})}(),h(A),_.debug&&console&&console.log(A),S}}function m(){return t.extend({},p(),_.options)}function g(t){v||(v=i()),t.is(":visible")||(t.remove(),t=null,0===v.children().length&&(v.remove(),b=void 0))}var v,y,b,x=0,w={error:"error",info:"info",success:"success",warning:"warning"},_={clear:s,remove:l,error:e,getContainer:i,info:n,options:{},subscribe:o,success:a,version:"2.1.4",warning:r};return _}()})}("function"==typeof define&&define.amd?define:function(t,e){"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):window.toastr=e(window.jQuery)}),define("fast",["jquery","bootstrap","toastr","layer","lang"],function(t,e,i,n,o){var a={config:{toastr:{closeButton:!0,debug:!1,newestOnTop:!1,progressBar:!1,positionClass:"toast-top-center",preventDuplicates:!1,onclick:null,showDuration:"300",hideDuration:"1000",timeOut:"5000",extendedTimeOut:"1000",showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"}},events:{onAjaxSuccess:function(t,e){var n=void 0!==t.data?t.data:null,o=void 0!==t.msg&&t.msg?t.msg:__("Operation completed");if("function"==typeof e){if(!1===e.call(this,n,t))return}i.success(o)},onAjaxError:function(t,e){var n=void 0!==t.data?t.data:null;if("function"==typeof e){if(!1===e.call(this,n,t))return}i.error(t.msg)},onAjaxResponse:function(e){try{var i="object"==typeof e?e:JSON.parse(e);i.hasOwnProperty("code")||t.extend(i,{code:-2,msg:e,data:null})}catch(t){var i={code:-1,msg:t.message,data:null}}return i}},api:{ajax:function(e,i,o){e="string"==typeof e?{url:e}:e;var r;return(void 0===e.loading||e.loading)&&(r=n.load(e.loading||0)),e=t.extend({type:"POST",dataType:"json",xhrFields:{withCredentials:!0},success:function(t){r&&n.close(r),t=a.events.onAjaxResponse(t),1===t.code?a.events.onAjaxSuccess(t,i):a.events.onAjaxError(t,o)},error:function(t){r&&n.close(r);var e={code:t.status,msg:t.statusText,data:null};a.events.onAjaxError(e,o)}},e),t.ajax(e)},fixurl:function(t){if("/"!==t.substr(0,1)){new RegExp("^(?:[a-z]+:)?//","i").test(t)||(t=Config.moduleurl+"/"+t)}else"/addons/"===t.substr(0,8)&&(t=Config.__PUBLIC__.replace(/(\/*$)/g,"")+t);return t},cdnurl:function(t,e){var i=new RegExp("^((?:[a-z]+:)?\\/\\/|data:image\\/)","i"),n=Config.upload.cdnurl;return void 0!==e&&!0!==e&&0!==n.indexOf("/")||(t=i.test(t)||n&&0===t.indexOf(n)?t:n+t),e&&!i.test(t)&&(e="string"==typeof e?e:location.origin,t=e+t),t},query:function(t,e){if(e||(e=window.location.href),!t)return"";t=t.replace(/[\[\]]/g,"\\$&");var i=new RegExp("[?&/]"+t+"([=/]([^&#/?]*)|&|#|$)"),n=i.exec(e);return n?n[2]?decodeURIComponent(n[2].replace(/\+/g," ")):"":null},open:function(i,o,r){o=r&&r.title?r.title:o||"",i=a.api.fixurl(i),i=i+(i.indexOf("?")>-1?"&":"?")+"dialog=1";var s=a.config.openArea!=e?a.config.openArea:[t(window).width()>800?"800px":"95%",t(window).height()>600?"600px":"95%"],l=r&&"function"==typeof r.success?r.success:t.noop;return r&&"function"==typeof r.success&&delete r.success,r=t.extend({type:2,title:o,shadeClose:!0,shade:!1,maxmin:!0,moveOut:!0,area:s,content:i,zIndex:n.zIndex,success:function(e,i){var o=this;t(e).data("callback",o.callback),n.setTop(e);try{var r=n.getChildFrame("html",i),s=r.find(".layer-footer");if(a.api.layerfooter(e,i,o),s.length>0){var c=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver;if(c){var d=s[0],u=new c(function(t){a.api.layerfooter(e,i,o),t.forEach(function(t){})}),p={attributes:!0,childList:!0,characterData:!0,subtree:!0};u.observe(d,p)}}}catch(t){}t(e).height()>t(window).height()&&n.style(i,{top:0,height:t(window).height()}),l.call(this,e,i)}},r||{}),(t(window).width()<480||/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&top.$(".tab-pane.active").length>0)&&(top.$(".tab-pane.active").length>0?(r.area=[top.$(".tab-pane.active").width()+"px",top.$(".tab-pane.active").height()+"px"],r.offset=[top.$(".tab-pane.active").scrollTop()+"px","0px"]):(r.area=[t(window).width()+"px",t(window).height()+"px"],r.offset=["0px","0px"])),n.open(r)},close:function(t){var i=parent.Layer.getFrameIndex(window.name),n=parent.$("#layui-layer"+i).data("callback");parent.Layer.close(i),"function"==typeof n&&n.call(e,t)},layerfooter:function(e,i,o){var a=n.getChildFrame("html",i),r=a.find(".layer-footer");if(r.length>0){t(".layui-layer-footer",e).remove();var s=t("<div />").addClass("layui-layer-btn layui-layer-footer");s.html(r.html()),0===t(".row",s).length&&t(">",s).wrapAll("<div class='row'></div>"),s.insertAfter(e.find(".layui-layer-content")),s.on("click",".btn",function(){if(!t(this).hasClass("disabled")&&!t(this).parent().hasClass("disabled")){var e=s.find(".btn").index(this);t(".btn:eq("+e+")",r).trigger("click")}});var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).height(e.height()-l-c)}if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream){var l=e.find(".layui-layer-title").outerHeight()||0,c=e.find(".layui-layer-btn").outerHeight()||0;t("iframe",e).parent().css("height",e.height()-l-c),t("iframe",e).css("height","100%")}},success:function(e,i){var o="function"==typeof e;return o&&(i=e),n.msg(__("Operation completed"),t.extend({offset:0,icon:1},o?{}:e),i)},error:function(e,i){var o="function"==typeof e;return o&&(i=e),n.msg(__("Operation failed"),t.extend({offset:0,icon:2},o?{}:e),i)},msg:function(t,e){var i="function"==typeof e?e:function(){void 0!==e&&e&&(location.href=e)};n.msg(t,{time:2e3},i)},escape:function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},toastr:i,layer:n},lang:function(){var t=arguments,e=t[0],i=1;if(e=e.toLowerCase(),void 0!==o&&void 0!==o[e]){if("object"==typeof o[e])return o[e];e=o[e]}else if(e.indexOf("."),1)e=t[0];else{for(var n=e.split("."),a=o[n[0]],i=1;i<n.length&&"object"==typeof(a=void 0!==a[n[i]]?a[n[i]]:"");i++);if("object"==typeof a)return a;e=a}return e.replace(/%((%)|s|d)/g,function(e){var n=null;if(e[2])n=e[2];else{switch(n=t[i],e){case"%d":n=parseFloat(n),isNaN(n)&&(n=0)}i++}return n})},init:function(){t.fn.extend({size:function(){return t(this).length}}),t.ajaxSetup({beforeSend:function(t,e){e.url=a.api.fixurl(e.url)}}),n.config({skin:"layui-layer-fast"}),t(window).keyup(function(e){if(27==e.keyCode&&t(".layui-layer").length>0){var i=0;t(".layui-layer").each(function(){i=Math.max(i,parseInt(t(this).attr("times")))}),i&&n.close(i)}}),i.options=a.config.toastr}};return window.Layer=n,window.Toastr=i,window.__=a.lang,window.Fast=a,a.init(),a}),function(){function t(t){return t.replace(b,"").replace(x,",").replace(w,"").replace(_,"").replace(k,"").split(C)}function e(t){return"'"+t.replace(/('|\\)/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n")+"'"}function i(i,n){function o(t){return p+=t.split(/\n/).length-1,d&&(t=t.replace(/\s+/g," ").replace(/<!--[\w\W]*?-->/g,"")),t&&(t=y[1]+e(t)+y[2]+"\n"),t}function a(e){var i=p;if(c?e=c(e,n):r&&(e=e.replace(/\n/g,function(){return"$line="+ ++p+";"})),0===e.indexOf("=")){var o=u&&!/^=[=#]/.test(e);if(e=e.replace(/^=[=#]?|[\s;]*$/g,""),o){var a=e.replace(/\s*\([^\)]+\)/,"");h[a]||/^(include|print)$/.test(a)||(e="$escape("+e+")")}else e="$string("+e+")";e=y[1]+e+y[2]}return r&&(e="$line="+i+";"+e),v(t(e),function(t){if(t&&!m[t]){var e;e="print"===t?x:"include"===t?w:h[t]?"$utils."+t:f[t]?"$helpers."+t:"$data."+t,_+=t+"="+e+",",m[t]=!0}}),e+"\n"}var r=n.debug,s=n.openTag,l=n.closeTag,c=n.parser,d=n.compress,u=n.escape,p=1,m={$data:1,$filename:1,$utils:1,$helpers:1,$out:1,$line:1},g="".trim,y=g?["$out='';","$out+=",";","$out"]:["$out=[];","$out.push(",");","$out.join('')"],b=g?"$out+=text;return $out;":"$out.push(text);",x="function(){var text=''.concat.apply('',arguments);"+b+"}",w="function(filename,data){data=data||$data;var text=$utils.$include(filename,data,$filename);"+b+"}",_="'use strict';var $utils=this,$helpers=$utils.$helpers,"+(r?"$line=0,":""),k=y[0],C="return new String("+y[3]+");";v(i.split(s),function(t){t=t.split(l);var e=t[0],i=t[1];1===t.length?k+=o(e):(k+=a(e),i&&(k+=o(i)))});var S=_+k+C;r&&(S="try{"+S+"}catch(e){throw {filename:$filename,name:'Render Error',message:e.message,line:$line,source:"+e(i)+".split(/\\n/)[$line-1].replace(/^\\s+/,'')};}");try{var T=new Function("$data","$filename",S);return T.prototype=h,T}catch(t){throw t.temp="function anonymous($data,$filename) {"+S+"}",t}}var n=function(t,e){return"string"==typeof e?g(e,{filename:t}):r(t,e)};n.version="3.0.0",n.config=function(t,e){o[t]=e};var o=n.defaults={openTag:"<%",closeTag:"%>",escape:!0,cache:!0,compress:!1,parser:null},a=n.cache={};n.render=function(t,e){return g(t)(e)};var r=n.renderFile=function(t,e){var i=n.get(t)||m({filename:t,name:"Render Error",message:"Template not found"});return e?i(e):i};n.get=function(t){var e;if(a[t])e=a[t];else if("object"==typeof document){var i=document.getElementById(t);if(i){var n=(i.value||i.innerHTML).replace(/^\s*|\s*$/g,"");e=g(n,{filename:t})}}return e};var s=function(t,e){return"string"!=typeof t&&(e=typeof t,"number"===e?t+="":t="function"===e?s(t.call(t)):""),t},l={"<":"&#60;",">":"&#62;",'"':"&#34;","'":"&#39;","&":"&#38;"},c=function(t){return l[t]},d=function(t){return s(t).replace(/&(?![\w#]+;)|[<>"']/g,c)},u=Array.isArray||function(t){return"[object Array]"==={}.toString.call(t)},p=function(t,e){var i,n;if(u(t))for(i=0,n=t.length;i<n;i++)e.call(t,t[i],i,t);else for(i in t)e.call(t,t[i],i)},h=n.utils={$helpers:{},$include:r,$string:s,$escape:d,$each:p};n.helper=function(t,e){f[t]=e};var f=n.helpers=h.$helpers;n.onerror=function(t){var e="Template Error\n\n";for(var i in t)e+="<"+i+">\n"+t[i]+"\n\n";"object"==typeof console&&console.error(e)};var m=function(t){return n.onerror(t),function(){return"{Template Error}"}},g=n.compile=function(t,e){function n(i){try{return new l(i,s)+""}catch(n){return e.debug?m(n)():(e.debug=!0,g(t,e)(i))}}e=e||{};for(var r in o)void 0===e[r]&&(e[r]=o[r]);var s=e.filename;try{var l=i(t,e)}catch(t){return t.filename=s||"anonymous",t.name="Syntax Error",m(t)}return n.prototype=l.prototype,n.toString=function(){return l.toString()},s&&e.cache&&(a[s]=n),n},v=h.$each,y="break,case,catch,continue,debugger,default,delete,do,else,false,finally,for,function,if,in,instanceof,new,null,return,switch,this,throw,true,try,typeof,var,void,while,with,abstract,boolean,byte,char,class,const,double,enum,export,extends,final,float,goto,implements,import,int,interface,long,native,package,private,protected,public,short,static,super,synchronized,throws,transient,volatile,arguments,let,yield,undefined",b=/\/\*[\w\W]*?\*\/|\/\/[^\n]*\n|\/\/[^\n]*$|"(?:[^"\\]|\\[\w\W])*"|'(?:[^'\\]|\\[\w\W])*'|\s*\.\s*[$\w\.]+/g,x=/[^\w$]+/g,w=new RegExp(["\\b"+y.replace(/,/g,"\\b|\\b")+"\\b"].join("|"),"g"),_=/^\d[^,]*|,\d[^,]*/g,k=/^,+|,+$/g,C=/^$|,+/;"object"==typeof exports&&"undefined"!=typeof module?module.exports=n:"function"==typeof define?define("template",[],function(){return n}):this.template=n}(),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("moment/moment",e):t.moment=e()}(this,function(){"use strict";function t(){return Kn.apply(null,arguments)}function e(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function i(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function o(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;var e;for(e in t)if(n(t,e))return!1;return!0}function a(t){return void 0===t}function r(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function s(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function l(t,e){var i,n=[],o=t.length;for(i=0;i<o;++i)n.push(e(t[i],i));return n}function c(t,e){for(var i in e)n(e,i)&&(t[i]=e[i]);return n(e,"toString")&&(t.toString=e.toString),n(e,"valueOf")&&(t.valueOf=e.valueOf),t}function d(t,e,i,n){return ke(t,e,i,n,!0).utc()}function u(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}}function p(t){return null==t._pf&&(t._pf=u()),t._pf}function h(t){var e=null,i=!1,n=t._d&&!isNaN(t._d.getTime());return n&&(e=p(t),i=Zn.call(e.parsedDateParts,function(t){return null!=t}),n=e.overflow<0&&!e.empty&&!e.invalidEra&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&i),t._strict&&(n=n&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour)),null!=Object.isFrozen&&Object.isFrozen(t)?n:(t._isValid=n,t._isValid)}function f(t){var e=d(NaN);return null!=t?c(p(e),t):p(e).userInvalidated=!0,e}function m(t,e){var i,n,o,r=Jn.length;if(a(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),a(e._i)||(t._i=e._i),a(e._f)||(t._f=e._f),a(e._l)||(t._l=e._l),a(e._strict)||(t._strict=e._strict),a(e._tzm)||(t._tzm=e._tzm),a(e._isUTC)||(t._isUTC=e._isUTC),a(e._offset)||(t._offset=e._offset),a(e._pf)||(t._pf=p(e)),a(e._locale)||(t._locale=e._locale),r>0)for(i=0;i<r;i++)n=Jn[i],o=e[n],a(o)||(t[n]=o);return t}function g(e){m(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===to&&(to=!0,t.updateOffset(this),to=!1)}function v(t){return t instanceof g||null!=t&&null!=t._isAMomentObject}function y(e){!1===t.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function b(e,i){var o=!0;return c(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),o){var a,r,s,l=[],c=arguments.length;for(r=0;r<c;r++){if(a="","object"==typeof arguments[r]){a+="\n["+r+"] ";for(s in arguments[0])n(arguments[0],s)&&(a+=s+": "+arguments[0][s]+", ");a=a.slice(0,-2)}else a=arguments[r];l.push(a)}y(e+"\nArguments: "+Array.prototype.slice.call(l).join("")+"\n"+(new Error).stack),o=!1}return i.apply(this,arguments)},i)}function x(e,i){null!=t.deprecationHandler&&t.deprecationHandler(e,i),eo[e]||(y(i),eo[e]=!0)}function w(t){return"undefined"!=typeof Function&&t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function _(t){var e,i;for(i in t)n(t,i)&&(e=t[i],w(e)?this[i]=e:this["_"+i]=e);this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)}function k(t,e){var o,a=c({},t);for(o in e)n(e,o)&&(i(t[o])&&i(e[o])?(a[o]={},c(a[o],t[o]),c(a[o],e[o])):null!=e[o]?a[o]=e[o]:delete a[o]);for(o in t)n(t,o)&&!n(e,o)&&i(t[o])&&(a[o]=c({},a[o]));return a}function C(t){null!=t&&this.set(t)}function S(t,e,i){var n=this._calendar[t]||this._calendar.sameElse;return w(n)?n.call(e,i):n}function T(t,e,i){var n=""+Math.abs(t),o=e-n.length;return(t>=0?i?"+":"":"-")+Math.pow(10,Math.max(0,o)).toString().substr(1)+n}function D(t,e,i,n){var o=n;"string"==typeof n&&(o=function(){return this[n]()}),t&&(lo[t]=o),e&&(lo[e[0]]=function(){return T(o.apply(this,arguments),e[1],e[2])}),i&&(lo[i]=function(){return this.localeData().ordinal(o.apply(this,arguments),t)})}function $(t){return t.match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"")}function E(t){var e,i,n=t.match(ao);for(e=0,i=n.length;e<i;e++)lo[n[e]]?n[e]=lo[n[e]]:n[e]=$(n[e]);return function(e){var o,a="";for(o=0;o<i;o++)a+=w(n[o])?n[o].call(e,t):n[o];return a}}function F(t,e){return t.isValid()?(e=A(e,t.localeData()),so[e]=so[e]||E(e),so[e](t)):t.localeData().invalidDate()}function A(t,e){function i(t){return e.longDateFormat(t)||t}var n=5;for(ro.lastIndex=0;n>=0&&ro.test(t);)t=t.replace(ro,i),ro.lastIndex=0,n-=1;return t}function O(t){var e=this._longDateFormat[t],i=this._longDateFormat[t.toUpperCase()];return e||!i?e:(this._longDateFormat[t]=i.match(ao).map(function(t){return"MMMM"===t||"MM"===t||"DD"===t||"dddd"===t?t.slice(1):t}).join(""),this._longDateFormat[t])}function M(){return this._invalidDate}function L(t){return this._ordinal.replace("%d",t)}function N(t,e,i,n){var o=this._relativeTime[i];return w(o)?o(t,e,i,n):o.replace(/%d/i,t)}function R(t,e){var i=this._relativeTime[t>0?"future":"past"];return w(i)?i(e):i.replace(/%s/i,e)}function P(t){return"string"==typeof t?ho[t]||ho[t.toLowerCase()]:void 0}function I(t){var e,i,o={};for(i in t)n(t,i)&&(e=P(i))&&(o[e]=t[i]);return o}function j(t){var e,i=[];for(e in t)n(t,e)&&i.push({unit:e,priority:fo[e]});return i.sort(function(t,e){return t.priority-e.priority}),i}function H(t,e,i){no[t]=w(e)?e:function(t,n){return t&&i?i:e}}function z(t,e){return n(no,t)?no[t](e._strict,e._locale):new RegExp(Y(t))}function Y(t){return B(t.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,i,n,o){return e||i||n||o}))}function B(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function U(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function q(t){var e=+t,i=0;return 0!==e&&isFinite(e)&&(i=U(e)),i}function W(t,e){var i,n,o=e;for("string"==typeof t&&(t=[t]),r(e)&&(o=function(t,i){i[e]=q(t)}),n=t.length,i=0;i<n;i++)Lo[t[i]]=o}function V(t,e){W(t,function(t,i,n,o){n._w=n._w||{},e(t,n._w,n,o)})}function G(t,e,i){null!=e&&n(Lo,t)&&Lo[t](e,i._a,i,t)}function X(t){return t%4==0&&t%100!=0||t%400==0}function Q(t){return X(t)?366:365}function K(){return X(this.year())}function Z(e,i){return function(n){return null!=n?(tt(this,e,n),t.updateOffset(this,i),this):J(this,e)}}function J(t,e){if(!t.isValid())return NaN;var i=t._d,n=t._isUTC;switch(e){case"Milliseconds":return n?i.getUTCMilliseconds():i.getMilliseconds();case"Seconds":return n?i.getUTCSeconds():i.getSeconds();case"Minutes":return n?i.getUTCMinutes():i.getMinutes();case"Hours":return n?i.getUTCHours():i.getHours();case"Date":return n?i.getUTCDate():i.getDate();case"Day":return n?i.getUTCDay():i.getDay();case"Month":return n?i.getUTCMonth():i.getMonth();case"FullYear":return n?i.getUTCFullYear():i.getFullYear();default:return NaN}}function tt(t,e,i){var n,o,a,r,s;if(t.isValid()&&!isNaN(i)){switch(n=t._d,o=t._isUTC,e){case"Milliseconds":return void(o?n.setUTCMilliseconds(i):n.setMilliseconds(i));case"Seconds":return void(o?n.setUTCSeconds(i):n.setSeconds(i));case"Minutes":return void(o?n.setUTCMinutes(i):n.setMinutes(i));case"Hours":return void(o?n.setUTCHours(i):n.setHours(i));case"Date":return void(o?n.setUTCDate(i):n.setDate(i));case"FullYear":break;default:return}a=i,r=t.month(),s=t.date(),s=29!==s||1!==r||X(a)?s:28,o?n.setUTCFullYear(a,r,s):n.setFullYear(a,r,s)}}function et(t){return t=P(t),w(this[t])?this[t]():this}function it(t,e){if("object"==typeof t){t=I(t);var i,n=j(t),o=n.length;for(i=0;i<o;i++)this[n[i].unit](t[n[i].unit])}else if(t=P(t),w(this[t]))return this[t](e);return this}function nt(t,e){return(t%e+e)%e}function ot(t,e){if(isNaN(t)||isNaN(e))return NaN;var i=nt(e,12);return t+=(e-i)/12,1===i?X(t)?29:28:31-i%7%2}function at(t,i){return t?e(this._months)?this._months[t.month()]:this._months[(this._months.isFormat||Go).test(i)?"format":"standalone"][t.month()]:e(this._months)?this._months:this._months.standalone}function rt(t,i){return t?e(this._monthsShort)?this._monthsShort[t.month()]:this._monthsShort[Go.test(i)?"format":"standalone"][t.month()]:e(this._monthsShort)?this._monthsShort:this._monthsShort.standalone}function st(t,e,i){var n,o,a,r=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],n=0;n<12;++n)a=d([2e3,n]),this._shortMonthsParse[n]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[n]=this.months(a,"").toLocaleLowerCase();return i?"MMM"===e?(o=Uo.call(this._shortMonthsParse,r),-1!==o?o:null):(o=Uo.call(this._longMonthsParse,r),-1!==o?o:null):"MMM"===e?-1!==(o=Uo.call(this._shortMonthsParse,r))?o:(o=Uo.call(this._longMonthsParse,r),-1!==o?o:null):-1!==(o=Uo.call(this._longMonthsParse,r))?o:(o=Uo.call(this._shortMonthsParse,r),-1!==o?o:null)}function lt(t,e,i){var n,o,a;if(this._monthsParseExact)return st.call(this,t,e,i);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),n=0;n<12;n++){if(o=d([2e3,n]),i&&!this._longMonthsParse[n]&&(this._longMonthsParse[n]=new RegExp("^"+this.months(o,"").replace(".","")+"$","i"),this._shortMonthsParse[n]=new RegExp("^"+this.monthsShort(o,"").replace(".","")+"$","i")),i||this._monthsParse[n]||(a="^"+this.months(o,"")+"|^"+this.monthsShort(o,""),this._monthsParse[n]=new RegExp(a.replace(".",""),"i")),i&&"MMMM"===e&&this._longMonthsParse[n].test(t))return n;if(i&&"MMM"===e&&this._shortMonthsParse[n].test(t))return n;if(!i&&this._monthsParse[n].test(t))return n}}function ct(t,e){if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=q(e);else if(e=t.localeData().monthsParse(e),!r(e))return t;var i=e,n=t.date();return n=n<29?n:Math.min(n,ot(t.year(),i)),t._isUTC?t._d.setUTCMonth(i,n):t._d.setMonth(i,n),t}function dt(e){return null!=e?(ct(this,e),t.updateOffset(this,!0),this):J(this,"Month")}function ut(){return ot(this.year(),this.month())}function pt(t){return this._monthsParseExact?(n(this,"_monthsRegex")||ft.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(n(this,"_monthsShortRegex")||(this._monthsShortRegex=Xo),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)}function ht(t){return this._monthsParseExact?(n(this,"_monthsRegex")||ft.call(this),t?this._monthsStrictRegex:this._monthsRegex):(n(this,"_monthsRegex")||(this._monthsRegex=Qo),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)}function ft(){function t(t,e){return e.length-t.length}var e,i,n,o,a=[],r=[],s=[];for(e=0;e<12;e++)i=d([2e3,e]),n=B(this.monthsShort(i,"")),o=B(this.months(i,"")),a.push(n),r.push(o),s.push(o),s.push(n);a.sort(t),r.sort(t),s.sort(t),this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function mt(t,e,i,n,o,a,r){var s;return t<100&&t>=0?(s=new Date(t+400,e,i,n,o,a,r),isFinite(s.getFullYear())&&s.setFullYear(t)):s=new Date(t,e,i,n,o,a,r),s}function gt(t){var e,i;return t<100&&t>=0?(i=Array.prototype.slice.call(arguments),i[0]=t+400,e=new Date(Date.UTC.apply(null,i)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function vt(t,e,i){var n=7+e-i;return-(7+gt(t,0,n).getUTCDay()-e)%7+n-1}function yt(t,e,i,n,o){var a,r,s=(7+i-n)%7,l=vt(t,n,o),c=1+7*(e-1)+s+l;return c<=0?(a=t-1,r=Q(a)+c):c>Q(t)?(a=t+1,r=c-Q(t)):(a=t,r=c),{year:a,dayOfYear:r}}function bt(t,e,i){var n,o,a=vt(t.year(),e,i),r=Math.floor((t.dayOfYear()-a-1)/7)+1;return r<1?(o=t.year()-1,n=r+xt(o,e,i)):r>xt(t.year(),e,i)?(n=r-xt(t.year(),e,i),o=t.year()+1):(o=t.year(),n=r),{week:n,year:o}}function xt(t,e,i){var n=vt(t,e,i),o=vt(t+1,e,i);return(Q(t)-n+o)/7}function wt(t){return bt(t,this._week.dow,this._week.doy).week}function _t(){return this._week.dow}function kt(){return this._week.doy}function Ct(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")}function St(t){var e=bt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")}function Tt(t,e){return"string"!=typeof t?t:isNaN(t)?(t=e.weekdaysParse(t),"number"==typeof t?t:null):parseInt(t,10)}function Dt(t,e){return"string"==typeof t?e.weekdaysParse(t)%7||7:isNaN(t)?null:t}function $t(t,e){return t.slice(e,7).concat(t.slice(0,e))}function Et(t,i){var n=e(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(i)?"format":"standalone"];return!0===t?$t(n,this._week.dow):t?n[t.day()]:n}function Ft(t){return!0===t?$t(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort}function At(t){return!0===t?$t(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin}function Ot(t,e,i){var n,o,a,r=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],n=0;n<7;++n)a=d([2e3,1]).day(n),this._minWeekdaysParse[n]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[n]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[n]=this.weekdays(a,"").toLocaleLowerCase();return i?"dddd"===e?(o=Uo.call(this._weekdaysParse,r),-1!==o?o:null):"ddd"===e?(o=Uo.call(this._shortWeekdaysParse,r),-1!==o?o:null):(o=Uo.call(this._minWeekdaysParse,r),-1!==o?o:null):"dddd"===e?-1!==(o=Uo.call(this._weekdaysParse,r))?o:-1!==(o=Uo.call(this._shortWeekdaysParse,r))?o:(o=Uo.call(this._minWeekdaysParse,r),-1!==o?o:null):"ddd"===e?-1!==(o=Uo.call(this._shortWeekdaysParse,r))?o:-1!==(o=Uo.call(this._weekdaysParse,r))?o:(o=Uo.call(this._minWeekdaysParse,r),-1!==o?o:null):-1!==(o=Uo.call(this._minWeekdaysParse,r))?o:-1!==(o=Uo.call(this._weekdaysParse,r))?o:(o=Uo.call(this._shortWeekdaysParse,r),-1!==o?o:null)}function Mt(t,e,i){var n,o,a;if(this._weekdaysParseExact)return Ot.call(this,t,e,i);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),n=0;n<7;n++){if(o=d([2e3,1]).day(n),i&&!this._fullWeekdaysParse[n]&&(this._fullWeekdaysParse[n]=new RegExp("^"+this.weekdays(o,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[n]=new RegExp("^"+this.weekdaysShort(o,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[n]=new RegExp("^"+this.weekdaysMin(o,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[n]||(a="^"+this.weekdays(o,"")+"|^"+this.weekdaysShort(o,"")+"|^"+this.weekdaysMin(o,""),this._weekdaysParse[n]=new RegExp(a.replace(".",""),"i")),i&&"dddd"===e&&this._fullWeekdaysParse[n].test(t))return n;if(i&&"ddd"===e&&this._shortWeekdaysParse[n].test(t))return n;if(i&&"dd"===e&&this._minWeekdaysParse[n].test(t))return n;if(!i&&this._weekdaysParse[n].test(t))return n}}function Lt(t){if(!this.isValid())return null!=t?this:NaN;var e=J(this,"Day");return null!=t?(t=Tt(t,this.localeData()),this.add(t-e,"d")):e}function Nt(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")}function Rt(t){if(!this.isValid())return null!=t?this:NaN;if(null!=t){var e=Dt(t,this.localeData());return this.day(this.day()%7?e:e-7)}return this.day()||7}function Pt(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||Ht.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(n(this,"_weekdaysRegex")||(this._weekdaysRegex=ea),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)}function It(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||Ht.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(n(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=ia),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)}function jt(t){return this._weekdaysParseExact?(n(this,"_weekdaysRegex")||Ht.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(n(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=na),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)}function Ht(){function t(t,e){return e.length-t.length}var e,i,n,o,a,r=[],s=[],l=[],c=[];for(e=0;e<7;e++)i=d([2e3,1]).day(e),n=B(this.weekdaysMin(i,"")),o=B(this.weekdaysShort(i,"")),a=B(this.weekdays(i,"")),r.push(n),s.push(o),l.push(a),c.push(n),c.push(o),c.push(a);r.sort(t),s.sort(t),l.sort(t),c.sort(t),this._weekdaysRegex=new RegExp("^("+c.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+r.join("|")+")","i")}function zt(){return this.hours()%12||12}function Yt(){return this.hours()||24}function Bt(t,e){D(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function Ut(t,e){return e._meridiemParse}function qt(t){return"p"===(t+"").toLowerCase().charAt(0)}function Wt(t,e,i){return t>11?i?"pm":"PM":i?"am":"AM"}function Vt(t,e){var i,n=Math.min(t.length,e.length);for(i=0;i<n;i+=1)if(t[i]!==e[i])return i;return n}function Gt(t){return t?t.toLowerCase().replace("_","-"):t}function Xt(t){for(var e,i,n,o,a=0;a<t.length;){for(o=Gt(t[a]).split("-"),e=o.length,i=Gt(t[a+1]),i=i?i.split("-"):null;e>0;){if(n=Kt(o.slice(0,e).join("-")))return n;if(i&&i.length>=e&&Vt(o,i)>=e-1)break;e--}a++}return oa}function Qt(t){return!(!t||!t.match("^[^/\\\\]*$"))}function Kt(t){var e,i=null;if(void 0===la[t]&&"undefined"!=typeof module&&module&&module.exports&&Qt(t))try{i=oa._abbr,e=require,e("./locale/"+t),Zt(i)}catch(e){la[t]=null}return la[t]}function Zt(t,e){var i;return t&&(i=a(e)?ee(t):Jt(t,e),i?oa=i:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),oa._abbr}function Jt(t,e){if(null!==e){var i,n=sa;if(e.abbr=t,null!=la[t])x("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),n=la[t]._config;else if(null!=e.parentLocale)if(null!=la[e.parentLocale])n=la[e.parentLocale]._config;else{if(null==(i=Kt(e.parentLocale)))return ca[e.parentLocale]||(ca[e.parentLocale]=[]),ca[e.parentLocale].push({name:t,config:e}),null;n=i._config}return la[t]=new C(k(n,e)),ca[t]&&ca[t].forEach(function(t){Jt(t.name,t.config)}),Zt(t),la[t]}return delete la[t],null}function te(t,e){if(null!=e){var i,n,o=sa;null!=la[t]&&null!=la[t].parentLocale?la[t].set(k(la[t]._config,e)):(n=Kt(t),null!=n&&(o=n._config),e=k(o,e),null==n&&(e.abbr=t),i=new C(e),i.parentLocale=la[t],la[t]=i),Zt(t)}else null!=la[t]&&(null!=la[t].parentLocale?(la[t]=la[t].parentLocale,t===Zt()&&Zt(t)):null!=la[t]&&delete la[t]);return la[t]}function ee(t){var i;if(t&&t._locale&&t._locale._abbr&&(t=t._locale._abbr),!t)return oa;if(!e(t)){if(i=Kt(t))return i;t=[t]}return Xt(t)}function ie(){return io(la)}function ne(t){var e,i=t._a;return i&&-2===p(t).overflow&&(e=i[Ro]<0||i[Ro]>11?Ro:i[Po]<1||i[Po]>ot(i[No],i[Ro])?Po:i[Io]<0||i[Io]>24||24===i[Io]&&(0!==i[jo]||0!==i[Ho]||0!==i[zo])?Io:i[jo]<0||i[jo]>59?jo:i[Ho]<0||i[Ho]>59?Ho:i[zo]<0||i[zo]>999?zo:-1,p(t)._overflowDayOfYear&&(e<No||e>Po)&&(e=Po),p(t)._overflowWeeks&&-1===e&&(e=Yo),p(t)._overflowWeekday&&-1===e&&(e=Bo),p(t).overflow=e),t}function oe(t){var e,i,n,o,a,r,s=t._i,l=da.exec(s)||ua.exec(s),c=ha.length,d=fa.length;if(l){for(p(t).iso=!0,e=0,i=c;e<i;e++)if(ha[e][1].exec(l[1])){o=ha[e][0],n=!1!==ha[e][2];break}if(null==o)return void(t._isValid=!1);if(l[3]){for(e=0,i=d;e<i;e++)if(fa[e][1].exec(l[3])){a=(l[2]||" ")+fa[e][0];break}if(null==a)return void(t._isValid=!1)}if(!n&&null!=a)return void(t._isValid=!1);if(l[4]){
if(!pa.exec(l[4]))return void(t._isValid=!1);r="Z"}t._f=o+(a||"")+(r||""),ge(t)}else t._isValid=!1}function ae(t,e,i,n,o,a){var r=[re(t),Vo.indexOf(e),parseInt(i,10),parseInt(n,10),parseInt(o,10)];return a&&r.push(parseInt(a,10)),r}function re(t){var e=parseInt(t,10);return e<=49?2e3+e:e<=999?1900+e:e}function se(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function le(t,e,i){if(t){if(Jo.indexOf(t)!==new Date(e[0],e[1],e[2]).getDay())return p(i).weekdayMismatch=!0,i._isValid=!1,!1}return!0}function ce(t,e,i){if(t)return va[t];if(e)return 0;var n=parseInt(i,10),o=n%100;return(n-o)/100*60+o}function de(t){var e,i=ga.exec(se(t._i));if(i){if(e=ae(i[4],i[3],i[2],i[5],i[6],i[7]),!le(i[1],e,t))return;t._a=e,t._tzm=ce(i[8],i[9],i[10]),t._d=gt.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),p(t).rfc2822=!0}else t._isValid=!1}function ue(e){var i=ma.exec(e._i);if(null!==i)return void(e._d=new Date(+i[1]));oe(e),!1===e._isValid&&(delete e._isValid,de(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:t.createFromInputFallback(e)))}function pe(t,e,i){return null!=t?t:null!=e?e:i}function he(e){var i=new Date(t.now());return e._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()]}function fe(t){var e,i,n,o,a,r=[];if(!t._d){for(n=he(t),t._w&&null==t._a[Po]&&null==t._a[Ro]&&me(t),null!=t._dayOfYear&&(a=pe(t._a[No],n[No]),(t._dayOfYear>Q(a)||0===t._dayOfYear)&&(p(t)._overflowDayOfYear=!0),i=gt(a,0,t._dayOfYear),t._a[Ro]=i.getUTCMonth(),t._a[Po]=i.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=r[e]=n[e];for(;e<7;e++)t._a[e]=r[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[Io]&&0===t._a[jo]&&0===t._a[Ho]&&0===t._a[zo]&&(t._nextDay=!0,t._a[Io]=0),t._d=(t._useUTC?gt:mt).apply(null,r),o=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[Io]=24),t._w&&void 0!==t._w.d&&t._w.d!==o&&(p(t).weekdayMismatch=!0)}}function me(t){var e,i,n,o,a,r,s,l,c;e=t._w,null!=e.GG||null!=e.W||null!=e.E?(a=1,r=4,i=pe(e.GG,t._a[No],bt(Ce(),1,4).year),n=pe(e.W,1),((o=pe(e.E,1))<1||o>7)&&(l=!0)):(a=t._locale._week.dow,r=t._locale._week.doy,c=bt(Ce(),a,r),i=pe(e.gg,t._a[No],c.year),n=pe(e.w,c.week),null!=e.d?((o=e.d)<0||o>6)&&(l=!0):null!=e.e?(o=e.e+a,(e.e<0||e.e>6)&&(l=!0)):o=a),n<1||n>xt(i,a,r)?p(t)._overflowWeeks=!0:null!=l?p(t)._overflowWeekday=!0:(s=yt(i,n,o,a,r),t._a[No]=s.year,t._dayOfYear=s.dayOfYear)}function ge(e){if(e._f===t.ISO_8601)return void oe(e);if(e._f===t.RFC_2822)return void de(e);e._a=[],p(e).empty=!0;var i,n,o,a,r,s,l,c=""+e._i,d=c.length,u=0;for(o=A(e._f,e._locale).match(ao)||[],l=o.length,i=0;i<l;i++)a=o[i],n=(c.match(z(a,e))||[])[0],n&&(r=c.substr(0,c.indexOf(n)),r.length>0&&p(e).unusedInput.push(r),c=c.slice(c.indexOf(n)+n.length),u+=n.length),lo[a]?(n?p(e).empty=!1:p(e).unusedTokens.push(a),G(a,n,e)):e._strict&&!n&&p(e).unusedTokens.push(a);p(e).charsLeftOver=d-u,c.length>0&&p(e).unusedInput.push(c),e._a[Io]<=12&&!0===p(e).bigHour&&e._a[Io]>0&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[Io]=ve(e._locale,e._a[Io],e._meridiem),s=p(e).era,null!==s&&(e._a[No]=e._locale.erasConvertYear(s,e._a[No])),fe(e),ne(e)}function ve(t,e,i){var n;return null==i?e:null!=t.meridiemHour?t.meridiemHour(e,i):null!=t.isPM?(n=t.isPM(i),n&&e<12&&(e+=12),n||12!==e||(e=0),e):e}function ye(t){var e,i,n,o,a,r,s=!1,l=t._f.length;if(0===l)return p(t).invalidFormat=!0,void(t._d=new Date(NaN));for(o=0;o<l;o++)a=0,r=!1,e=m({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[o],ge(e),h(e)&&(r=!0),a+=p(e).charsLeftOver,a+=10*p(e).unusedTokens.length,p(e).score=a,s?a<n&&(n=a,i=e):(null==n||a<n||r)&&(n=a,i=e,r&&(s=!0));c(t,i||e)}function be(t){if(!t._d){var e=I(t._i),i=void 0===e.day?e.date:e.day;t._a=l([e.year,e.month,i,e.hour,e.minute,e.second,e.millisecond],function(t){return t&&parseInt(t,10)}),fe(t)}}function xe(t){var e=new g(ne(we(t)));return e._nextDay&&(e.add(1,"d"),e._nextDay=void 0),e}function we(t){var i=t._i,n=t._f;return t._locale=t._locale||ee(t._l),null===i||void 0===n&&""===i?f({nullInput:!0}):("string"==typeof i&&(t._i=i=t._locale.preparse(i)),v(i)?new g(ne(i)):(s(i)?t._d=i:e(n)?ye(t):n?ge(t):_e(t),h(t)||(t._d=null),t))}function _e(n){var o=n._i;a(o)?n._d=new Date(t.now()):s(o)?n._d=new Date(o.valueOf()):"string"==typeof o?ue(n):e(o)?(n._a=l(o.slice(0),function(t){return parseInt(t,10)}),fe(n)):i(o)?be(n):r(o)?n._d=new Date(o):t.createFromInputFallback(n)}function ke(t,n,a,r,s){var l={};return!0!==n&&!1!==n||(r=n,n=void 0),!0!==a&&!1!==a||(r=a,a=void 0),(i(t)&&o(t)||e(t)&&0===t.length)&&(t=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=s,l._l=a,l._i=t,l._f=n,l._strict=r,xe(l)}function Ce(t,e,i,n){return ke(t,e,i,n,!1)}function Se(t,i){var n,o;if(1===i.length&&e(i[0])&&(i=i[0]),!i.length)return Ce();for(n=i[0],o=1;o<i.length;++o)i[o].isValid()&&!i[o][t](n)||(n=i[o]);return n}function Te(){return Se("isBefore",[].slice.call(arguments,0))}function De(){return Se("isAfter",[].slice.call(arguments,0))}function $e(t){var e,i,o=!1,a=wa.length;for(e in t)if(n(t,e)&&(-1===Uo.call(wa,e)||null!=t[e]&&isNaN(t[e])))return!1;for(i=0;i<a;++i)if(t[wa[i]]){if(o)return!1;parseFloat(t[wa[i]])!==q(t[wa[i]])&&(o=!0)}return!0}function Ee(){return this._isValid}function Fe(){return Qe(NaN)}function Ae(t){var e=I(t),i=e.year||0,n=e.quarter||0,o=e.month||0,a=e.week||e.isoWeek||0,r=e.day||0,s=e.hour||0,l=e.minute||0,c=e.second||0,d=e.millisecond||0;this._isValid=$e(e),this._milliseconds=+d+1e3*c+6e4*l+1e3*s*60*60,this._days=+r+7*a,this._months=+o+3*n+12*i,this._data={},this._locale=ee(),this._bubble()}function Oe(t){return t instanceof Ae}function Me(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Le(t,e,i){var n,o=Math.min(t.length,e.length),a=Math.abs(t.length-e.length),r=0;for(n=0;n<o;n++)(i&&t[n]!==e[n]||!i&&q(t[n])!==q(e[n]))&&r++;return r+a}function Ne(t,e){D(t,0,0,function(){var t=this.utcOffset(),i="+";return t<0&&(t=-t,i="-"),i+T(~~(t/60),2)+e+T(~~t%60,2)})}function Re(t,e){var i,n,o,a=(e||"").match(t);return null===a?null:(i=a[a.length-1]||[],n=(i+"").match(_a)||["-",0,0],o=60*n[1]+q(n[2]),0===o?0:"+"===n[0]?o:-o)}function Pe(e,i){var n,o;return i._isUTC?(n=i.clone(),o=(v(e)||s(e)?e.valueOf():Ce(e).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+o),t.updateOffset(n,!1),n):Ce(e).local()}function Ie(t){return-Math.round(t._d.getTimezoneOffset())}function je(e,i,n){var o,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"==typeof e){if(null===(e=Re(Eo,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&i&&(o=Ie(this)),this._offset=e,this._isUTC=!0,null!=o&&this.add(o,"m"),a!==e&&(!i||this._changeInProgress?ei(this,Qe(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?a:Ie(this)}function He(t,e){return null!=t?("string"!=typeof t&&(t=-t),this.utcOffset(t,e),this):-this.utcOffset()}function ze(t){return this.utcOffset(0,t)}function Ye(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Ie(this),"m")),this}function Be(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"==typeof this._i){var t=Re($o,this._i);null!=t?this.utcOffset(t):this.utcOffset(0,!0)}return this}function Ue(t){return!!this.isValid()&&(t=t?Ce(t).utcOffset():0,(this.utcOffset()-t)%60==0)}function qe(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function We(){if(!a(this._isDSTShifted))return this._isDSTShifted;var t,e={};return m(e,this),e=we(e),e._a?(t=e._isUTC?d(e._a):Ce(e._a),this._isDSTShifted=this.isValid()&&Le(e._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}function Ve(){return!!this.isValid()&&!this._isUTC}function Ge(){return!!this.isValid()&&this._isUTC}function Xe(){return!!this.isValid()&&(this._isUTC&&0===this._offset)}function Qe(t,e){var i,o,a,s=t,l=null;return Oe(t)?s={ms:t._milliseconds,d:t._days,M:t._months}:r(t)||!isNaN(+t)?(s={},e?s[e]=+t:s.milliseconds=+t):(l=ka.exec(t))?(i="-"===l[1]?-1:1,s={y:0,d:q(l[Po])*i,h:q(l[Io])*i,m:q(l[jo])*i,s:q(l[Ho])*i,ms:q(Me(1e3*l[zo]))*i}):(l=Ca.exec(t))?(i="-"===l[1]?-1:1,s={y:Ke(l[2],i),M:Ke(l[3],i),w:Ke(l[4],i),d:Ke(l[5],i),h:Ke(l[6],i),m:Ke(l[7],i),s:Ke(l[8],i)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(a=Je(Ce(s.from),Ce(s.to)),s={},s.ms=a.milliseconds,s.M=a.months),o=new Ae(s),Oe(t)&&n(t,"_locale")&&(o._locale=t._locale),Oe(t)&&n(t,"_isValid")&&(o._isValid=t._isValid),o}function Ke(t,e){var i=t&&parseFloat(t.replace(",","."));return(isNaN(i)?0:i)*e}function Ze(t,e){var i={};return i.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(i.months,"M").isAfter(e)&&--i.months,i.milliseconds=+e-+t.clone().add(i.months,"M"),i}function Je(t,e){var i;return t.isValid()&&e.isValid()?(e=Pe(e,t),t.isBefore(e)?i=Ze(t,e):(i=Ze(e,t),i.milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}function ti(t,e){return function(i,n){var o,a;return null===n||isNaN(+n)||(x(e,"moment()."+e+"(period, number) is deprecated. Please use moment()."+e+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),a=i,i=n,n=a),o=Qe(i,n),ei(this,o,t),this}}function ei(e,i,n,o){var a=i._milliseconds,r=Me(i._days),s=Me(i._months);e.isValid()&&(o=null==o||o,s&&ct(e,J(e,"Month")+s*n),r&&tt(e,"Date",J(e,"Date")+r*n),a&&e._d.setTime(e._d.valueOf()+a*n),o&&t.updateOffset(e,r||s))}function ii(t){return"string"==typeof t||t instanceof String}function ni(t){return v(t)||s(t)||ii(t)||r(t)||ai(t)||oi(t)||null===t||void 0===t}function oi(t){var e,a,r=i(t)&&!o(t),s=!1,l=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],c=l.length;for(e=0;e<c;e+=1)a=l[e],s=s||n(t,a);return r&&s}function ai(t){var i=e(t),n=!1;return i&&(n=0===t.filter(function(e){return!r(e)&&ii(t)}).length),i&&n}function ri(t){var e,a,r=i(t)&&!o(t),s=!1,l=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(e=0;e<l.length;e+=1)a=l[e],s=s||n(t,a);return r&&s}function si(t,e){var i=t.diff(e,"days",!0);return i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse"}function li(e,i){1===arguments.length&&(arguments[0]?ni(arguments[0])?(e=arguments[0],i=void 0):ri(arguments[0])&&(i=arguments[0],e=void 0):(e=void 0,i=void 0));var n=e||Ce(),o=Pe(n,this).startOf("day"),a=t.calendarFormat(this,o)||"sameElse",r=i&&(w(i[a])?i[a].call(this,n):i[a]);return this.format(r||this.localeData().calendar(a,this,Ce(n)))}function ci(){return new g(this)}function di(t,e){var i=v(t)?t:Ce(t);return!(!this.isValid()||!i.isValid())&&(e=P(e)||"millisecond","millisecond"===e?this.valueOf()>i.valueOf():i.valueOf()<this.clone().startOf(e).valueOf())}function ui(t,e){var i=v(t)?t:Ce(t);return!(!this.isValid()||!i.isValid())&&(e=P(e)||"millisecond","millisecond"===e?this.valueOf()<i.valueOf():this.clone().endOf(e).valueOf()<i.valueOf())}function pi(t,e,i,n){var o=v(t)?t:Ce(t),a=v(e)?e:Ce(e);return!!(this.isValid()&&o.isValid()&&a.isValid())&&(n=n||"()",("("===n[0]?this.isAfter(o,i):!this.isBefore(o,i))&&(")"===n[1]?this.isBefore(a,i):!this.isAfter(a,i)))}function hi(t,e){var i,n=v(t)?t:Ce(t);return!(!this.isValid()||!n.isValid())&&(e=P(e)||"millisecond","millisecond"===e?this.valueOf()===n.valueOf():(i=n.valueOf(),this.clone().startOf(e).valueOf()<=i&&i<=this.clone().endOf(e).valueOf()))}function fi(t,e){return this.isSame(t,e)||this.isAfter(t,e)}function mi(t,e){return this.isSame(t,e)||this.isBefore(t,e)}function gi(t,e,i){var n,o,a;if(!this.isValid())return NaN;if(n=Pe(t,this),!n.isValid())return NaN;switch(o=6e4*(n.utcOffset()-this.utcOffset()),e=P(e)){case"year":a=vi(this,n)/12;break;case"month":a=vi(this,n);break;case"quarter":a=vi(this,n)/3;break;case"second":a=(this-n)/1e3;break;case"minute":a=(this-n)/6e4;break;case"hour":a=(this-n)/36e5;break;case"day":a=(this-n-o)/864e5;break;case"week":a=(this-n-o)/6048e5;break;default:a=this-n}return i?a:U(a)}function vi(t,e){if(t.date()<e.date())return-vi(e,t);var i,n,o=12*(e.year()-t.year())+(e.month()-t.month()),a=t.clone().add(o,"months");return e-a<0?(i=t.clone().add(o-1,"months"),n=(e-a)/(a-i)):(i=t.clone().add(o+1,"months"),n=(e-a)/(i-a)),-(o+n)||0}function yi(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function bi(t){if(!this.isValid())return null;var e=!0!==t,i=e?this.clone().utc():this;return i.year()<0||i.year()>9999?F(i,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):w(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",F(i,"Z")):F(i,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")}function xi(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t,e,i,n,o="moment",a="";return this.isLocal()||(o=0===this.utcOffset()?"moment.utc":"moment.parseZone",a="Z"),t="["+o+'("]',e=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",i="-MM-DD[T]HH:mm:ss.SSS",n=a+'[")]',this.format(t+e+i+n)}function wi(e){e||(e=this.isUtc()?t.defaultFormatUtc:t.defaultFormat);var i=F(this,e);return this.localeData().postformat(i)}function _i(t,e){return this.isValid()&&(v(t)&&t.isValid()||Ce(t).isValid())?Qe({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function ki(t){return this.from(Ce(),t)}function Ci(t,e){return this.isValid()&&(v(t)&&t.isValid()||Ce(t).isValid())?Qe({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()}function Si(t){return this.to(Ce(),t)}function Ti(t){var e;return void 0===t?this._locale._abbr:(e=ee(t),null!=e&&(this._locale=e),this)}function Di(){return this._locale}function $i(t,e){return(t%e+e)%e}function Ei(t,e,i){return t<100&&t>=0?new Date(t+400,e,i)-Aa:new Date(t,e,i).valueOf()}function Fi(t,e,i){return t<100&&t>=0?Date.UTC(t+400,e,i)-Aa:Date.UTC(t,e,i)}function Ai(e){var i,n;if(void 0===(e=P(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Fi:Ei,e){case"year":i=n(this.year(),0,1);break;case"quarter":i=n(this.year(),this.month()-this.month()%3,1);break;case"month":i=n(this.year(),this.month(),1);break;case"week":i=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":i=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":i=n(this.year(),this.month(),this.date());break;case"hour":i=this._d.valueOf(),i-=$i(i+(this._isUTC?0:this.utcOffset()*Ea),Fa);break;case"minute":i=this._d.valueOf(),i-=$i(i,Ea);break;case"second":i=this._d.valueOf(),i-=$i(i,$a)}return this._d.setTime(i),t.updateOffset(this,!0),this}function Oi(e){var i,n;if(void 0===(e=P(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?Fi:Ei,e){case"year":i=n(this.year()+1,0,1)-1;break;case"quarter":i=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":i=n(this.year(),this.month()+1,1)-1;break;case"week":i=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":i=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":i=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":i=this._d.valueOf(),i+=Fa-$i(i+(this._isUTC?0:this.utcOffset()*Ea),Fa)-1;break;case"minute":i=this._d.valueOf(),i+=Ea-$i(i,Ea)-1;break;case"second":i=this._d.valueOf(),i+=$a-$i(i,$a)-1}return this._d.setTime(i),t.updateOffset(this,!0),this}function Mi(){return this._d.valueOf()-6e4*(this._offset||0)}function Li(){return Math.floor(this.valueOf()/1e3)}function Ni(){return new Date(this.valueOf())}function Ri(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]}function Pi(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}}function Ii(){return this.isValid()?this.toISOString():null}function ji(){return h(this)}function Hi(){return c({},p(this))}function zi(){return p(this).overflow}function Yi(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function Bi(e,i){var n,o,a,r=this._eras||ee("en")._eras;for(n=0,o=r.length;n<o;++n){switch(typeof r[n].since){case"string":a=t(r[n].since).startOf("day"),r[n].since=a.valueOf()}switch(typeof r[n].until){case"undefined":r[n].until=1/0;break;case"string":a=t(r[n].until).startOf("day").valueOf(),r[n].until=a.valueOf()}}return r}function Ui(t,e,i){var n,o,a,r,s,l=this.eras();for(t=t.toUpperCase(),n=0,o=l.length;n<o;++n)if(a=l[n].name.toUpperCase(),r=l[n].abbr.toUpperCase(),s=l[n].narrow.toUpperCase(),i)switch(e){case"N":case"NN":case"NNN":if(r===t)return l[n];break;case"NNNN":if(a===t)return l[n];break;case"NNNNN":if(s===t)return l[n]}else if([a,r,s].indexOf(t)>=0)return l[n]}function qi(e,i){var n=e.since<=e.until?1:-1;return void 0===i?t(e.since).year():t(e.since).year()+(i-e.offset)*n}function Wi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].name;if(n[t].until<=i&&i<=n[t].since)return n[t].name}return""}function Vi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].narrow;if(n[t].until<=i&&i<=n[t].since)return n[t].narrow}return""}function Gi(){var t,e,i,n=this.localeData().eras();for(t=0,e=n.length;t<e;++t){if(i=this.clone().startOf("day").valueOf(),n[t].since<=i&&i<=n[t].until)return n[t].abbr;if(n[t].until<=i&&i<=n[t].since)return n[t].abbr}return""}function Xi(){var e,i,n,o,a=this.localeData().eras();for(e=0,i=a.length;e<i;++e)if(n=a[e].since<=a[e].until?1:-1,o=this.clone().startOf("day").valueOf(),a[e].since<=o&&o<=a[e].until||a[e].until<=o&&o<=a[e].since)return(this.year()-t(a[e].since).year())*n+a[e].offset;return this.year()}function Qi(t){return n(this,"_erasNameRegex")||on.call(this),t?this._erasNameRegex:this._erasRegex}function Ki(t){return n(this,"_erasAbbrRegex")||on.call(this),t?this._erasAbbrRegex:this._erasRegex}function Zi(t){return n(this,"_erasNarrowRegex")||on.call(this),t?this._erasNarrowRegex:this._erasRegex}function Ji(t,e){return e.erasAbbrRegex(t)}function tn(t,e){return e.erasNameRegex(t)}function en(t,e){return e.erasNarrowRegex(t)}function nn(t,e){return e._eraYearOrdinalRegex||To}function on(){var t,e,i,n,o,a=[],r=[],s=[],l=[],c=this.eras();for(t=0,e=c.length;t<e;++t)i=B(c[t].name),n=B(c[t].abbr),o=B(c[t].narrow),r.push(i),a.push(n),s.push(o),l.push(i),l.push(n),l.push(o);this._erasRegex=new RegExp("^("+l.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+r.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+s.join("|")+")","i")}function an(t,e){D(0,[t,t.length],0,e)}function rn(t){return pn.call(this,t,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)}function sn(t){return pn.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)}function ln(){return xt(this.year(),1,4)}function cn(){return xt(this.isoWeekYear(),1,4)}function dn(){var t=this.localeData()._week;return xt(this.year(),t.dow,t.doy)}function un(){var t=this.localeData()._week;return xt(this.weekYear(),t.dow,t.doy)}function pn(t,e,i,n,o){var a;return null==t?bt(this,n,o).year:(a=xt(t,n,o),e>a&&(e=a),hn.call(this,t,e,i,n,o))}function hn(t,e,i,n,o){var a=yt(t,e,i,n,o),r=gt(a.year,0,a.dayOfYear);return this.year(r.getUTCFullYear()),this.month(r.getUTCMonth()),this.date(r.getUTCDate()),this}function fn(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)}function mn(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")}function gn(t,e){e[zo]=q(1e3*("0."+t))}function vn(){return this._isUTC?"UTC":""}function yn(){return this._isUTC?"Coordinated Universal Time":""}function bn(t){return Ce(1e3*t)}function xn(){return Ce.apply(null,arguments).parseZone()}function wn(t){return t}function _n(t,e,i,n){var o=ee(),a=d().set(n,e);return o[i](a,t)}function kn(t,e,i){if(r(t)&&(e=t,t=void 0),t=t||"",null!=e)return _n(t,e,i,"month");var n,o=[];for(n=0;n<12;n++)o[n]=_n(t,n,i,"month");return o}function Cn(t,e,i,n){"boolean"==typeof t?(r(e)&&(i=e,e=void 0),e=e||""):(e=t,i=e,t=!1,r(e)&&(i=e,e=void 0),e=e||"");var o,a=ee(),s=t?a._week.dow:0,l=[];if(null!=i)return _n(e,(i+s)%7,n,"day");for(o=0;o<7;o++)l[o]=_n(e,(o+s)%7,n,"day");return l}function Sn(t,e){return kn(t,e,"months")}function Tn(t,e){return kn(t,e,"monthsShort")}function Dn(t,e,i){return Cn(t,e,i,"weekdays")}function $n(t,e,i){return Cn(t,e,i,"weekdaysShort")}function En(t,e,i){return Cn(t,e,i,"weekdaysMin")}function Fn(){var t=this._data;return this._milliseconds=ja(this._milliseconds),this._days=ja(this._days),this._months=ja(this._months),t.milliseconds=ja(t.milliseconds),t.seconds=ja(t.seconds),t.minutes=ja(t.minutes),t.hours=ja(t.hours),t.months=ja(t.months),t.years=ja(t.years),this}function An(t,e,i,n){var o=Qe(e,i);return t._milliseconds+=n*o._milliseconds,t._days+=n*o._days,t._months+=n*o._months,t._bubble()}function On(t,e){return An(this,t,e,1)}function Mn(t,e){return An(this,t,e,-1)}function Ln(t){return t<0?Math.floor(t):Math.ceil(t)}function Nn(){var t,e,i,n,o,a=this._milliseconds,r=this._days,s=this._months,l=this._data;return a>=0&&r>=0&&s>=0||a<=0&&r<=0&&s<=0||(a+=864e5*Ln(Pn(s)+r),r=0,s=0),l.milliseconds=a%1e3,t=U(a/1e3),l.seconds=t%60,e=U(t/60),l.minutes=e%60,i=U(e/60),l.hours=i%24,r+=U(i/24),o=U(Rn(r)),s+=o,r-=Ln(Pn(o)),n=U(s/12),s%=12,l.days=r,l.months=s,l.years=n,this}function Rn(t){return 4800*t/146097}function Pn(t){return 146097*t/4800}function In(t){if(!this.isValid())return NaN;var e,i,n=this._milliseconds;if("month"===(t=P(t))||"quarter"===t||"year"===t)switch(e=this._days+n/864e5,i=this._months+Rn(e),t){case"month":return i;case"quarter":return i/3;case"year":return i/12}else switch(e=this._days+Math.round(Pn(this._months)),t){case"week":return e/7+n/6048e5;case"day":return e+n/864e5;case"hour":return 24*e+n/36e5;case"minute":return 1440*e+n/6e4;case"second":return 86400*e+n/1e3;case"millisecond":return Math.floor(864e5*e)+n;default:throw new Error("Unknown unit "+t)}}function jn(t){return function(){return this.as(t)}}function Hn(){return Qe(this)}function zn(t){return t=P(t),this.isValid()?this[t+"s"]():NaN}function Yn(t){return function(){return this.isValid()?this._data[t]:NaN}}function Bn(){return U(this.days()/7)}function Un(t,e,i,n,o){return o.relativeTime(e||1,!!i,t,n)}function qn(t,e,i,n){var o=Qe(t).abs(),a=nr(o.as("s")),r=nr(o.as("m")),s=nr(o.as("h")),l=nr(o.as("d")),c=nr(o.as("M")),d=nr(o.as("w")),u=nr(o.as("y")),p=a<=i.ss&&["s",a]||a<i.s&&["ss",a]||r<=1&&["m"]||r<i.m&&["mm",r]||s<=1&&["h"]||s<i.h&&["hh",s]||l<=1&&["d"]||l<i.d&&["dd",l];return null!=i.w&&(p=p||d<=1&&["w"]||d<i.w&&["ww",d]),p=p||c<=1&&["M"]||c<i.M&&["MM",c]||u<=1&&["y"]||["yy",u],p[2]=e,p[3]=+t>0,p[4]=n,Un.apply(null,p)}function Wn(t){return void 0===t?nr:"function"==typeof t&&(nr=t,!0)}function Vn(t,e){return void 0!==or[t]&&(void 0===e?or[t]:(or[t]=e,"s"===t&&(or.ss=e-1),!0))}function Gn(t,e){if(!this.isValid())return this.localeData().invalidDate();var i,n,o=!1,a=or;return"object"==typeof t&&(e=t,t=!1),"boolean"==typeof t&&(o=t),"object"==typeof e&&(a=Object.assign({},or,e),null!=e.s&&null==e.ss&&(a.ss=e.s-1)),i=this.localeData(),n=qn(this,!o,a,i),o&&(n=i.pastFuture(+this,n)),i.postformat(n)}function Xn(t){return(t>0)-(t<0)||+t}function Qn(){if(!this.isValid())return this.localeData().invalidDate();var t,e,i,n,o,a,r,s,l=ar(this._milliseconds)/1e3,c=ar(this._days),d=ar(this._months),u=this.asSeconds();return u?(t=U(l/60),e=U(t/60),l%=60,t%=60,i=U(d/12),d%=12,n=l?l.toFixed(3).replace(/\.?0+$/,""):"",o=u<0?"-":"",a=Xn(this._months)!==Xn(u)?"-":"",r=Xn(this._days)!==Xn(u)?"-":"",s=Xn(this._milliseconds)!==Xn(u)?"-":"",o+"P"+(i?a+i+"Y":"")+(d?a+d+"M":"")+(c?r+c+"D":"")+(e||t||l?"T":"")+(e?s+e+"H":"")+(t?s+t+"M":"")+(l?s+n+"S":"")):"P0D"}var Kn,Zn;Zn=Array.prototype.some?Array.prototype.some:function(t){var e,i=Object(this),n=i.length>>>0;for(e=0;e<n;e++)if(e in i&&t.call(this,i[e],e,i))return!0;return!1};var Jn=t.momentProperties=[],to=!1,eo={};t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var io;io=Object.keys?Object.keys:function(t){var e,i=[];for(e in t)n(t,e)&&i.push(e);return i};var no,oo={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},ao=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ro=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,so={},lo={},co={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},uo=/\d{1,2}/,po={future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ho={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"},fo={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},mo=/\d/,go=/\d\d/,vo=/\d{3}/,yo=/\d{4}/,bo=/[+-]?\d{6}/,xo=/\d\d?/,wo=/\d\d\d\d?/,_o=/\d\d\d\d\d\d?/,ko=/\d{1,3}/,Co=/\d{1,4}/,So=/[+-]?\d{1,6}/,To=/\d+/,Do=/[+-]?\d+/,$o=/Z|[+-]\d\d:?\d\d/gi,Eo=/Z|[+-]\d\d(?::?\d\d)?/gi,Fo=/[+-]?\d+(\.\d{1,3})?/,Ao=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,Oo=/^[1-9]\d?/,Mo=/^([1-9]\d|\d)/;no={};var Lo={},No=0,Ro=1,Po=2,Io=3,jo=4,Ho=5,zo=6,Yo=7,Bo=8;D("Y",0,0,function(){var t=this.year();return t<=9999?T(t,4):"+"+t}),D(0,["YY",2],0,function(){return this.year()%100}),D(0,["YYYY",4],0,"year"),D(0,["YYYYY",5],0,"year"),D(0,["YYYYYY",6,!0],0,"year"),H("Y",Do),H("YY",xo,go),H("YYYY",Co,yo),H("YYYYY",So,bo),H("YYYYYY",So,bo),W(["YYYYY","YYYYYY"],No),W("YYYY",function(e,i){i[No]=2===e.length?t.parseTwoDigitYear(e):q(e)}),W("YY",function(e,i){i[No]=t.parseTwoDigitYear(e)}),W("Y",function(t,e){e[No]=parseInt(t,10)}),t.parseTwoDigitYear=function(t){return q(t)+(q(t)>68?1900:2e3)};var Uo,qo=Z("FullYear",!0);Uo=Array.prototype.indexOf?Array.prototype.indexOf:function(t){var e;for(e=0;e<this.length;++e)if(this[e]===t)return e;return-1},D("M",["MM",2],"Mo",function(){return this.month()+1}),D("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),D("MMMM",0,0,function(t){return this.localeData().months(this,t)}),H("M",xo,Oo),H("MM",xo,go),H("MMM",function(t,e){return e.monthsShortRegex(t)}),H("MMMM",function(t,e){return e.monthsRegex(t)}),W(["M","MM"],function(t,e){e[Ro]=q(t)-1}),W(["MMM","MMMM"],function(t,e,i,n){var o=i._locale.monthsParse(t,n,i._strict);null!=o?e[Ro]=o:p(i).invalidMonth=t});var Wo="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Vo="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Go=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Xo=Ao,Qo=Ao;D("w",["ww",2],"wo","week"),D("W",["WW",2],"Wo","isoWeek"),H("w",xo,Oo),H("ww",xo,go),H("W",xo,Oo),H("WW",xo,go),V(["w","ww","W","WW"],function(t,e,i,n){e[n.substr(0,1)]=q(t)});var Ko={dow:0,doy:6};D("d",0,"do","day"),D("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),D("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),D("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),D("e",0,0,"weekday"),D("E",0,0,"isoWeekday"),H("d",xo),H("e",xo),H("E",xo),H("dd",function(t,e){return e.weekdaysMinRegex(t)}),H("ddd",function(t,e){return e.weekdaysShortRegex(t)}),H("dddd",function(t,e){return e.weekdaysRegex(t)}),V(["dd","ddd","dddd"],function(t,e,i,n){var o=i._locale.weekdaysParse(t,n,i._strict);null!=o?e.d=o:p(i).invalidWeekday=t}),V(["d","e","E"],function(t,e,i,n){e[n]=q(t)});var Zo="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Jo="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),ta="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),ea=Ao,ia=Ao,na=Ao;D("H",["HH",2],0,"hour"),D("h",["hh",2],0,zt),D("k",["kk",2],0,Yt),D("hmm",0,0,function(){return""+zt.apply(this)+T(this.minutes(),2)}),D("hmmss",0,0,function(){return""+zt.apply(this)+T(this.minutes(),2)+T(this.seconds(),2)}),D("Hmm",0,0,function(){return""+this.hours()+T(this.minutes(),2)}),D("Hmmss",0,0,function(){return""+this.hours()+T(this.minutes(),2)+T(this.seconds(),2)}),Bt("a",!0),Bt("A",!1),H("a",Ut),H("A",Ut),H("H",xo,Mo),H("h",xo,Oo),H("k",xo,Oo),H("HH",xo,go),H("hh",xo,go),H("kk",xo,go),H("hmm",wo),H("hmmss",_o),H("Hmm",wo),H("Hmmss",_o),W(["H","HH"],Io),W(["k","kk"],function(t,e,i){var n=q(t);e[Io]=24===n?0:n}),W(["a","A"],function(t,e,i){i._isPm=i._locale.isPM(t),i._meridiem=t}),W(["h","hh"],function(t,e,i){e[Io]=q(t),p(i).bigHour=!0}),W("hmm",function(t,e,i){var n=t.length-2;e[Io]=q(t.substr(0,n)),e[jo]=q(t.substr(n)),p(i).bigHour=!0}),W("hmmss",function(t,e,i){var n=t.length-4,o=t.length-2;e[Io]=q(t.substr(0,n)),e[jo]=q(t.substr(n,2)),e[Ho]=q(t.substr(o)),p(i).bigHour=!0}),W("Hmm",function(t,e,i){var n=t.length-2;e[Io]=q(t.substr(0,n)),e[jo]=q(t.substr(n))}),W("Hmmss",function(t,e,i){var n=t.length-4,o=t.length-2;e[Io]=q(t.substr(0,n)),e[jo]=q(t.substr(n,2)),e[Ho]=q(t.substr(o))});var oa,aa=/[ap]\.?m?\.?/i,ra=Z("Hours",!0),sa={calendar:oo,longDateFormat:co,invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:uo,relativeTime:po,months:Wo,monthsShort:Vo,week:Ko,weekdays:Zo,weekdaysMin:ta,weekdaysShort:Jo,meridiemParse:aa},la={},ca={},da=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ua=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pa=/Z|[+-]\d\d(?::?\d\d)?/,ha=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],fa=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ma=/^\/?Date\((-?\d+)/i,ga=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,va={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,
MST:-420,PDT:-420,PST:-480};t.createFromInputFallback=b("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),t.ISO_8601=function(){},t.RFC_2822=function(){};var ya=b("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ce.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:f()}),ba=b("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=Ce.apply(null,arguments);return this.isValid()&&t.isValid()?t>this?this:t:f()}),xa=function(){return Date.now?Date.now():+new Date},wa=["year","quarter","month","week","day","hour","minute","second","millisecond"];Ne("Z",":"),Ne("ZZ",""),H("Z",Eo),H("ZZ",Eo),W(["Z","ZZ"],function(t,e,i){i._useUTC=!0,i._tzm=Re(Eo,t)});var _a=/([\+\-]|\d\d)/gi;t.updateOffset=function(){};var ka=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Ca=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;Qe.fn=Ae.prototype,Qe.invalid=Fe;var Sa=ti(1,"add"),Ta=ti(-1,"subtract");t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var Da=b("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)}),$a=1e3,Ea=60*$a,Fa=60*Ea,Aa=3506328*Fa;D("N",0,0,"eraAbbr"),D("NN",0,0,"eraAbbr"),D("NNN",0,0,"eraAbbr"),D("NNNN",0,0,"eraName"),D("NNNNN",0,0,"eraNarrow"),D("y",["y",1],"yo","eraYear"),D("y",["yy",2],0,"eraYear"),D("y",["yyy",3],0,"eraYear"),D("y",["yyyy",4],0,"eraYear"),H("N",Ji),H("NN",Ji),H("NNN",Ji),H("NNNN",tn),H("NNNNN",en),W(["N","NN","NNN","NNNN","NNNNN"],function(t,e,i,n){var o=i._locale.erasParse(t,n,i._strict);o?p(i).era=o:p(i).invalidEra=t}),H("y",To),H("yy",To),H("yyy",To),H("yyyy",To),H("yo",nn),W(["y","yy","yyy","yyyy"],No),W(["yo"],function(t,e,i,n){var o;i._locale._eraYearOrdinalRegex&&(o=t.match(i._locale._eraYearOrdinalRegex)),i._locale.eraYearOrdinalParse?e[No]=i._locale.eraYearOrdinalParse(t,o):e[No]=parseInt(t,10)}),D(0,["gg",2],0,function(){return this.weekYear()%100}),D(0,["GG",2],0,function(){return this.isoWeekYear()%100}),an("gggg","weekYear"),an("ggggg","weekYear"),an("GGGG","isoWeekYear"),an("GGGGG","isoWeekYear"),H("G",Do),H("g",Do),H("GG",xo,go),H("gg",xo,go),H("GGGG",Co,yo),H("gggg",Co,yo),H("GGGGG",So,bo),H("ggggg",So,bo),V(["gggg","ggggg","GGGG","GGGGG"],function(t,e,i,n){e[n.substr(0,2)]=q(t)}),V(["gg","GG"],function(e,i,n,o){i[o]=t.parseTwoDigitYear(e)}),D("Q",0,"Qo","quarter"),H("Q",mo),W("Q",function(t,e){e[Ro]=3*(q(t)-1)}),D("D",["DD",2],"Do","date"),H("D",xo,Oo),H("DD",xo,go),H("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),W(["D","DD"],Po),W("Do",function(t,e){e[Po]=q(t.match(xo)[0])});var Oa=Z("Date",!0);D("DDD",["DDDD",3],"DDDo","dayOfYear"),H("DDD",ko),H("DDDD",vo),W(["DDD","DDDD"],function(t,e,i){i._dayOfYear=q(t)}),D("m",["mm",2],0,"minute"),H("m",xo,Mo),H("mm",xo,go),W(["m","mm"],jo);var Ma=Z("Minutes",!1);D("s",["ss",2],0,"second"),H("s",xo,Mo),H("ss",xo,go),W(["s","ss"],Ho);var La=Z("Seconds",!1);D("S",0,0,function(){return~~(this.millisecond()/100)}),D(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),D(0,["SSS",3],0,"millisecond"),D(0,["SSSS",4],0,function(){return 10*this.millisecond()}),D(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),D(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),D(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),D(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),D(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),H("S",ko,mo),H("SS",ko,go),H("SSS",ko,vo);var Na,Ra;for(Na="SSSS";Na.length<=9;Na+="S")H(Na,To);for(Na="S";Na.length<=9;Na+="S")W(Na,gn);Ra=Z("Milliseconds",!1),D("z",0,0,"zoneAbbr"),D("zz",0,0,"zoneName");var Pa=g.prototype;Pa.add=Sa,Pa.calendar=li,Pa.clone=ci,Pa.diff=gi,Pa.endOf=Oi,Pa.format=wi,Pa.from=_i,Pa.fromNow=ki,Pa.to=Ci,Pa.toNow=Si,Pa.get=et,Pa.invalidAt=zi,Pa.isAfter=di,Pa.isBefore=ui,Pa.isBetween=pi,Pa.isSame=hi,Pa.isSameOrAfter=fi,Pa.isSameOrBefore=mi,Pa.isValid=ji,Pa.lang=Da,Pa.locale=Ti,Pa.localeData=Di,Pa.max=ba,Pa.min=ya,Pa.parsingFlags=Hi,Pa.set=it,Pa.startOf=Ai,Pa.subtract=Ta,Pa.toArray=Ri,Pa.toObject=Pi,Pa.toDate=Ni,Pa.toISOString=bi,Pa.inspect=xi,"undefined"!=typeof Symbol&&null!=Symbol.for&&(Pa[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),Pa.toJSON=Ii,Pa.toString=yi,Pa.unix=Li,Pa.valueOf=Mi,Pa.creationData=Yi,Pa.eraName=Wi,Pa.eraNarrow=Vi,Pa.eraAbbr=Gi,Pa.eraYear=Xi,Pa.year=qo,Pa.isLeapYear=K,Pa.weekYear=rn,Pa.isoWeekYear=sn,Pa.quarter=Pa.quarters=fn,Pa.month=dt,Pa.daysInMonth=ut,Pa.week=Pa.weeks=Ct,Pa.isoWeek=Pa.isoWeeks=St,Pa.weeksInYear=dn,Pa.weeksInWeekYear=un,Pa.isoWeeksInYear=ln,Pa.isoWeeksInISOWeekYear=cn,Pa.date=Oa,Pa.day=Pa.days=Lt,Pa.weekday=Nt,Pa.isoWeekday=Rt,Pa.dayOfYear=mn,Pa.hour=Pa.hours=ra,Pa.minute=Pa.minutes=Ma,Pa.second=Pa.seconds=La,Pa.millisecond=Pa.milliseconds=Ra,Pa.utcOffset=je,Pa.utc=ze,Pa.local=Ye,Pa.parseZone=Be,Pa.hasAlignedHourOffset=Ue,Pa.isDST=qe,Pa.isLocal=Ve,Pa.isUtcOffset=Ge,Pa.isUtc=Xe,Pa.isUTC=Xe,Pa.zoneAbbr=vn,Pa.zoneName=yn,Pa.dates=b("dates accessor is deprecated. Use date instead.",Oa),Pa.months=b("months accessor is deprecated. Use month instead",dt),Pa.years=b("years accessor is deprecated. Use year instead",qo),Pa.zone=b("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",He),Pa.isDSTShifted=b("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",We);var Ia=C.prototype;Ia.calendar=S,Ia.longDateFormat=O,Ia.invalidDate=M,Ia.ordinal=L,Ia.preparse=wn,Ia.postformat=wn,Ia.relativeTime=N,Ia.pastFuture=R,Ia.set=_,Ia.eras=Bi,Ia.erasParse=Ui,Ia.erasConvertYear=qi,Ia.erasAbbrRegex=Ki,Ia.erasNameRegex=Qi,Ia.erasNarrowRegex=Zi,Ia.months=at,Ia.monthsShort=rt,Ia.monthsParse=lt,Ia.monthsRegex=ht,Ia.monthsShortRegex=pt,Ia.week=wt,Ia.firstDayOfYear=kt,Ia.firstDayOfWeek=_t,Ia.weekdays=Et,Ia.weekdaysMin=At,Ia.weekdaysShort=Ft,Ia.weekdaysParse=Mt,Ia.weekdaysRegex=Pt,Ia.weekdaysShortRegex=It,Ia.weekdaysMinRegex=jt,Ia.isPM=qt,Ia.meridiem=Wt,Zt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===q(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th")}}),t.lang=b("moment.lang is deprecated. Use moment.locale instead.",Zt),t.langData=b("moment.langData is deprecated. Use moment.localeData instead.",ee);var ja=Math.abs,Ha=jn("ms"),za=jn("s"),Ya=jn("m"),Ba=jn("h"),Ua=jn("d"),qa=jn("w"),Wa=jn("M"),Va=jn("Q"),Ga=jn("y"),Xa=Ha,Qa=Yn("milliseconds"),Ka=Yn("seconds"),Za=Yn("minutes"),Ja=Yn("hours"),tr=Yn("days"),er=Yn("months"),ir=Yn("years"),nr=Math.round,or={ss:44,s:45,m:45,h:22,d:26,w:null,M:11},ar=Math.abs,rr=Ae.prototype;return rr.isValid=Ee,rr.abs=Fn,rr.add=On,rr.subtract=Mn,rr.as=In,rr.asMilliseconds=Ha,rr.asSeconds=za,rr.asMinutes=Ya,rr.asHours=Ba,rr.asDays=Ua,rr.asWeeks=qa,rr.asMonths=Wa,rr.asQuarters=Va,rr.asYears=Ga,rr.valueOf=Xa,rr._bubble=Nn,rr.clone=Hn,rr.get=zn,rr.milliseconds=Qa,rr.seconds=Ka,rr.minutes=Za,rr.hours=Ja,rr.days=tr,rr.weeks=Bn,rr.months=er,rr.years=ir,rr.humanize=Gn,rr.toISOString=Qn,rr.toString=Qn,rr.toJSON=Qn,rr.locale=Ti,rr.localeData=Di,rr.toIsoString=b("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Qn),rr.lang=Da,D("X",0,0,"unix"),D("x",0,0,"valueOf"),H("x",Do),H("X",Fo),W("X",function(t,e,i){i._d=new Date(1e3*parseFloat(t))}),W("x",function(t,e,i){i._d=new Date(q(t))}),t.version="2.30.1",function(t){Kn=t}(Ce),t.fn=Pa,t.min=Te,t.max=De,t.now=xa,t.utc=d,t.unix=bn,t.months=Sn,t.isDate=s,t.locale=Zt,t.invalid=f,t.duration=Qe,t.isMoment=v,t.weekdays=Dn,t.parseZone=xn,t.localeData=ee,t.isDuration=Oe,t.monthsShort=Tn,t.weekdaysMin=En,t.defineLocale=Jt,t.updateLocale=te,t.locales=ie,t.weekdaysShort=$n,t.normalizeUnits=P,t.relativeTimeRounding=Wn,t.relativeTimeThreshold=Vn,t.calendarFormat=si,t.prototype=Pa,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t}),define("moment",["moment/moment"],function(t){return t}),define("frontend",["fast","template","moment"],function(t,e,i){var n={api:t.api,init:function(){var t={};if($(document).on("click",".btn-captcha",function(e){var i=$(this).data("type")?$(this).data("type"):"mobile",o=this;if(n.api.sendcaptcha=function(e,i,o,a){$(e).addClass("disabled",!0).text("发送中..."),n.api.ajax({url:$(e).data("url"),data:o},function(n,o){clearInterval(t[i]);var r=60;t[i]=setInterval(function(){r--,r<=0?(clearInterval(t),$(e).removeClass("disabled").text("发送验证码")):$(e).addClass("disabled").text(r+"秒后可再次发送")},1e3),"function"==typeof a&&a.call(this,n,o)},function(){$(e).removeClass("disabled").text("发送验证码")})},["mobile","email"].indexOf(i)>-1){var a=$(this).data("input-id")?$("#"+$(this).data("input-id")):$("input[name='"+i+"']",$(this).closest("form")),r="email"===i?"邮箱":"手机号码";if(""===a.val())return Layer.msg(r+"不能为空！"),a.focus(),!1;if("mobile"===i&&!a.val().match(/^1[3-9]\d{9}$/))return Layer.msg("请输入正确的"+r+"！"),a.focus(),!1;if("email"===i&&!a.val().match(/^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/))return Layer.msg("请输入正确的"+r+"！"),a.focus(),!1;a.isValid(function(t){if(t){var e={event:$(o).data("event")};e[i]=a.val(),n.api.sendcaptcha(o,i,e)}else Layer.msg("请确认已经输入了正确的"+r+"！")})}else{var s={event:$(o).data("event")};n.api.sendcaptcha(o,i,s,function(t,e){Layer.open({title:!1,area:["400px","430px"],content:"<img src='"+t.image+"' width='400' height='400' /><div class='text-center panel-title'>扫一扫关注公众号获取验证码</div>",type:1})})}return!1}),"ontouchstart"in document.documentElement||$("body").tooltip({selector:'[data-toggle="tooltip"]'}),$("body").popover({selector:'[data-toggle="popover"]'}),"ontouchstart"in document.documentElement){var e,i,o,a,r,s,l;l=$("body",document),l.on("touchstart",function(t){e=t.originalEvent.changedTouches[0].pageX,i=t.originalEvent.changedTouches[0].pageY}),l.on("touchend",function(t){o=t.originalEvent.changedTouches[0].pageX,a=t.originalEvent.changedTouches[0].pageY,r=o-e,s=a-i,r>45?Math.abs(r)-Math.abs(s)>50&&l.addClass("sidebar-open"):r<-45&&Math.abs(r)-Math.abs(s)>50&&l.removeClass("sidebar-open")})}$(document).on("click",".sidebar-toggle",function(){$("body").toggleClass("sidebar-open")})}};return n.api=$.extend(t.api,n.api),window.Template=e,window.Moment=i,window.Frontend=n,n.init(),n}),define("frontend-init",["frontend"],function(t){}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module&&"function"==typeof require?e(require("../moment")):"function"==typeof define&&define.amd?define("moment/locale/zh-cn",["../moment"],e):e(t.moment)}(this,function(t){"use strict";return t.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(t,e){return 12===t&&(t=0),"凌晨"===e||"早上"===e||"上午"===e?t:"下午"===e||"晚上"===e?t+12:t>=11?t:t+12},meridiem:function(t,e,i){var n=100*t+e;return n<600?"凌晨":n<900?"早上":n<1130?"上午":n<1230?"中午":n<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:function(t){return t.week()!==this.week()?"[下]dddLT":"[本]dddLT"},lastDay:"[昨天]LT",lastWeek:function(t){return this.week()!==t.week()?"[上]dddLT":"[本]dddLT"},sameElse:"L"},dayOfMonthOrdinalParse:/\d{1,2}(日|月|周)/,ordinal:function(t,e){switch(e){case"d":case"D":case"DDD":return t+"日";case"M":return t+"月";case"w":case"W":return t+"周";default:return t}},relativeTime:{future:"%s后",past:"%s前",s:"几秒",ss:"%d 秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",w:"1 周",ww:"%d 周",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}})}),function(t){"use strict";function e(t){var e=arguments,i=!0,n=1;return t=t.replace(/%s/g,function(){var t=e[n++];return void 0===t?(i=!1,""):t}),i?t:""}function i(e,i){var n=-1;return t.each(e,function(t,e){return e.field!==i||(n=t,!1)}),n}function n(){var e,i,n;return null===u&&(n=t("<p/>").addClass("fixed-table-scroll-inner"),(e=t("<div/>").addClass("fixed-table-scroll-outer")).append(n),t("body").append(e),i=n[0].offsetWidth,e.css("overflow","scroll"),i===(n=n[0].offsetWidth)&&(n=e[0].clientWidth),e.remove(),u=i-n),u}function o(i,n,o,a){var r,s=n;return"string"==typeof n&&(1<(r=n.split(".")).length?(s=window,t.each(r,function(t,e){s=s[e]})):s=window[n]),"object"==typeof s?s:"function"==typeof s?s.apply(i,o||[]):!s&&"string"==typeof n&&e.apply(this,[n].concat(o))?e.apply(this,[n].concat(o)):a}function a(e,i,n){var o,a=Object.getOwnPropertyNames(e),r=Object.getOwnPropertyNames(i);if(n&&a.length!==r.length)return!1;for(var s=0;s<a.length;s++)if(o=a[s],-1<t.inArray(o,r)&&e[o]!==i[o])return!1;return!0}function r(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t}function s(t){for(var e in t){var i=e.split(/(?=[A-Z])/).join("-").toLowerCase();i!==e&&(t[i]=t[e],delete t[e])}return t}function l(t,e,i){var n=t;if("string"!=typeof e||t.hasOwnProperty(e))return i?r(t[e]):t[e];var o,a=e.split(".");for(o in a)a.hasOwnProperty(o)&&(n=n&&n[a[o]]);return i?r(n):n}function c(){return!!(0<navigator.userAgent.indexOf("MSIE ")||navigator.userAgent.match(/Trident.*rv\:11\./))}function d(e,i){this.options=i,this.$el=t(e),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0,this.init()}var u=null,p=(d.DEFAULTS={classes:"table table-hover",sortClass:void 0,locale:void 0,height:void 0,undefinedText:"-",sortName:void 0,sortOrder:"asc",sortStable:!1,striped:!1,columns:[[]],data:[],totalField:"total",dataField:"rows",method:"get",url:void 0,ajax:void 0,cache:!0,contentType:"application/json",dataType:"json",ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},pagination:!1,onlyInfoPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",search:!1,searchOnEnterKey:!1,strictSearch:!1,searchAlign:"right",selectItemName:"btSelectItem",showHeader:!0,showFooter:!1,showColumns:!1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,buttonsAlign:"right",smartDisplay:!0,escape:!1,minimumCountColumns:1,idField:void 0,uniqueId:void 0,cardView:!1,detailView:!1,detailFormatter:function(t,e){return""},trimOnSearch:!0,clickToSelect:!1,singleSelect:!1,toolbar:void 0,toolbarAlign:"left",checkboxHeader:!0,sortable:!0,silentSort:!0,maintainSelected:!1,searchTimeOut:500,searchText:"",iconSize:void 0,buttonsClass:"default",iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggle:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus"},customSearch:t.noop,customSort:t.noop,rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},footerStyle:function(t,e){return{}},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1}},(d.LOCALES={})["en-US"]=d.LOCALES.en={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(t){return e("%s rows per page",t)},formatShowingRows:function(t,i,n){return e("Showing %s to %s of %s rows",t,i,n)},formatDetailPagination:function(t){return e("Showing %s rows",t)},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"}},t.extend(d.DEFAULTS,d.LOCALES["en-US"]),d.COLUMN_DEFAULTS={radio:!1,checkbox:!1,checkboxEnabled:!0,field:void 0,title:void 0,titleTooltip:void 0,class:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,width:void 0,sortable:!1,order:"asc",visible:!0,switchable:!0,clickToSelect:!0,formatter:void 0,footerFormatter:void 0,events:void 0,sorter:void 0,sortName:void 0,cellStyle:void 0,searchable:!0,searchFormatter:!0,cardVisible:!0,escape:!1},d.EVENTS={"all.bs.table":"onAll","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh"},d.prototype.init=function(){this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initFooter(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()},d.prototype.initLocale=function(){var e;this.options.locale&&((e=this.options.locale.split(/-|_/))[0].toLowerCase(),e[1]&&e[1].toUpperCase(),t.fn.bootstrapTable.locales[this.options.locale]?t.extend(this.options,t.fn.bootstrapTable.locales[this.options.locale]):t.fn.bootstrapTable.locales[e.join("-")]?t.extend(this.options,t.fn.bootstrapTable.locales[e.join("-")]):t.fn.bootstrapTable.locales[e[0]]&&t.extend(this.options,t.fn.bootstrapTable.locales[e[0]]))},d.prototype.initContainer=function(){this.$container=t(['<div class="bootstrap-table">','<div class="fixed-table-toolbar"></div>',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination" style="clear: both;"></div>':"",'<div class="fixed-table-container">','<div class="fixed-table-header"><table></table></div>','<div class="fixed-table-body">','<div class="fixed-table-loading">',this.options.formatLoadingMessage(),"</div>","</div>",'<div class="fixed-table-footer"><table><tr></tr></table></div>',"bottom"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination"></div>':"","</div>","</div>"].join("")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$container.find(".fixed-table-footer"),this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),-1!==t.inArray("table-no-bordered",this.options.classes.split(" "))&&this.$tableContainer.addClass("table-no-bordered")},d.prototype.initTable=function(){for(var e,i,n,o=this,a=[],r=[],l=(this.$header=this.$el.find(">thead"),this.$header.length||(this.$header=t("<thead></thead>").appendTo(this.$el)),this.$header.find("tr").each(function(){var e=[];t(this).find("th").each(function(){void 0!==t(this).data("field")&&t(this).data("field",t(this).data("field")+""),e.push(t.extend({},{title:t(this).html(),class:t(this).attr("class"),titleTooltip:t(this).attr("title"),rowspan:t(this).attr("rowspan")?+t(this).attr("rowspan"):void 0,colspan:t(this).attr("colspan")?+t(this).attr("colspan"):void 0},t(this).data()))}),a.push(e)}),t.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=t.extend(!0,[],a,this.options.columns),this.columns=[],this.options.columns),c=0,u=[],p=0;p<l[0].length;p++)c+=l[0][p].colspan||1;for(p=0;p<l.length;p++)for(u[p]=[],i=0;i<c;i++)u[p][i]=!1;for(p=0;p<l.length;p++)for(i=0;i<l[p].length;i++){var h=l[p][i],f=h.rowspan||1,m=h.colspan||1,g=t.inArray(!1,u[p]);for(1===m&&(h.fieldIndex=g,void 0===h.field)&&(h.field=g),n=0;n<f;n++)u[p+n][g]=!0;for(n=0;n<m;n++)u[p][g+n]=!0}t.each(this.options.columns,function(e,i){t.each(i,function(i,n){void 0!==(n=t.extend({},d.COLUMN_DEFAULTS,n)).fieldIndex&&(o.columns[n.fieldIndex]=n),o.options.columns[e][i]=n})}),this.options.data.length||(e=[],this.$el.find(">tbody>tr").each(function(i){var n={};n._id=t(this).attr("id"),n._class=t(this).attr("class"),n._data=s(t(this).data()),t(this).find(">td").each(function(a){for(var r,l,c=t(this),d=+c.attr("colspan")||1,u=+c.attr("rowspan")||1;e[i]&&e[i][a];a++);for(r=a;r<a+d;r++)for(l=i;l<i+u;l++)e[l]||(e[l]=[]),e[l][r]=!0;c=o.columns[a].field,n[c]=t(this).html(),n["_"+c+"_id"]=t(this).attr("id"),n["_"+c+"_class"]=t(this).attr("class"),n["_"+c+"_rowspan"]=t(this).attr("rowspan"),n["_"+c+"_colspan"]=t(this).attr("colspan"),n["_"+c+"_title"]=t(this).attr("title"),n["_"+c+"_data"]=s(t(this).data())}),r.push(n)}),(this.options.data=r).length&&(this.fromHtml=!0))},d.prototype.initHeader=function(){var i=this,n={},o=[];this.header={fields:[],styles:[],classes:[],formatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},t.each(this.options.columns,function(a,s){o.push("<tr>"),0===a&&!i.options.cardView&&i.options.detailView&&o.push(e('<th class="detail" rowspan="%s"><div class="fht-cell"></div></th>',i.options.columns.length)),t.each(s,function(t,a){var s,l,c,d="",u=e(' class="%s"',a.class),p=(i.options.sortOrder||a.order,"px"),h=a.width;if(void 0===a.width||i.options.cardView||"string"==typeof a.width&&-1!==a.width.indexOf("%")&&(p="%"),a.width&&"string"==typeof a.width&&(h=a.width.replace("%","").replace("px","")),s=e("text-align: %s; ",a.halign||a.align),l=e("text-align: %s; ",a.align),c=e("vertical-align: %s; ",a.valign),c+=e("width: %s; ",!a.checkbox&&!a.radio||h?h?h+p:void 0:"36px"),void 0!==a.fieldIndex){if(i.header.fields[a.fieldIndex]=a.field,i.header.styles[a.fieldIndex]=l+c,i.header.classes[a.fieldIndex]=u,i.header.formatters[a.fieldIndex]=a.formatter,i.header.events[a.fieldIndex]=a.events,i.header.sorters[a.fieldIndex]=a.sorter,i.header.sortNames[a.fieldIndex]=a.sortName,i.header.cellStyles[a.fieldIndex]=a.cellStyle,i.header.searchables[a.fieldIndex]=a.searchable,!a.visible)return;if(i.options.cardView&&!a.cardVisible)return;n[a.field]=a}o.push("<th"+e(' title="%s"',a.titleTooltip),a.checkbox||a.radio?e(' class="bs-checkbox %s"',a.class||""):u,e(' style="%s"',s+c),e(' rowspan="%s"',a.rowspan),e(' colspan="%s"',a.colspan),e(' data-field="%s"',a.field),">"),o.push(e('<div class="th-inner %s">',i.options.sortable&&a.sortable?"sortable both":"")),d=i.options.escape?r(a.title):a.title,a.checkbox&&(!i.options.singleSelect&&i.options.checkboxHeader&&(d='<input name="btSelectAll" type="checkbox" />'),i.header.stateField=a.field),a.radio&&(d="",i.header.stateField=a.field,i.options.singleSelect=!0),o.push(d),o.push("</div>"),o.push('<div class="fht-cell"></div>'),o.push("</div>"),o.push("</th>")}),o.push("</tr>")}),this.$header.html(o.join("")),this.$header.find("th[data-field]").each(function(e){t(this).data(n[t(this).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(e){var n=t(this);if(i.options.detailView&&n.closest(".bootstrap-table")[0]!==i.$container[0])return!1;i.options.sortable&&n.parent().data().sortable&&i.onSort(e)}),this.$header.children().children().off("keypress").on("keypress",function(e){i.options.sortable&&t(this).data().sortable&&13==(e.keyCode||e.which)&&i.onSort(e)}),t(window).off("resize.bootstrap-table"),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on("resize.bootstrap-table",t.proxy(this.resetWidth,this))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(){var e=t(this).prop("checked");i[e?"checkAll":"uncheckAll"](),i.updateSelected()})},d.prototype.initFooter=function(){!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()},d.prototype.initData=function(t,e){this.data="append"===e?this.data.concat(t):"prepend"===e?[].concat(t).concat(this.data):t||this.options.data,this.options.data="append"===e?this.options.data.concat(t):"prepend"===e?[].concat(t).concat(this.options.data):this.data,"server"!==this.options.sidePagination&&this.initSort()},d.prototype.initSort=function(){var i=this,n=this.options.sortName,a="desc"===this.options.sortOrder?-1:1,r=t.inArray(this.options.sortName,this.header.fields);this.options.customSort!==t.noop?this.options.customSort.apply(this,[this.options.sortName,this.options.sortOrder]):-1!==r&&(this.options.sortStable&&t.each(this.data,function(t,e){e.hasOwnProperty("_position")||(e._position=t)}),this.data.sort(function(e,s){i.header.sortNames[r]&&(n=i.header.sortNames[r]);var c=l(e,n,i.options.escape),d=l(s,n,i.options.escape),u=o(i.header,i.header.sorters[r],[c,d]);return void 0!==u?a*u:(null==c&&(c=""),null==d&&(d=""),i.options.sortStable&&c===d&&(c=e._position,d=s._position),t.isNumeric(c)&&t.isNumeric(d)?(c=parseFloat(c))<(d=parseFloat(d))?-1*a:a:c===d?0:-1===(c="string"!=typeof c?c.toString():c).localeCompare(d)?-1*a:a)}),void 0!==this.options.sortClass)&&(clearTimeout(0),setTimeout(function(){i.$el.removeClass(i.options.sortClass);var t=i.$header.find(e('[data-field="%s"]',i.options.sortName).index()+1);i.$el.find(e("tr td:nth-child(%s)",t)).addClass(i.options.sortClass)},250))},d.prototype.onSort=function(e){var e="keypress"===e.type?t(e.currentTarget):t(e.currentTarget).parent(),i=this.$header.find("th").eq(e.index());this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===e.data("field")?this.options.sortOrder="asc"===this.options.sortOrder?"desc":"asc":(this.options.sortName=e.data("field"),this.options.sortOrder="asc"===e.data("order")?"desc":"asc"),this.trigger("sort",this.options.sortName,this.options.sortOrder),e.add(i).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination?this.initServer(this.options.silentSort):(this.initSort(),this.initBody())},d.prototype.initToolbar=function(){var i,n=this,a=[],r=0,s=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(this.options.toolbar)),this.$toolbar.html(""),"string"!=typeof this.options.toolbar&&"object"!=typeof this.options.toolbar||t(e('<div class="bs-bars pull-%s"></div>',this.options.toolbarAlign)).appendTo(this.$toolbar).append(t(this.options.toolbar)),a=[e('<div class="columns columns-%s btn-group pull-%s">',this.options.buttonsAlign,this.options.buttonsAlign)],"string"==typeof this.options.icons&&(this.options.icons=o(null,this.options.icons)),this.options.showPaginationSwitch&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="paginationSwitch" aria-label="pagination Switch" title="%s">',this.options.formatPaginationSwitch()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.paginationSwitchDown),"</button>"),this.options.showRefresh&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="refresh" aria-label="refresh" title="%s">',this.options.formatRefresh()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.refresh),"</button>"),this.options.showToggle&&a.push(e('<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" type="button" name="toggle" aria-label="toggle" title="%s">',this.options.formatToggle()),e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.toggle),"</button>"),this.options.showColumns&&(a.push(e('<div class="keep-open btn-group" title="%s">',this.options.formatColumns()),'<button type="button" aria-label="columns" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.columns),' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'),t.each(this.columns,function(t,i){var o;i.radio||i.checkbox||n.options.cardView&&!i.cardVisible||(o=i.visible?' checked="checked"':"",i.switchable&&(a.push(e('<li role="menuitem"><label><input type="checkbox" data-field="%s" value="%s"%s> %s</label></li>',i.field,t,o,i.title)),s++))}),a.push("</ul>","</div>")),a.push("</div>"),(this.showToolbar||2<a.length)&&this.$toolbar.append(a.join("")),this.options.showPaginationSwitch&&this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click",t.proxy(this.togglePagination,this)),this.options.showRefresh&&this.$toolbar.find('button[name="refresh"]').off("click").on("click",t.proxy(this.refresh,this)),this.options.showToggle&&this.$toolbar.find('button[name="toggle"]').off("click").on("click",function(){n.toggleView()}),this.options.showColumns&&(i=this.$toolbar.find(".keep-open"),s<=this.options.minimumCountColumns&&i.find("input").prop("disabled",!0),i.find("li").off("click").on("click",function(t){t.stopImmediatePropagation()}),i.find("input").off("click").on("click",function(){var e=t(this);n.toggleColumn(t(this).val(),e.prop("checked"),!1),
n.trigger("column-switch",t(this).data("field"),e.prop("checked"))})),this.options.search&&((a=[]).push('<div class="pull-'+this.options.searchAlign+' search">',e('<input class="form-control'+e(" input-%s",this.options.iconSize)+'" type="text" placeholder="%s">',this.options.formatSearch()),"</div>"),this.$toolbar.append(a.join("")),(i=this.$toolbar.find(".search input")).off("keyup drop blur").on("keyup drop blur",function(e){n.options.searchOnEnterKey&&13!==e.keyCode||-1<t.inArray(e.keyCode,[37,38,39,40])||(clearTimeout(r),r=setTimeout(function(){n.onSearch(e)},n.options.searchTimeOut))}),c())&&i.off("mouseup").on("mouseup",function(t){clearTimeout(r),r=setTimeout(function(){n.onSearch(t)},n.options.searchTimeOut)})},d.prototype.onSearch=function(e){var i=t.trim(t(e.currentTarget).val());this.options.trimOnSearch&&t(e.currentTarget).val()!==i&&t(e.currentTarget).val(i),i===this.searchText||""===i&&void 0===this.searchText||(this.searchText=i,this.options.searchText=i,this.options.pageNumber=1,this.initSearch(),this.updatePagination(),this.trigger("search",i))},d.prototype.initSearch=function(){var e,n,a=this;"server"!==this.options.sidePagination&&(this.options.customSearch!==t.noop?this.options.customSearch.apply(this,[this.searchText]):(e=this.searchText&&(this.options.escape?r(this.searchText):this.searchText).toLowerCase(),n=t.isEmptyObject(this.filterColumns)?null:this.filterColumns,this.data=n?t.grep(this.options.data,function(e,i){for(var o in n)if(t.isArray(n[o])&&-1===t.inArray(e[o],n[o])||!t.isArray(n[o])&&e[o]!==n[o])return!1;return!0}):this.options.data,this.data=e?t.grep(this.data,function(n,r){for(var s=0;s<a.header.fields.length;s++)if(a.header.searchables[s]){var l=t.isNumeric(a.header.fields[s])?parseInt(a.header.fields[s],10):a.header.fields[s],c=a.columns[i(a.columns,l)];if("string"==typeof l){for(var d=n,u=l.split("."),p=0;p<u.length;p++)d=d[u[p]];c&&c.searchFormatter&&(d=o(c,a.header.formatters[s],[d,n,r],d))}else d=n[l];if("string"==typeof d||"number"==typeof d)if(a.options.strictSearch){if((d+"").toLowerCase()===e)return!0}else if(-1!==(d+"").toLowerCase().indexOf(e))return!0}return!1}):this.data))},d.prototype.initPagination=function(){if(this.options.pagination){this.$pagination.show();var i,n,o,a,r,s,l,c=this,d=[],u=!1,p=this.getData(),h=this.options.pageList;if("server"!==this.options.sidePagination&&(this.options.totalRows=p.length),this.totalPages=0,this.options.totalRows&&(this.options.pageSize===this.options.formatAllRows()?(this.options.pageSize=this.options.totalRows,u=!0):this.options.pageSize===this.options.totalRows&&(p="string"==typeof this.options.pageList?this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.pageList,-1<t.inArray(this.options.formatAllRows().toLowerCase(),p))&&(u=!0),this.totalPages=1+~~((this.options.totalRows-1)/this.options.pageSize),this.options.totalPages=this.totalPages),0<this.totalPages&&this.options.pageNumber>this.totalPages&&(this.options.pageNumber=this.totalPages),this.pageFrom=(this.options.pageNumber-1)*this.options.pageSize+1,this.pageTo=this.options.pageNumber*this.options.pageSize,this.pageTo>this.options.totalRows&&(this.pageTo=this.options.totalRows),d.push('<div class="pull-'+this.options.paginationDetailHAlign+' pagination-detail">','<span class="pagination-info">',this.options.onlyInfoPagination?this.options.formatDetailPagination(this.options.totalRows):this.options.formatShowingRows(this.pageFrom,this.pageTo,this.options.totalRows),"</span>"),!this.options.onlyInfoPagination){d.push('<span class="page-list">');var f=[e('<span class="btn-group %s">',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?"dropdown":"dropup"),'<button type="button" class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">','<span class="page-size">',u?this.options.formatAllRows():this.options.pageSize,"</span>",' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'];for("string"==typeof this.options.pageList&&(p=this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").split(","),h=[],t.each(p,function(t,e){h.push(e.toUpperCase()===c.options.formatAllRows().toUpperCase()?c.options.formatAllRows():+e)})),t.each(h,function(t,i){(!c.options.smartDisplay||0===t||h[t-1]<c.options.totalRows)&&(t=u?i===c.options.formatAllRows()?' class="active"':"":i===c.options.pageSize?' class="active"':"",f.push(e('<li role="menuitem"%s><a href="#">%s</a></li>',t,i)))}),f.push("</ul></span>"),d.push(this.options.formatRecordsPerPage(f.join(""))),d.push("</span>"),d.push("</div>",'<div class="pull-'+this.options.paginationHAlign+' pagination">','<ul class="pagination'+e(" pagination-%s",this.options.iconSize)+'">','<li class="page-pre"><a href="#">'+this.options.paginationPreText+"</a></li>"),this.totalPages<5?(o=1,n=this.totalPages):(n=4+(o=this.options.pageNumber-2),o<1&&(o=1,n=5),n>this.totalPages&&(o=(n=this.totalPages)-4)),6<=this.totalPages&&(3<=this.options.pageNumber&&(d.push('<li class="page-first'+(1===this.options.pageNumber?" active":"")+'">','<a href="#">',1,"</a>","</li>"),o++),4<=this.options.pageNumber)&&(4==this.options.pageNumber||6==this.totalPages||7==this.totalPages?o--:d.push('<li class="page-first-separator disabled">','<a href="#">...</a>',"</li>"),n--),7<=this.totalPages&&this.options.pageNumber>=this.totalPages-2&&o--,6==this.totalPages?this.options.pageNumber>=this.totalPages-2&&n++:7<=this.totalPages&&(7==this.totalPages||this.options.pageNumber>=this.totalPages-3)&&n++,i=o;i<=n;i++)d.push('<li class="page-number'+(i===this.options.pageNumber?" active":"")+'">','<a href="#">',i,"</a>","</li>");8<=this.totalPages&&this.options.pageNumber<=this.totalPages-4&&d.push('<li class="page-last-separator disabled">','<a href="#">...</a>',"</li>"),6<=this.totalPages&&this.options.pageNumber<=this.totalPages-3&&d.push('<li class="page-last'+(this.totalPages===this.options.pageNumber?" active":"")+'">','<a href="#">',this.totalPages,"</a>","</li>"),d.push('<li class="page-next"><a href="#">'+this.options.paginationNextText+"</a></li>","</ul>","</div>")}this.$pagination.html(d.join("")),this.options.onlyInfoPagination||(p=this.$pagination.find(".page-list a"),o=this.$pagination.find(".page-first"),a=this.$pagination.find(".page-pre"),r=this.$pagination.find(".page-next"),s=this.$pagination.find(".page-last"),l=this.$pagination.find(".page-number"),this.options.smartDisplay&&(this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),(h.length<2||this.options.totalRows<=h[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"]()),this.options.paginationLoop||(1===this.options.pageNumber&&a.addClass("disabled"),this.options.pageNumber===this.totalPages&&r.addClass("disabled")),u&&(this.options.pageSize=this.options.formatAllRows()),p.off("click").on("click",t.proxy(this.onPageListChange,this)),o.off("click").on("click",t.proxy(this.onPageFirst,this)),a.off("click").on("click",t.proxy(this.onPagePre,this)),r.off("click").on("click",t.proxy(this.onPageNext,this)),s.off("click").on("click",t.proxy(this.onPageLast,this)),l.off("click").on("click",t.proxy(this.onPageNumber,this)))}else this.$pagination.hide()},d.prototype.updatePagination=function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainSelected||this.resetRows(),this.initPagination(),"server"===this.options.sidePagination?this.initServer():this.initBody(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize))},d.prototype.onPageListChange=function(e){var i=t(e.currentTarget);return i.parent().addClass("active").siblings().removeClass("active"),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1},d.prototype.onPageFirst=function(t){return this.options.pageNumber=1,this.updatePagination(t),!1},d.prototype.onPagePre=function(t){return this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1},d.prototype.onPageNext=function(t){return this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1},d.prototype.onPageLast=function(t){return this.options.pageNumber=this.totalPages,this.updatePagination(t),!1},d.prototype.onPageNumber=function(e){if(this.options.pageNumber!==+t(e.currentTarget).text())return this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1},d.prototype.initRow=function(i,n,a,s){var c,d=this,u=[],p={},h=[],f="",m={},g=[];if(!(-1<t.inArray(i,this.hiddenRows))){if((p=o(this.options,this.options.rowStyle,[i,n],p))&&p.css)for(c in p.css)h.push(c+": "+p.css[c]);if(m=o(this.options,this.options.rowAttributes,[i,n],m))for(c in m)g.push(e('%s="%s"',c,r(m[c])));return i._data&&!t.isEmptyObject(i._data)&&t.each(i._data,function(t,i){"index"!==t&&(f+=e(' data-%s="%s"',t,i))}),u.push("<tr",e(" %s",g.join(" ")),e(' id="%s"',t.isArray(i)?void 0:i._id),e(' class="%s"',p.classes||(t.isArray(i)?void 0:i._class)),e(' data-index="%s"',n),e(' data-uniqueid="%s"',i[this.options.uniqueId]),e("%s",f),">"),this.options.cardView&&u.push(e('<td colspan="%s"><div class="card-views">',this.header.fields.length)),!this.options.cardView&&this.options.detailView&&u.push("<td>",'<a class="detail-icon" href="#">',e('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.detailOpen),"</a>","</td>"),t.each(this.header.fields,function(a,s){var c="",f=l(i,s,d.options.escape),m="",g={},v="",y=d.header.classes[a],b="",x="",w="",_="",k=d.columns[a];if((!d.fromHtml||void 0!==f)&&k.visible&&(!d.options.cardView||k.cardVisible)){if(k.escape&&(f=r(f)),p=e('style="%s"',h.concat(d.header.styles[a]).join("; ")),i["_"+s+"_id"]&&(v=e(' id="%s"',i["_"+s+"_id"])),i["_"+s+"_class"]&&(y=e(' class="%s"',i["_"+s+"_class"])),i["_"+s+"_rowspan"]&&(x=e(' rowspan="%s"',i["_"+s+"_rowspan"])),i["_"+s+"_colspan"]&&(w=e(' colspan="%s"',i["_"+s+"_colspan"])),i["_"+s+"_title"]&&(_=e(' title="%s"',i["_"+s+"_title"])),(g=o(d.header,d.header.cellStyles[a],[f,i,n,s],g)).classes&&(y=e(' class="%s"',g.classes)),g.css){var C,S=[];for(C in g.css)S.push(C+": "+g.css[C]);p=e('style="%s"',S.concat(d.header.styles[a]).join("; "))}var T,D,$,E,F,m=o(k,d.header.formatters[a],[f,i,n],f);i["_"+s+"_data"]&&!t.isEmptyObject(i["_"+s+"_data"])&&t.each(i["_"+s+"_data"],function(t,i){"index"!==t&&(b+=e(' data-%s="%s"',t,i))}),k.checkbox||k.radio?(T=k.checkbox?"checkbox":"",T=k.radio?"radio":T,c=[e(d.options.cardView?'<div class="card-view %s">':'<td class="bs-checkbox %s">',k.class||""),"<input"+e(' data-index="%s"',n)+e(' name="%s"',d.options.selectItemName)+e(' type="%s"',T)+e(' value="%s"',i[d.options.idField])+e(' checked="%s"',!0===m||f||m&&m.checked?"checked":void 0)+e(' disabled="%s"',!k.checkboxEnabled||m&&m.disabled?"disabled":void 0)+" />",d.header.formatters[a]&&"string"==typeof m?m:"",d.options.cardView?"</div>":"</td>"].join(""),i[d.header.stateField]=!0===m||m&&m.checked):(m=null==m?d.options.undefinedText:m,c=(d.options.cardView?['<div class="card-view">',d.options.showHeader?e('<span class="title" %s>%s</span>',p,(T=d.columns,D="field",$="title",E=s,F="",t.each(T,function(t,e){return e[D]!==E||(F=e[$],!1)}),F)):"",e('<span class="value">%s</span>',m),"</div>"]:[e("<td%s %s %s %s %s %s %s>",v,y,p,b,x,w,_),m,"</td>"]).join(""),d.options.cardView&&d.options.smartDisplay&&""===m&&(c='<div class="card-view"></div>')),u.push(c)}}),this.options.cardView&&u.push("</div></td>"),u.push("</tr>"),u.join(" ")}},d.prototype.initBody=function(n){for(var a=this,r=this.getData(),s=(this.trigger("pre-body",r),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=r.length),t(document.createDocumentFragment())),c=this.pageFrom-1;c<this.pageTo;c++){var d=r[c],d=this.initRow(d,c,r,s),u=u||!!d;d&&!0!==d&&s.append(d)}u||s.append('<tr class="no-records-found">'+e('<td colspan="%s">%s</td>',this.$header.find("th").length,this.options.formatNoMatches())+"</tr>"),this.$body.html(s),n||this.scrollTo(0),this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(n){var o=t(this),r=o.parent(),s=a.data[r.data("index")],c=o[0].cellIndex,c=a.getVisibleFields()[a.options.detailView&&!a.options.cardView?c-1:c],d=a.columns[i(a.columns,c)],u=l(s,c,a.options.escape);o.find(".detail-icon").length||(a.trigger("click"===n.type?"click-cell":"dbl-click-cell",c,u,s,o),a.trigger("click"===n.type?"click-row":"dbl-click-row",s,r,c),"click"===n.type&&a.options.clickToSelect&&d.clickToSelect&&(u=r.find(e('[name="%s"]',a.options.selectItemName))).length&&u[0].click())}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(){var i=t(this),n=i.parent().parent(),s=n.data("index"),l=r[s];return n.next().is("tr.detail-view")?(i.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailOpen)),a.trigger("collapse-row",s,l),n.next().remove()):(i.find("i").attr("class",e("%s %s",a.options.iconsPrefix,a.options.icons.detailClose)),n.after(e('<tr class="detail-view"><td colspan="%s"></td></tr>',n.find("td").length)),i=n.next().find("td"),n=o(a.options,a.options.detailFormatter,[s,l,i],""),1===i.length&&i.append(n),a.trigger("expand-row",s,l,i)),a.resetView(),!1}),this.$selectItem=this.$body.find(e('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(e){e.stopImmediatePropagation();var e=t(this),i=e.prop("checked"),n=a.data[e.data("index")];a.options.maintainSelected&&t(this).is(":radio")&&t.each(a.options.data,function(t,e){e[a.header.stateField]=!1}),n[a.header.stateField]=i,a.options.singleSelect&&(a.$selectItem.not(this).each(function(){a.data[t(this).data("index")][a.header.stateField]=!1}),a.$selectItem.filter(":checked").not(this).prop("checked",!1)),a.updateSelected(),a.trigger(i?"check":"uncheck",n,e)}),t.each(this.header.events,function(e,i){if(i){"string"==typeof i&&(i=o(null,i));var n,r=a.header.fields[e],s=t.inArray(r,a.getVisibleFields());for(n in a.options.detailView&&!a.options.cardView&&(s+=1),i)a.$body.find(">tr:not(.no-records-found)").each(function(){var e=t(this),o=e.find(a.options.cardView?".card-view":">td").eq(s),l=n.indexOf(" "),c=n.substring(0,l),l=n.substring(l+1),d=i[n];o.find(l).off(c).on(c,function(t){var i=e.data("index"),n=a.data[i],o=n[r],s=r.split(".");if(1<s.length)for(var o=n,l=0;l<s.length;l++)o=o[s[l]];d.apply(this,[t,o,n,i])})})}}),this.updateSelected(),this.resetView(),this.trigger("post-body",r)},d.prototype.initServer=function(e,i,n){var a=this,r={},s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};this.options.pagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),(n||this.options.url||this.options.ajax)&&("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination)&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize),t.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),r=o(this.options,this.options.queryParams,[s],r),t.extend(r,i||{}),!1!==r)&&(e||this.$tableLoading.show(),s=t.extend({},o(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(r):r,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t){t=o(a.options,a.options.responseHandler,[t],t),a.load(t),a.trigger("load-success",t),e||a.$tableLoading.hide()},error:function(t){a.trigger("load-error",t.status,t),e||a.$tableLoading.hide()}}),this.options.ajax?o(this,this.options.ajax,[s],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=t.ajax(s)))},d.prototype.initSearchText=function(){var t;this.options.search&&""!==this.options.searchText&&((t=this.$toolbar.find(".search input")).val(this.options.searchText),this.onSearch({currentTarget:t}))},d.prototype.getCaret=function(){var e=this;t.each(this.$header.find("th"),function(i,n){t(n).find(".sortable").removeClass("desc asc").addClass(t(n).data("field")===e.options.sortName?e.options.sortOrder:"both")})},d.prototype.updateSelected=function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each(function(){t(this).closest("tr")[t(this).prop("checked")?"addClass":"removeClass"]("selected")})},d.prototype.updateRows=function(){var e=this;this.$selectItem.each(function(){e.data[t(this).data("index")][e.header.stateField]=t(this).prop("checked")})},d.prototype.resetRows=function(){var e=this;t.each(this.data,function(t,i){e.$selectAll.prop("checked",!1),e.$selectItem.prop("checked",!1),e.header.stateField&&(i[e.header.stateField]=!1)}),this.initHiddenRows()},d.prototype.trigger=function(e){var i=Array.prototype.slice.call(arguments,1);this.options[d.EVENTS[e+=".bs.table"]].apply(this.options,i),this.$el.trigger(t.Event(e),i),this.options.onAll(e,i),this.$el.trigger(t.Event("all.bs.table"),[e,i])},d.prototype.resetHeader=function(){clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(t.proxy(this.fitHeader,this),this.$el.is(":hidden")?100:0)},d.prototype.fitHeader=function(){var i,o,a,r,s=this;s.$el.is(":hidden")?s.timeoutId_=setTimeout(t.proxy(s.fitHeader,s),100):(o=(o=this.$tableBody.get(0)).scrollWidth>o.clientWidth&&o.scrollHeight>o.clientHeight+this.$header.outerHeight()?n():0,this.$el.css("margin-top",-this.$header.outerHeight()),0<(i=t(":focus")).length&&0<(i=i.parents("th")).length&&void 0!==(i=i.attr("data-field"))&&0<(i=this.$header.find("[data-field='"+i+"']")).length&&i.find(":input").addClass("focus-temp"),this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css({"margin-right":o}).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),0<(i=t(".focus-temp:visible:eq(0)")).length&&(i.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(i){s.$header_.find(e('th[data-field="%s"]',t(this).data("field"))).data(t(this).data())}),a=this.getVisibleFields(),r=this.$header_.find("th"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(i){var n=t(this),o=i,i=(s.options.detailView&&!s.options.cardView&&(0===i&&s.$header_.find("th.detail").find(".fht-cell").width(n.innerWidth()),o=i-1),s.$header_.find(e('th[data-field="%s"]',a[o])));(i=1<i.length?t(r[n[0].cellIndex]):i).find(".fht-cell").width(n.innerWidth())}),this.$tableBody.off("scroll").on("scroll",function(){s.$tableHeader.scrollLeft(t(this).scrollLeft()),s.options.showFooter&&!s.options.cardView&&s.$tableFooter.scrollLeft(t(this).scrollLeft())}),s.trigger("post-header"))},d.prototype.resetFooter=function(){var i=this,n=i.getData(),a=[];this.options.showFooter&&!this.options.cardView&&(!this.options.cardView&&this.options.detailView&&a.push('<td><div class="th-inner">&nbsp;</div><div class="fht-cell"></div></td>'),t.each(this.columns,function(t,r){var s,l,c,d,u=[],p=e(' class="%s"',r.class);if(r.visible&&(!i.options.cardView||r.cardVisible)){if(l=e("text-align: %s; ",r.falign||r.align),c=e("vertical-align: %s; ",r.valign),(d=o(null,i.options.footerStyle))&&d.css)for(s in d.css)u.push(s+": "+d.css[s]);a.push("<td",p,e(' style="%s"',l+c+u.concat().join("; ")),">"),a.push('<div class="th-inner">'),a.push(o(r,r.footerFormatter,[n],"&nbsp;")||"&nbsp;"),a.push("</div>"),a.push('<div class="fht-cell"></div>'),a.push("</div>"),a.push("</td>")}}),this.$tableFooter.find("tr").html(a.join("")),this.$tableFooter.show(),clearTimeout(this.timeoutFooter_),this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),this.$el.is(":hidden")?100:0))},d.prototype.fitFooter=function(){var e,i,o;clearTimeout(this.timeoutFooter_),this.$el.is(":hidden")?this.timeoutFooter_=setTimeout(t.proxy(this.fitFooter,this),100):(o=(i=this.$el.css("width"))>this.$tableBody.width()?n():0,this.$tableFooter.css({"margin-right":o}).find("table").css("width",i).attr("class",this.$el.attr("class")),e=this.$tableFooter.find("td"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(i){var n=t(this);e.eq(i).find(".fht-cell").width(n.innerWidth())}))},d.prototype.toggleColumn=function(t,i,n){var o;-1!==t&&(this.columns[t].visible=i,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)&&(o=this.$toolbar.find(".keep-open input").prop("disabled",!1),n&&o.filter(e('[value="%s"]',t)).prop("checked",i),o.filter(":checked").length<=this.options.minimumCountColumns)&&o.filter(":checked").prop("disabled",!0)},d.prototype.getVisibleFields=function(){var e=this,n=[];return t.each(this.header.fields,function(t,o){e.columns[i(e.columns,o)].visible&&n.push(o)}),n},d.prototype.resetView=function(t){var e,i=0;t&&t.height&&(this.options.height=t.height),this.$selectAll.prop("checked",0<this.$selectItem.length&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.options.height&&(t=this.$toolbar.outerHeight(!0),e=this.$pagination.outerHeight(!0),t=this.options.height-t-e,this.$tableContainer.css("height",t+"px")),this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),i+=this.$header.outerHeight()):(this.$tableHeader.hide(),this.trigger("post-header")),this.options.showFooter&&(this.resetFooter(),this.options.height)&&(i+=this.$tableFooter.outerHeight()+1),this.getCaret(),this.$tableContainer.css("padding-bottom",i+"px"),this.trigger("reset-view"))},d.prototype.getData=function(e){return!this.searchText&&t.isEmptyObject(this.filterColumns)&&t.isEmptyObject(this.filterColumnsPartial)?e?this.options.data.slice(this.pageFrom-1,this.pageTo):this.options.data:e?this.data.slice(this.pageFrom-1,this.pageTo):this.data},d.prototype.load=function(e){var i=!1;"server"===this.options.sidePagination?(this.options.totalRows=e[this.options.totalField],i=e.fixedScroll,e=e[this.options.dataField]):t.isArray(e)||(i=e.fixedScroll,e=e.data),this.initData(e),this.initSearch(),this.initPagination(),this.initBody(i)},d.prototype.append=function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.prepend=function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.remove=function(e){var i,n,o=this.options.data.length;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){for(i=o-1;0<=i;i--)(n=this.options.data[i]).hasOwnProperty(e.field)&&-1!==t.inArray(n[e.field],e.values)&&(this.options.data.splice(i,1),"server"===this.options.sidePagination)&&--this.options.totalRows;o!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},d.prototype.removeAll=function(){0<this.options.data.length&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.getRowByUniqueId=function(t){for(var e,i,n=this.options.uniqueId,o=null,a=this.options.data.length-1;0<=a;a--){if((e=this.options.data[a]).hasOwnProperty(n))i=e[n];else{if(!e._data.hasOwnProperty(n))continue;i=e._data[n]}if("string"==typeof i?t=t.toString():"number"==typeof i&&(Number(i)===i&&i%1==0?t=parseInt(t):i===Number(i)&&0!==i&&(t=parseFloat(t))),i===t){o=e;break}}return o},d.prototype.removeByUniqueId=function(t){var e=this.options.data.length,t=this.getRowByUniqueId(t);t&&this.options.data.splice(this.options.data.indexOf(t),1),e!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))},d.prototype.updateByUniqueId=function(e){var i=this,e=t.isArray(e)?e:[e];t.each(e,function(e,n){var o;n.hasOwnProperty("id")&&n.hasOwnProperty("row")&&-1!==(o=t.inArray(i.getRowByUniqueId(n.id),i.options.data))&&t.extend(i.options.data[o],n.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.insertRow=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))},d.prototype.updateRow=function(e){var i=this,e=t.isArray(e)?e:[e];t.each(e,function(e,n){n.hasOwnProperty("index")&&n.hasOwnProperty("row")&&t.extend(i.options.data[n.index],n.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},d.prototype.initHiddenRows=function(){this.hiddenRows=[]},d.prototype.showRow=function(t){this.toggleRow(t,!0)},d.prototype.hideRow=function(t){this.toggleRow(t,!1)},d.prototype.toggleRow=function(e,i){var n;e.hasOwnProperty("index")?n=this.getData()[e.index]:e.hasOwnProperty("uniqueId")&&(n=this.getRowByUniqueId(e.uniqueId)),n&&(e=t.inArray(n,this.hiddenRows),i||-1!==e?i&&-1<e&&this.hiddenRows.splice(e,1):this.hiddenRows.push(n),this.initBody(!0))},d.prototype.getHiddenRows=function(e){var i=this,n=this.getData(),o=[];return t.each(n,function(e,n){-1<t.inArray(n,i.hiddenRows)&&o.push(n)}),this.hiddenRows=o},d.prototype.mergeCells=function(e){var i,n,o=e.index,a=t.inArray(e.field,this.getVisibleFields()),r=e.rowspan||1,s=e.colspan||1,l=this.$body.find(">tr");if(this.options.detailView&&!this.options.cardView&&(a+=1),e=l.eq(o).find(">td").eq(a),!(o<0||a<0||o>=this.data.length)){for(i=o;i<o+r;i++)for(n=a;n<a+s;n++)l.eq(i).find(">td").eq(n).hide();e.attr("rowspan",r).attr("colspan",s).show()}},d.prototype.updateCell=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit)&&(this.initSort(),this.initBody(!0))},d.prototype.getOptions=function(){return this.options},d.prototype.getSelections=function(){var e=this;return t.grep(this.options.data,function(t){return!0===t[e.header.stateField]})},d.prototype.getAllSelections=function(){var e=this;return t.grep(this.options.data,function(t){return t[e.header.stateField]})},d.prototype.checkAll=function(){this.checkAll_(!0)},d.prototype.uncheckAll=function(){this.checkAll_(!1)},d.prototype.checkInvert=function(){var e=this,i=e.$selectItem.filter(":enabled"),n=i.filter(":checked");i.each(function(){t(this).prop("checked",!t(this).prop("checked"))}),e.updateRows(),e.updateSelected(),e.trigger("uncheck-some",n),n=e.getSelections(),e.trigger("check-some",n)},d.prototype.checkAll_=function(t){var e;t||(e=this.getSelections()),this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),t&&(e=this.getSelections()),this.trigger(t?"check-all":"uncheck-all",e)},d.prototype.check=function(t){this.check_(!0,t)},d.prototype.uncheck=function(t){this.check_(!1,t)},d.prototype.check_=function(t,i){var n=this.$selectItem.filter(e('[data-index="%s"]',i)).prop("checked",t);this.data[i][this.header.stateField]=t,this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[i],n)},d.prototype.checkBy=function(t){this.checkBy_(!0,t)},d.prototype.uncheckBy=function(t){this.checkBy_(!1,t)},d.prototype.checkBy_=function(i,n){var o,a;n.hasOwnProperty("field")&&n.hasOwnProperty("values")&&(a=[],t.each((o=this).options.data,function(r,s){if(!s.hasOwnProperty(n.field))return!1;-1!==t.inArray(s[n.field],n.values)&&(r=o.$selectItem.filter(":enabled").filter(e('[data-index="%s"]',r)).prop("checked",i),s[o.header.stateField]=i,a.push(s),o.trigger(i?"check":"uncheck",s,r))}),this.updateSelected(),this.trigger(i?"check-some":"uncheck-some",a))},d.prototype.destroy=function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")},d.prototype.showLoading=function(){this.$tableLoading.show()},d.prototype.hideLoading=function(){this.$tableLoading.hide()},d.prototype.togglePagination=function(){this.options.pagination=!this.options.pagination;var t=this.$toolbar.find('button[name="paginationSwitch"] i');this.options.pagination?t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchDown):t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchUp),this.updatePagination()},d.prototype.refresh=function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.initServer(t&&t.silent,t&&t.query,t&&t.url),this.trigger("refresh",t)},d.prototype.resetWidth=function(){this.options.showHeader&&this.options.height&&this.fitHeader(),this.options.showFooter&&this.fitFooter()},d.prototype.showColumn=function(t){this.toggleColumn(i(this.columns,t),!0,!0)},d.prototype.hideColumn=function(t){this.toggleColumn(i(this.columns,t),!1,!0)},d.prototype.getHiddenColumns=function(){return t.grep(this.columns,function(t){return!t.visible})},d.prototype.getVisibleColumns=function(){return t.grep(this.columns,function(t){return t.visible})},d.prototype.toggleAllColumns=function(e){var i;t.each(this.columns,function(t,i){this.columns[t].visible=e}),this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns&&(i=this.$toolbar.find(".keep-open input").prop("disabled",!1)).filter(":checked").length<=this.options.minimumCountColumns&&i.filter(":checked").prop("disabled",!0)},d.prototype.showAllColumns=function(){this.toggleAllColumns(!0)},d.prototype.hideAllColumns=function(){this.toggleAllColumns(!1)},d.prototype.filterBy=function(e){this.filterColumns=t.isEmptyObject(e)?{}:e,this.options.pageNumber=1,this.initSearch(),this.updatePagination()},d.prototype.scrollTo=function(t){if("number"==typeof(t="string"==typeof t?"bottom"===t?this.$tableBody[0].scrollHeight:0:t)&&this.$tableBody.scrollTop(t),void 0===t)return this.$tableBody.scrollTop()},d.prototype.getScrollPosition=function(){return this.scrollTo()},d.prototype.selectPage=function(t){0<t&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())},d.prototype.prevPage=function(){1<this.options.pageNumber&&(this.options.pageNumber--,this.updatePagination())},d.prototype.nextPage=function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())},d.prototype.toggleView=function(){this.options.cardView=!this.options.cardView,this.initHeader(),this.initBody(),this.trigger("toggle",this.options.cardView)},d.prototype.refreshOptions=function(e){a(this.options,e,!0)||(this.options=t.extend(this.options,e),this.trigger("refresh-options",this.options),this.destroy(),this.init())},d.prototype.resetSearch=function(t){var e=this.$toolbar.find(".search input");e.val(t||""),this.onSearch({currentTarget:e})},d.prototype.expandRow_=function(t,i){i=this.$body.find(e('> tr[data-index="%s"]',i)),i.next().is("tr.detail-view")===!t&&i.find("> td > .detail-icon").click()},d.prototype.expandRow=function(t){this.expandRow_(!0,t)},d.prototype.collapseRow=function(t){this.expandRow_(!1,t)
},d.prototype.expandAllRows=function(i){if(i){var i=this.$body.find(e('> tr[data-index="%s"]',0)),n=this,o=null,a=!1,r=-1;if(i.next().is("tr.detail-view")?i.next().next().is("tr.detail-view")||(i.next().find(".detail-icon").click(),a=!0):(i.find("> td > .detail-icon").click(),a=!0),a)try{r=setInterval(function(){0<(o=n.$body.find("tr.detail-view").last().find(".detail-icon")).length?o.click():clearInterval(r)},1)}catch(i){clearInterval(r)}}else for(var s=this.$body.children(),l=0;l<s.length;l++)this.expandRow_(!0,t(s[l]).data("index"))},d.prototype.collapseAllRows=function(e){if(e)this.expandRow_(!1,0);else for(var i=this.$body.children(),n=0;n<i.length;n++)this.expandRow_(!1,t(i[n]).data("index"))},d.prototype.updateFormatText=function(t,i){this.options[e("format%s",t)]&&("string"==typeof i?this.options[e("format%s",t)]=function(){return i}:"function"==typeof i&&(this.options[e("format%s",t)]=i)),this.initToolbar(),this.initPagination(),this.initBody()},["getOptions","getSelections","getAllSelections","getData","load","append","prepend","remove","removeAll","insertRow","updateRow","updateCell","updateByUniqueId","removeByUniqueId","getRowByUniqueId","showRow","hideRow","getHiddenRows","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","resetView","resetWidth","destroy","showLoading","hideLoading","showColumn","hideColumn","getHiddenColumns","getVisibleColumns","showAllColumns","hideAllColumns","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","togglePagination","toggleView","refreshOptions","resetSearch","expandRow","collapseRow","expandAllRows","collapseAllRows","updateFormatText"]);t.fn.bootstrapTable=function(e){var i,n=Array.prototype.slice.call(arguments,1);return this.each(function(){var o=t(this),a=o.data("bootstrap.table"),r=t.extend({},d.DEFAULTS,o.data(),"object"==typeof e&&e);if("string"==typeof e){if(t.inArray(e,p)<0)throw new Error("Unknown method: "+e);if(!a)return;i=a[e].apply(a,n),"destroy"===e&&o.removeData("bootstrap.table")}a||o.data("bootstrap.table",a=new d(this,r))}),void 0===i?this:i},t.fn.bootstrapTable.Constructor=d,t.fn.bootstrapTable.defaults=d.DEFAULTS,t.fn.bootstrapTable.columnDefaults=d.COLUMN_DEFAULTS,t.fn.bootstrapTable.locales=d.LOCALES,t.fn.bootstrapTable.methods=p,t.fn.bootstrapTable.utils={sprintf:e,getFieldIndex:i,compareObjects:a,calculateObjectValue:o,getItemField:l,objectKeys:function(){var t,e,i,n;Object.keys||(Object.keys=(t=Object.prototype.hasOwnProperty,e=!{toString:null}.propertyIsEnumerable("toString"),n=(i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(o){if("object"!=typeof o&&("function"!=typeof o||null===o))throw new TypeError("Object.keys called on non-object");var a,r,s=[];for(a in o)t.call(o,a)&&s.push(a);if(e)for(r=0;r<n;r++)t.call(o,i[r])&&s.push(i[r]);return s}))},isIEBrowser:c},t(function(){t('[data-toggle="table"]').bootstrapTable()})}(jQuery),define("bootstrap-table",["bootstrap"],function(t){return function(){return t.$.fn.bootstrapTable}}(this)),function(t){"use strict";t.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候……"},formatRecordsPerPage:function(t){return"每页显示 "+t+" 条记录"},formatShowingRows:function(t,e,i){return"显示第 "+t+" 到第 "+e+" 条记录，总共 "+i+" 条记录"},formatDetailPagination:function(t){return"总共 "+t+" 条记录"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatColumns:function(){return"列"},formatExport:function(){return"导出数据"},formatClearFilters:function(){return"清空过滤"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}(jQuery),define("bootstrap-table-lang",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf,i={json:"JSON",xml:"XML",png:"PNG",csv:"CSV",txt:"TXT",sql:"SQL",doc:"MS-Word",excel:"MS-Excel",xlsx:"MS-Excel (OpenXML)",powerpoint:"MS-Powerpoint",pdf:"PDF"},n=(t.extend(t.fn.bootstrapTable.defaults,{showExport:!1,exportDataType:"basic",exportTypes:["json","xml","csv","txt","sql","excel"],exportOptions:{}}),t.extend(t.fn.bootstrapTable.defaults.icons,{export:"glyphicon-export icon-share"}),t.extend(t.fn.bootstrapTable.locales,{formatExport:function(){return"Export data"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales),t.fn.bootstrapTable.Constructor),o=n.prototype.initToolbar;n.prototype.initToolbar=function(){var n,a,r,s;this.showToolbar=this.options.showExport,o.apply(this,Array.prototype.slice.apply(arguments)),this.options.showExport&&!(r=(n=this).$toolbar.find(">.btn-group")).find("div.export").length&&(a=t(['<div class="export btn-group">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+' dropdown-toggle" aria-label="export type" title="'+this.options.formatExport()+'" data-toggle="dropdown" type="button">',e('<i class="%s %s"></i> ',this.options.iconsPrefix,this.options.icons.export),'<span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">',"</ul>","</div>"].join("")).appendTo(r).find(".dropdown-menu"),s=this.options.exportTypes,"string"==typeof this.options.exportTypes&&(r=this.options.exportTypes.slice(1,-1).replace(/ /g,"").split(","),s=[],t.each(r,function(t,e){s.push(e.slice(1,-1))})),t.each(s,function(t,e){i.hasOwnProperty(e)&&a.append(['<li role="menuitem" data-type="'+e+'">','<a href="javascript:void(0)">',i[e],"</a>","</li>"].join(""))}),a.find("li").click(function(){var e=this;if("function"!=typeof require)throw new Error("RequireJS not found");require(["tableexport"],function(){function i(){n.$el.tableExport(t.extend({},n.options.exportOptions,{type:s,escape:!1}))}var o,a,r,s=t(e).data("type");"all"===n.options.exportDataType&&n.options.pagination?(n.$el.one("server"===n.options.sidePagination?"post-body.bs.table":"page-change.bs.table",function(){i(),n.togglePagination()}),n.togglePagination()):"selected"===n.options.exportDataType?(o=n.getData(),r=n.getAllSelections(),"server"===n.options.sidePagination&&((o={total:n.options.totalRows})[n.options.dataField]=n.getData(),a="function"==typeof require?require("table"):null,(r={total:n.options.totalRows})[n.options.dataField]=a&&n.options.maintainSelected?a.api.selecteddata(n.$el):n.getAllSelections()),n.load(r),i(),n.load(o)):i()})}))}}(jQuery),define("bootstrap-table-export",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"function"==typeof define&&define.amd?define("dropzone",["jquery"],t):t(jQuery)}(function(t){function e(t){"@babel/helpers - typeof";return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,i){return!i||"object"!==e(i)&&"function"!=typeof i?o(t):i}function n(t){return(n=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function o(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&r(t,e)}function r(t,e){return(r=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function c(t,e,i){return e&&l(t.prototype,e),i&&l(t,i),t}function d(t,e){return void 0!==t&&null!==t?e(t):void 0}function u(t,e,i){return void 0!==t&&null!==t&&"function"==typeof t[e]?i(t,e):void 0}var p={exports:{}},h=function(){function t(){s(this,t)}return c(t,[{key:"on",value:function(t,e){return this._callbacks=this._callbacks||{},this._callbacks[t]||(this._callbacks[t]=[]),this._callbacks[t].push(e),this}},{key:"emit",value:function(t){this._callbacks=this._callbacks||{};var e=this._callbacks[t];if(e){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];var a=!0,r=!1,s=void 0;try{for(var l,c=e[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){l.value.apply(this,n)}}catch(t){r=!0,s=t}finally{try{a||null==c.return||c.return()}finally{if(r)throw s}}}return this}},{key:"off",value:function(t,e){if(!this._callbacks||0===arguments.length)return this._callbacks={},this;var i=this._callbacks[t];if(!i)return this;if(1===arguments.length)return delete this._callbacks[t],this;for(var n=0;n<i.length;n++){if(i[n]===e){i.splice(n,1);break}}return this}}]),t}(),f=function(t){function e(t,a){var r;s(this,e),r=i(this,n(e).call(this));var l,c;if(r.element=t,r.version=e.version,r.defaultOptions.previewTemplate=r.defaultOptions.previewTemplate.replace(/\n*/g,""),r.clickableElements=[],r.listeners=[],r.files=[],"string"==typeof r.element&&(r.element=document.querySelector(r.element)),!r.element||null==r.element.nodeType)throw new Error("Invalid dropzone element.");if(r.element.dropzone)throw new Error("Dropzone already attached.");e.instances.push(o(r)),r.element.dropzone=o(r);var d=null!=(c=e.optionsForElement(r.element))?c:{};if(r.options=e.extend({},r.defaultOptions,d,null!=a?a:{}),r.options.forceFallback||!e.isBrowserSupported())return i(r,r.options.fallback.call(o(r)));if(null==r.options.url&&(r.options.url=r.element.getAttribute("action")),!r.options.url)throw new Error("No URL provided.");if(r.options.acceptedFiles&&r.options.acceptedMimeTypes)throw new Error("You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.");if(r.options.uploadMultiple&&r.options.chunking)throw new Error("You cannot set both: uploadMultiple and chunking.");return r.options.acceptedMimeTypes&&(r.options.acceptedFiles=r.options.acceptedMimeTypes,delete r.options.acceptedMimeTypes),null!=r.options.renameFilename&&(r.options.renameFile=function(t){return r.options.renameFilename.call(o(r),t.name,t)}),r.options.method="function"!=typeof r.options.method?r.options.method.toUpperCase():r.options.method,(l=r.getExistingFallback())&&l.parentNode&&l.parentNode.removeChild(l),!1!==r.options.previewsContainer&&(r.options.previewsContainer?r.previewsContainer=e.getElement(r.options.previewsContainer,"previewsContainer"):r.previewsContainer=r.element),r.options.clickable&&(!0===r.options.clickable?r.clickableElements=[r.element]:r.clickableElements=e.getElements(r.options.clickable,"clickable")),r.init(),r}return a(e,t),c(e,null,[{key:"initClass",value:function(){this.prototype.Emitter=h,this.prototype.events=["drop","dragstart","dragend","dragenter","dragover","dragleave","addedfile","addedfiles","removedfile","thumbnail","error","errormultiple","processing","processingmultiple","uploadprogress","totaluploadprogress","sending","sendingmultiple","success","successmultiple","canceled","canceledmultiple","complete","completemultiple","reset","maxfilesexceeded","maxfilesreached","queuecomplete"],this.prototype.defaultOptions={url:null,method:"post",withCredentials:!1,timeout:3e4,parallelUploads:2,uploadMultiple:!1,chunking:!1,forceChunking:!1,chunkSize:2e6,parallelChunkUploads:!1,retryChunks:!1,retryChunksLimit:3,maxFilesize:256,paramName:"file",createImageThumbnails:!0,maxThumbnailFilesize:10,thumbnailWidth:120,thumbnailHeight:120,thumbnailMethod:"crop",resizeWidth:null,resizeHeight:null,resizeMimeType:null,resizeQuality:.8,resizeMethod:"contain",filesizeBase:1e3,maxFiles:null,headers:null,clickable:!0,ignoreHiddenFiles:!0,acceptedFiles:null,acceptedMimeTypes:null,autoProcessQueue:!0,autoQueue:!0,addRemoveLinks:!1,previewsContainer:null,hiddenInputContainer:"body",capture:null,renameFilename:null,renameFile:null,forceFallback:!1,dictDefaultMessage:"Drop files here to upload",dictFallbackMessage:"Your browser does not support drag'n'drop file uploads.",dictFallbackText:"Please use the fallback form below to upload your files like in the olden days.",dictFileTooBig:"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.",dictInvalidFileType:"You can't upload files of this type.",dictResponseError:"Server responded with {{statusCode}} code.",dictCancelUpload:"Cancel upload",dictUploadCanceled:"Upload canceled.",dictCancelUploadConfirmation:"Are you sure you want to cancel this upload?",dictRemoveFile:"Remove file",dictRemoveFileConfirmation:null,dictMaxFilesExceeded:"You can not upload any more files.",dictFileSizeUnits:{tb:"TB",gb:"GB",mb:"MB",kb:"KB",b:"b"},init:function(){},params:function(t,e,i){if(i)return{dzuuid:i.file.upload.uuid,dzchunkindex:i.index,dztotalfilesize:i.file.size,dzchunksize:this.options.chunkSize,dztotalchunkcount:i.file.upload.totalChunkCount,dzchunkbyteoffset:i.index*this.options.chunkSize}},accept:function(t,e){return e()},chunkSuccess:function(t,e,i){},chunksUploaded:function(t,e){e()},fallback:function(){var t;this.element.className="".concat(this.element.className," dz-browser-not-supported");var i=!0,n=!1,o=void 0;try{for(var a,r=this.element.getElementsByTagName("div")[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;if(/(^| )dz-message($| )/.test(s.className)){t=s,s.className="dz-message";break}}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}t||(t=e.createElement('<div class="dz-message"><span></span></div>'),this.element.appendChild(t));var l=t.getElementsByTagName("span")[0];return l&&(null!=l.textContent?l.textContent=this.options.dictFallbackMessage:null!=l.innerText&&(l.innerText=this.options.dictFallbackMessage)),this.element.appendChild(this.getFallbackForm())},resize:function(t,e,i,n){var o={srcX:0,srcY:0,srcWidth:t.width,srcHeight:t.height},a=t.width/t.height;null==e&&null==i?(e=o.srcWidth,i=o.srcHeight):null==e?e=i*a:null==i&&(i=e/a),e=Math.min(e,o.srcWidth),i=Math.min(i,o.srcHeight);var r=e/i;if(o.srcWidth>e||o.srcHeight>i)if("crop"===n)a>r?(o.srcHeight=t.height,o.srcWidth=o.srcHeight*r):(o.srcWidth=t.width,o.srcHeight=o.srcWidth/r);else{if("contain"!==n)throw new Error("Unknown resizeMethod '".concat(n,"'"));a>r?i=e/a:e=i*a}return o.srcX=(t.width-o.srcWidth)/2,o.srcY=(t.height-o.srcHeight)/2,o.trgWidth=e,o.trgHeight=i,o},transformFile:function(t,e){return(this.options.resizeWidth||this.options.resizeHeight)&&t.type.match(/image.*/)?this.resizeImage(t,this.options.resizeWidth,this.options.resizeHeight,this.options.resizeMethod,e):e(t)},previewTemplate:'<div class="dz-preview dz-file-preview">\n  <div class="dz-image"><img data-dz-thumbnail /></div>\n  <div class="dz-details">\n    <div class="dz-size"><span data-dz-size></span></div>\n    <div class="dz-filename"><span data-dz-name></span></div>\n  </div>\n  <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>\n  <div class="dz-error-message"><span data-dz-errormessage></span></div>\n  <div class="dz-success-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Check</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z" stroke-opacity="0.198794158" stroke="#747474" fill-opacity="0.816519475" fill="#FFFFFF"></path>\n      </g>\n    </svg>\n  </div>\n  <div class="dz-error-mark">\n    <svg width="54px" height="54px" viewBox="0 0 54 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      <title>Error</title>\n      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <g stroke="#747474" stroke-opacity="0.198794158" fill="#FFFFFF" fill-opacity="0.816519475">\n          <path d="M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>',drop:function(t){return this.element.classList.remove("dz-drag-hover")},dragstart:function(t){},dragend:function(t){return this.element.classList.remove("dz-drag-hover")},dragenter:function(t){return this.element.classList.add("dz-drag-hover")},dragover:function(t){return this.element.classList.add("dz-drag-hover")},dragleave:function(t){return this.element.classList.remove("dz-drag-hover")},paste:function(t){},reset:function(){return this.element.classList.remove("dz-started")},addedfile:function(t){var i=this;if(this.element===this.previewsContainer&&this.element.classList.add("dz-started"),this.previewsContainer){t.previewElement=e.createElement(this.options.previewTemplate.trim()),t.previewTemplate=t.previewElement,this.previewsContainer.appendChild(t.previewElement);var n=!0,o=!1,a=void 0;try{for(var r,s=t.previewElement.querySelectorAll("[data-dz-name]")[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l=r.value;l.textContent=t.name}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}var c=!0,d=!1,u=void 0;try{for(var p,h=t.previewElement.querySelectorAll("[data-dz-size]")[Symbol.iterator]();!(c=(p=h.next()).done);c=!0)l=p.value,l.innerHTML=this.filesize(t.size)}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}this.options.addRemoveLinks&&(t._removeLink=e.createElement('<a class="dz-remove" href="javascript:undefined;" data-dz-remove>'.concat(this.options.dictRemoveFile,"</a>")),t.previewElement.appendChild(t._removeLink));var f=function(n){return n.preventDefault(),n.stopPropagation(),t.status===e.UPLOADING?e.confirm(i.options.dictCancelUploadConfirmation,function(){return i.removeFile(t)}):i.options.dictRemoveFileConfirmation?e.confirm(i.options.dictRemoveFileConfirmation,function(){return i.removeFile(t)}):i.removeFile(t)},m=!0,g=!1,v=void 0;try{for(var y,b=t.previewElement.querySelectorAll("[data-dz-remove]")[Symbol.iterator]();!(m=(y=b.next()).done);m=!0){y.value.addEventListener("click",f)}}catch(t){g=!0,v=t}finally{try{m||null==b.return||b.return()}finally{if(g)throw v}}}},removedfile:function(t){return null!=t.previewElement&&null!=t.previewElement.parentNode&&t.previewElement.parentNode.removeChild(t.previewElement),this._updateMaxFilesReachedClass()},thumbnail:function(t,e){if(t.previewElement){t.previewElement.classList.remove("dz-file-preview");var i=!0,n=!1,o=void 0;try{for(var a,r=t.previewElement.querySelectorAll("[data-dz-thumbnail]")[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;s.alt=t.name,s.src=e}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}return setTimeout(function(){return t.previewElement.classList.add("dz-image-preview")},1)}},error:function(t,e){if(t.previewElement){t.previewElement.classList.add("dz-error"),"String"!=typeof e&&e.error&&(e=e.error);var i=!0,n=!1,o=void 0;try{for(var a,r=t.previewElement.querySelectorAll("[data-dz-errormessage]")[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){a.value.textContent=e}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}}},errormultiple:function(){},processing:function(t){if(t.previewElement&&(t.previewElement.classList.add("dz-processing"),t._removeLink))return t._removeLink.innerHTML=this.options.dictCancelUpload},processingmultiple:function(){},uploadprogress:function(t,e,i){if(t.previewElement){var n=!0,o=!1,a=void 0;try{for(var r,s=t.previewElement.querySelectorAll("[data-dz-uploadprogress]")[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l=r.value;"PROGRESS"===l.nodeName?l.value=e:l.style.width="".concat(e,"%")}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}}},totaluploadprogress:function(){},sending:function(){},sendingmultiple:function(){},success:function(t){if(t.previewElement)return t.previewElement.classList.add("dz-success")},successmultiple:function(){},canceled:function(t){return this.emit("error",t,this.options.dictUploadCanceled)},canceledmultiple:function(){},complete:function(t){if(t._removeLink&&(t._removeLink.innerHTML=this.options.dictRemoveFile),t.previewElement)return t.previewElement.classList.add("dz-complete")},completemultiple:function(){},maxfilesexceeded:function(){},maxfilesreached:function(){},queuecomplete:function(){},addedfiles:function(){}},this.prototype._thumbnailQueue=[],this.prototype._processingThumbnail=!1}},{key:"extend",value:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(var o=0,a=i;o<a.length;o++){var r=a[o];for(var s in r){var l=r[s];t[s]=l}}return t}}]),c(e,[{key:"getAcceptedFiles",value:function(){return this.files.filter(function(t){return t.accepted}).map(function(t){return t})}},{key:"getRejectedFiles",value:function(){return this.files.filter(function(t){return!t.accepted}).map(function(t){return t})}},{key:"getFilesWithStatus",value:function(t){return this.files.filter(function(e){return e.status===t}).map(function(t){return t})}},{key:"getQueuedFiles",value:function(){return this.getFilesWithStatus(e.QUEUED)}},{key:"getUploadingFiles",value:function(){return this.getFilesWithStatus(e.UPLOADING)}},{key:"getAddedFiles",value:function(){return this.getFilesWithStatus(e.ADDED)}},{key:"getActiveFiles",value:function(){return this.files.filter(function(t){return t.status===e.UPLOADING||t.status===e.QUEUED}).map(function(t){return t})}},{key:"init",value:function(){var t=this;if("form"===this.element.tagName&&this.element.setAttribute("enctype","multipart/form-data"),this.element.classList.contains("dropzone")&&!this.element.querySelector(".dz-message")&&this.element.appendChild(e.createElement('<div class="dz-default dz-message"><button class="dz-button" type="button">'.concat(this.options.dictDefaultMessage,"</button></div>"))),this.clickableElements.length){!function i(){return t.hiddenFileInput&&t.hiddenFileInput.parentNode.removeChild(t.hiddenFileInput),t.hiddenFileInput=document.createElement("input"),t.hiddenFileInput.setAttribute("type","file"),(null===t.options.maxFiles||t.options.maxFiles>1)&&t.hiddenFileInput.setAttribute("multiple","multiple"),t.hiddenFileInput.className="dz-hidden-input",null!==t.options.acceptedFiles&&t.hiddenFileInput.setAttribute("accept",t.options.acceptedFiles),null!==t.options.capture&&t.hiddenFileInput.setAttribute("capture",t.options.capture),t.hiddenFileInput.style.visibility="hidden",t.hiddenFileInput.style.position="absolute",t.hiddenFileInput.style.top="0",t.hiddenFileInput.style.left="0",t.hiddenFileInput.style.height="0",t.hiddenFileInput.style.width="0",e.getElement(t.options.hiddenInputContainer,"hiddenInputContainer").appendChild(t.hiddenFileInput),t.hiddenFileInput.addEventListener("change",function(){var e=t.hiddenFileInput.files;if(e.length){var n=!0,o=!1,a=void 0;try{for(var r,s=e[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l=r.value;t.addFile(l)}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}}return t.emit("addedfiles",e),i()})}()}this.URL=null!==window.URL?window.URL:window.webkitURL;var i=!0,n=!1,o=void 0;try{for(var a,r=this.events[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;this.on(s,this.options[s])}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}this.on("uploadprogress",function(){return t.updateTotalUploadProgress()}),this.on("removedfile",function(){return t.updateTotalUploadProgress()}),this.on("canceled",function(e){return t.emit("complete",e)}),this.on("complete",function(e){if(0===t.getAddedFiles().length&&0===t.getUploadingFiles().length&&0===t.getQueuedFiles().length)return setTimeout(function(){return t.emit("queuecomplete")},0)});var l=function(t){return t.dataTransfer.types&&t.dataTransfer.types.some(function(t){return"Files"==t})},c=function(t){if(l(t))return t.stopPropagation(),t.preventDefault?t.preventDefault():t.returnValue=!1};return this.listeners=[{element:this.element,events:{dragstart:function(e){return t.emit("dragstart",e)},dragenter:function(e){return c(e),t.emit("dragenter",e)},dragover:function(e){var i;try{i=e.dataTransfer.effectAllowed}catch(t){}return e.dataTransfer.dropEffect="move"===i||"linkMove"===i?"move":"copy",c(e),t.emit("dragover",e)},dragleave:function(e){return t.emit("dragleave",e)},drop:function(e){return c(e),t.drop(e)},dragend:function(e){return t.emit("dragend",e)}}}],this.clickableElements.forEach(function(i){return t.listeners.push({element:i,events:{click:function(n){return(i!==t.element||n.target===t.element||e.elementInside(n.target,t.element.querySelector(".dz-message")))&&t.hiddenFileInput.click(),!0}}})}),this.enable(),this.options.init.call(this)}},{key:"destroy",value:function(){return this.disable(),this.removeAllFiles(!0),(null!=this.hiddenFileInput?this.hiddenFileInput.parentNode:void 0)&&(this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput),this.hiddenFileInput=null),delete this.element.dropzone,e.instances.splice(e.instances.indexOf(this),1)}},{key:"updateTotalUploadProgress",value:function(){var t,e=0,i=0;if(this.getActiveFiles().length){var n=!0,o=!1,a=void 0;try{for(var r,s=this.getActiveFiles()[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l=r.value;e+=l.upload.bytesSent,i+=l.upload.total}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}t=100*e/i}else t=100;return this.emit("totaluploadprogress",t,i,e)}},{key:"_getParamName",value:function(t){return"function"==typeof this.options.paramName?this.options.paramName(t):"".concat(this.options.paramName).concat(this.options.uploadMultiple?"[".concat(t,"]"):"")}},{key:"_renameFile",value:function(t){return"function"!=typeof this.options.renameFile?t.name:this.options.renameFile(t)}},{key:"getFallbackForm",value:function(){var t,i;if(t=this.getExistingFallback())return t;var n='<div class="dz-fallback">';this.options.dictFallbackText&&(n+="<p>".concat(this.options.dictFallbackText,"</p>")),n+='<input type="file" name="'.concat(this._getParamName(0),'" ').concat(this.options.uploadMultiple?'multiple="multiple"':void 0,' /><input type="submit" value="Upload!"></div>');var o=e.createElement(n);return"FORM"!==this.element.tagName?(i=e.createElement('<form action="'.concat(this.options.url,'" enctype="multipart/form-data" method="').concat(this.options.method,'"></form>')),i.appendChild(o)):(this.element.setAttribute("enctype","multipart/form-data"),this.element.setAttribute("method",this.options.method)),null!=i?i:o}},{key:"getExistingFallback",value:function(){for(var t=0,e=["div","form"];t<e.length;t++){var i,n=e[t];if(i=function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;if(/(^| )fallback($| )/.test(r.className))return r}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}(this.element.getElementsByTagName(n)))return i}}},{key:"setupEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var i in t.events){var n=t.events[i];e.push(t.element.addEventListener(i,n,!1))}return e}()})}},{key:"removeEventListeners",value:function(){return this.listeners.map(function(t){return function(){var e=[];for(var i in t.events){var n=t.events[i];e.push(t.element.removeEventListener(i,n,!1))}return e}()})}},{key:"disable",value:function(){var t=this;return this.clickableElements.forEach(function(t){return t.classList.remove("dz-clickable")}),this.removeEventListeners(),this.disabled=!0,this.files.map(function(e){return t.cancelUpload(e)})}},{key:"enable",value:function(){return delete this.disabled,this.clickableElements.forEach(function(t){return t.classList.add("dz-clickable")}),this.setupEventListeners()}},{key:"filesize",value:function(t){var e=0,i="b";if(t>0){for(var n=["tb","gb","mb","kb","b"],o=0;o<n.length;o++){var a=n[o];if(t>=Math.pow(this.options.filesizeBase,4-o)/10){e=t/Math.pow(this.options.filesizeBase,4-o),i=a;break}}e=Math.round(10*e)/10}return"<strong>".concat(e,"</strong> ").concat(this.options.dictFileSizeUnits[i])}},{key:"_updateMaxFilesReachedClass",value:function(){return null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(this.getAcceptedFiles().length===this.options.maxFiles&&this.emit("maxfilesreached",this.files),this.element.classList.add("dz-max-files-reached")):this.element.classList.remove("dz-max-files-reached")}},{key:"drop",value:function(t){if(t.dataTransfer){this.emit("drop",t);for(var e=[],i=0;i<t.dataTransfer.files.length;i++)e[i]=t.dataTransfer.files[i];if(e.length){var n=t.dataTransfer.items;n&&n.length&&null!=n[0].webkitGetAsEntry?this._addFilesFromItems(n):this.handleFiles(e)}this.emit("addedfiles",e)}}},{key:"paste",value:function(t){if(null!=d(null!=t?t.clipboardData:void 0,function(t){return t.items})){this.emit("paste",t);var e=t.clipboardData.items;return e.length?this._addFilesFromItems(e):void 0}}},{key:"handleFiles",value:function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;this.addFile(r)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}},{key:"_addFilesFromItems",value:function(t){var e=this;return function(){var i=[],n=!0,o=!1,a=void 0;try{for(var r,s=t[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l,c=r.value;null!=c.webkitGetAsEntry&&(l=c.webkitGetAsEntry())?l.isFile?i.push(e.addFile(c.getAsFile())):l.isDirectory?i.push(e._addFilesFromDirectory(l,l.name)):i.push(void 0):null!=c.getAsFile&&(null==c.kind||"file"===c.kind)?i.push(e.addFile(c.getAsFile())):i.push(void 0)}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}return i}()}},{key:"_addFilesFromDirectory",value:function(t,e){var i=this,n=t.createReader(),o=function(t){return u(console,"log",function(e){return e.log(t)})};return function t(){return n.readEntries(function(n){if(n.length>0){var o=!0,a=!1,r=void 0;try{for(var s,l=n[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.isFile?c.file(function(t){if(!i.options.ignoreHiddenFiles||"."!==t.name.substring(0,1))return t.fullPath="".concat(e,"/").concat(t.name),i.addFile(t)}):c.isDirectory&&i._addFilesFromDirectory(c,"".concat(e,"/").concat(c.name))}}catch(t){a=!0,r=t}finally{try{
o||null==l.return||l.return()}finally{if(a)throw r}}t()}return null},o)}()}},{key:"accept",value:function(t,i){this.options.maxFilesize&&t.size>1024*this.options.maxFilesize*1024?i(this.options.dictFileTooBig.replace("{{filesize}}",Math.round(t.size/1024/10.24)/100).replace("{{maxFilesize}}",this.options.maxFilesize)):e.isValidFile(t,this.options.acceptedFiles)?null!=this.options.maxFiles&&this.getAcceptedFiles().length>=this.options.maxFiles?(i(this.options.dictMaxFilesExceeded.replace("{{maxFiles}}",this.options.maxFiles)),this.emit("maxfilesexceeded",t)):this.options.accept.call(this,t,i):i(this.options.dictInvalidFileType)}},{key:"addFile",value:function(t){var i=this;t.upload={uuid:e.uuidv4(),progress:0,total:t.size,bytesSent:0,filename:this._renameFile(t)},this.files.push(t),t.status=e.ADDED,this.emit("addedfile",t),this._enqueueThumbnail(t),this.accept(t,function(e){e?(t.accepted=!1,i._errorProcessing([t],e)):(t.accepted=!0,i.options.autoQueue&&i.enqueueFile(t)),i._updateMaxFilesReachedClass()})}},{key:"enqueueFiles",value:function(t){var e=!0,i=!1,n=void 0;try{for(var o,a=t[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;this.enqueueFile(r)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}return null}},{key:"enqueueFile",value:function(t){var i=this;if(t.status!==e.ADDED||!0!==t.accepted)throw new Error("This file can't be queued because it has already been processed or was rejected.");if(t.status=e.QUEUED,this.options.autoProcessQueue)return setTimeout(function(){return i.processQueue()},0)}},{key:"_enqueueThumbnail",value:function(t){var e=this;if(this.options.createImageThumbnails&&t.type.match(/image.*/)&&t.size<=1024*this.options.maxThumbnailFilesize*1024)return this._thumbnailQueue.push(t),setTimeout(function(){return e._processThumbnailQueue()},0)}},{key:"_processThumbnailQueue",value:function(){var t=this;if(!this._processingThumbnail&&0!==this._thumbnailQueue.length){this._processingThumbnail=!0;var e=this._thumbnailQueue.shift();return this.createThumbnail(e,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.thumbnailMethod,!0,function(i){return t.emit("thumbnail",e,i),t._processingThumbnail=!1,t._processThumbnailQueue()})}}},{key:"removeFile",value:function(t){if(t.status===e.UPLOADING&&this.cancelUpload(t),this.files=m(this.files,t),this.emit("removedfile",t),0===this.files.length)return this.emit("reset")}},{key:"removeAllFiles",value:function(t){null==t&&(t=!1);var i=!0,n=!1,o=void 0;try{for(var a,r=this.files.slice()[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;(s.status!==e.UPLOADING||t)&&this.removeFile(s)}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}return null}},{key:"resizeImage",value:function(t,i,n,o,a){var r=this;return this.createThumbnail(t,i,n,o,!0,function(i,n){if(null==n)return a(t);var o=r.options.resizeMimeType;null==o&&(o=t.type);var s=n.toDataURL(o,r.options.resizeQuality);return"image/jpeg"!==o&&"image/jpg"!==o||(s=b.restore(t.dataURL,s)),a(e.dataURItoBlob(s))})}},{key:"createThumbnail",value:function(t,e,i,n,o,a){var r=this,s=new FileReader;s.onload=function(){if(t.dataURL=s.result,"image/svg+xml"===t.type)return void(null!=a&&a(s.result));r.createThumbnailFromUrl(t,e,i,n,o,a)},s.readAsDataURL(t)}},{key:"displayExistingFile",value:function(t,e,i,n){var o=this,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];if(this.emit("addedfile",t),this.emit("complete",t),a){var r=function(e){o.emit("thumbnail",t,e),i&&i()};t.dataURL=e,this.createThumbnailFromUrl(t,this.options.thumbnailWidth,this.options.thumbnailHeight,this.options.resizeMethod,this.options.fixOrientation,r,n)}else this.emit("thumbnail",t,e),i&&i()}},{key:"createThumbnailFromUrl",value:function(t,e,i,n,o,a,r){var s=this,l=document.createElement("img");return r&&(l.crossOrigin=r),l.onload=function(){var r=function(t){return t(1)};return"undefined"!=typeof EXIF&&null!==EXIF&&o&&(r=function(t){return EXIF.getData(l,function(){return t(EXIF.getTag(this,"Orientation"))})}),r(function(o){t.width=l.width,t.height=l.height;var r=s.options.resize.call(s,t,e,i,n),c=document.createElement("canvas"),d=c.getContext("2d");switch(c.width=r.trgWidth,c.height=r.trgHeight,o>4&&(c.width=r.trgHeight,c.height=r.trgWidth),o){case 2:d.translate(c.width,0),d.scale(-1,1);break;case 3:d.translate(c.width,c.height),d.rotate(Math.PI);break;case 4:d.translate(0,c.height),d.scale(1,-1);break;case 5:d.rotate(.5*Math.PI),d.scale(1,-1);break;case 6:d.rotate(.5*Math.PI),d.translate(0,-c.width);break;case 7:d.rotate(.5*Math.PI),d.translate(c.height,-c.width),d.scale(-1,1);break;case 8:d.rotate(-.5*Math.PI),d.translate(-c.height,0)}y(d,l,null!=r.srcX?r.srcX:0,null!=r.srcY?r.srcY:0,r.srcWidth,r.srcHeight,null!=r.trgX?r.trgX:0,null!=r.trgY?r.trgY:0,r.trgWidth,r.trgHeight);var u=c.toDataURL("image/png");if(null!=a)return a(u,c)})},null!=a&&(l.onerror=a),l.src=t.dataURL}},{key:"processQueue",value:function(){var t=this.options.parallelUploads,e=this.getUploadingFiles().length,i=e;if(!(e>=t)){var n=this.getQueuedFiles();if(n.length>0){if(this.options.uploadMultiple)return this.processFiles(n.slice(0,t-e));for(;i<t;){if(!n.length)return;this.processFile(n.shift()),i++}}}}},{key:"processFile",value:function(t){return this.processFiles([t])}},{key:"processFiles",value:function(t){var i=!0,n=!1,o=void 0;try{for(var a,r=t[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;s.processing=!0,s.status=e.UPLOADING,this.emit("processing",s)}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}return this.options.uploadMultiple&&this.emit("processingmultiple",t),this.uploadFiles(t)}},{key:"_getFilesWithXhr",value:function(t){return this.files.filter(function(e){return e.xhr===t}).map(function(t){return t})}},{key:"cancelUpload",value:function(t){if(t.status===e.UPLOADING){var i=this._getFilesWithXhr(t.xhr),n=!0,o=!1,a=void 0;try{for(var r,s=i[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){r.value.status=e.CANCELED}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}void 0!==t.xhr&&t.xhr.abort();var l=!0,c=!1,d=void 0;try{for(var u,p=i[Symbol.iterator]();!(l=(u=p.next()).done);l=!0){var h=u.value;this.emit("canceled",h)}}catch(t){c=!0,d=t}finally{try{l||null==p.return||p.return()}finally{if(c)throw d}}this.options.uploadMultiple&&this.emit("canceledmultiple",i)}else t.status!==e.ADDED&&t.status!==e.QUEUED||(t.status=e.CANCELED,this.emit("canceled",t),this.options.uploadMultiple&&this.emit("canceledmultiple",[t]));if(this.options.autoProcessQueue)return this.processQueue()}},{key:"resolveOption",value:function(t){if("function"==typeof t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return t.apply(this,i)}return t}},{key:"uploadFile",value:function(t){return this.uploadFiles([t])}},{key:"uploadFiles",value:function(t){var i=this;this._transformFiles(t,function(n){if(i.options.chunking){var o=n[0];t[0].upload.chunked=i.options.chunking&&(i.options.forceChunking||o.size>i.options.chunkSize),t[0].upload.totalChunkCount=Math.ceil(o.size/i.options.chunkSize)}if(t[0].upload.chunked){var a=t[0],r=n[0],s=0;a.upload.chunks=[];var l=function(){for(var n=0;void 0!==a.upload.chunks[n];)n++;if(!(n>=a.upload.totalChunkCount)){s++;var o=n*i.options.chunkSize,l=Math.min(o+i.options.chunkSize,a.size),c={name:i._getParamName(0),data:r.webkitSlice?r.webkitSlice(o,l):r.slice(o,l),filename:a.upload.filename,chunkIndex:n};a.upload.chunks[n]={file:a,index:n,dataBlock:c,status:e.UPLOADING,progress:0,retries:0},i._uploadData(t,[c])}};if(a.upload.finishedChunkUpload=function(n,o){var r=!0;n.status=e.SUCCESS,i.options.chunkSuccess.call(i,n,a,o),n.dataBlock=null,n.xhr=null;for(var s=0;s<a.upload.totalChunkCount;s++){if(void 0===a.upload.chunks[s])return l();a.upload.chunks[s].status!==e.SUCCESS&&(r=!1)}r&&i.options.chunksUploaded.call(i,a,function(e){i._finished(t,e||"",null)})},i.options.parallelChunkUploads)for(var c=0;c<a.upload.totalChunkCount;c++)l();else l()}else{for(var d=[],u=0;u<t.length;u++)d[u]={name:i._getParamName(u),data:n[u],filename:t[u].upload.filename};i._uploadData(t,d)}})}},{key:"_getChunk",value:function(t,e){for(var i=0;i<t.upload.totalChunkCount;i++)if(void 0!==t.upload.chunks[i]&&t.upload.chunks[i].xhr===e)return t.upload.chunks[i]}},{key:"_uploadData",value:function(t,i){var n=this,o=new XMLHttpRequest,a=!0,r=!1,s=void 0;try{for(var l,c=t[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){l.value.xhr=o}}catch(t){r=!0,s=t}finally{try{a||null==c.return||c.return()}finally{if(r)throw s}}t[0].upload.chunked&&(t[0].upload.chunks[i[0].chunkIndex].xhr=o);var d=this.resolveOption(this.options.method,t),u=this.resolveOption(this.options.url,t);o.open(d,u,!0),o.timeout=this.resolveOption(this.options.timeout,t),o.withCredentials=!!this.options.withCredentials,o.onload=function(e){n._finishedUploading(t,o,e)},o.ontimeout=function(){n._handleUploadError(t,o,"Request timedout after ".concat(n.options.timeout," seconds"))},o.onerror=function(){n._handleUploadError(t,o)},(null!=o.upload?o.upload:o).onprogress=function(e){return n._updateFilesUploadProgress(t,o,e)};var p={Accept:"application/json","Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"};this.options.headers&&e.extend(p,this.options.headers);for(var h in p){var f=p[h];f&&o.setRequestHeader(h,f)}var m=new FormData;if(this.options.params){var g=this.options.params;"function"==typeof g&&(g=g.call(this,t,o,t[0].upload.chunked?this._getChunk(t[0],o):null));for(var v in g){var y=g[v];m.append(v,y)}}var b=!0,x=!1,w=void 0;try{for(var _,k=t[Symbol.iterator]();!(b=(_=k.next()).done);b=!0){var C=_.value;this.emit("sending",C,o,m)}}catch(t){x=!0,w=t}finally{try{b||null==k.return||k.return()}finally{if(x)throw w}}this.options.uploadMultiple&&this.emit("sendingmultiple",t,o,m),this._addFormElementData(m);for(var S=0;S<i.length;S++){var T=i[S];m.append(T.name,T.data,T.filename)}this.submitRequest(o,m,t)}},{key:"_transformFiles",value:function(t,e){for(var i=this,n=[],o=0,a=0;a<t.length;a++)!function(a){i.options.transformFile.call(i,t[a],function(i){n[a]=i,++o===t.length&&e(n)})}(a)}},{key:"_addFormElementData",value:function(t){if("FORM"===this.element.tagName){var e=!0,i=!1,n=void 0;try{for(var o,a=this.element.querySelectorAll("input, textarea, select, button")[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value,s=r.getAttribute("name"),l=r.getAttribute("type");if(l&&(l=l.toLowerCase()),void 0!==s&&null!==s)if("SELECT"===r.tagName&&r.hasAttribute("multiple")){var c=!0,d=!1,u=void 0;try{for(var p,h=r.options[Symbol.iterator]();!(c=(p=h.next()).done);c=!0){var f=p.value;f.selected&&t.append(s,f.value)}}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}}else(!l||"checkbox"!==l&&"radio"!==l||r.checked)&&t.append(s,r.value)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}}},{key:"_updateFilesUploadProgress",value:function(t,e,i){var n;if(void 0!==i){if(n=100*i.loaded/i.total,t[0].upload.chunked){var o=t[0],a=this._getChunk(o,e);a.progress=n,a.total=i.total,a.bytesSent=i.loaded;o.upload.progress=0,o.upload.total=0,o.upload.bytesSent=0;for(var r=0;r<o.upload.totalChunkCount;r++)void 0!==o.upload.chunks[r]&&void 0!==o.upload.chunks[r].progress&&(o.upload.progress+=o.upload.chunks[r].progress,o.upload.total+=o.upload.chunks[r].total,o.upload.bytesSent+=o.upload.chunks[r].bytesSent);o.upload.progress=o.upload.progress/o.upload.totalChunkCount}else{var s=!0,l=!1,c=void 0;try{for(var d,u=t[Symbol.iterator]();!(s=(d=u.next()).done);s=!0){var p=d.value;p.upload.progress=n,p.upload.total=i.total,p.upload.bytesSent=i.loaded}}catch(t){l=!0,c=t}finally{try{s||null==u.return||u.return()}finally{if(l)throw c}}}var h=!0,f=!1,m=void 0;try{for(var g,v=t[Symbol.iterator]();!(h=(g=v.next()).done);h=!0){var y=g.value;this.emit("uploadprogress",y,y.upload.progress,y.upload.bytesSent)}}catch(t){f=!0,m=t}finally{try{h||null==v.return||v.return()}finally{if(f)throw m}}}else{var b=!0;n=100;var x=!0,w=!1,_=void 0;try{for(var k,C=t[Symbol.iterator]();!(x=(k=C.next()).done);x=!0){var S=k.value;100===S.upload.progress&&S.upload.bytesSent===S.upload.total||(b=!1),S.upload.progress=n,S.upload.bytesSent=S.upload.total}}catch(t){w=!0,_=t}finally{try{x||null==C.return||C.return()}finally{if(w)throw _}}if(b)return;var T=!0,D=!1,$=void 0;try{for(var E,F=t[Symbol.iterator]();!(T=(E=F.next()).done);T=!0){var A=E.value;this.emit("uploadprogress",A,n,A.upload.bytesSent)}}catch(t){D=!0,$=t}finally{try{T||null==F.return||F.return()}finally{if(D)throw $}}}}},{key:"_finishedUploading",value:function(t,i,n){var o;if(t[0].status!==e.CANCELED&&4===i.readyState){if("arraybuffer"!==i.responseType&&"blob"!==i.responseType&&(o=i.responseText,i.getResponseHeader("content-type")&&~i.getResponseHeader("content-type").indexOf("application/json")))try{o=JSON.parse(o)}catch(t){n=t,o="Invalid JSON response from server."}this._updateFilesUploadProgress(t),200<=i.status&&i.status<300?t[0].upload.chunked?t[0].upload.finishedChunkUpload(this._getChunk(t[0],i),o):this._finished(t,o,n):this._handleUploadError(t,i,o)}}},{key:"_handleUploadError",value:function(t,i,n){if(t[0].status!==e.CANCELED){if(t[0].upload.chunked&&this.options.retryChunks){var o=this._getChunk(t[0],i);if(o.retries++<this.options.retryChunksLimit)return void this._uploadData(t,[o.dataBlock]);console.warn("Retried this chunk too often. Giving up.")}this._errorProcessing(t,n||this.options.dictResponseError.replace("{{statusCode}}",i.status),i)}}},{key:"submitRequest",value:function(t,e,i){t.send(e)}},{key:"_finished",value:function(t,i,n){var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.status=e.SUCCESS,this.emit("success",c,i,n),this.emit("complete",c)}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}if(this.options.uploadMultiple&&(this.emit("successmultiple",t,i,n),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}},{key:"_errorProcessing",value:function(t,i,n){var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;c.status=e.ERROR,this.emit("error",c,i,n),this.emit("complete",c)}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}if(this.options.uploadMultiple&&(this.emit("errormultiple",t,i,n),this.emit("completemultiple",t)),this.options.autoProcessQueue)return this.processQueue()}}],[{key:"uuidv4",value:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})}}]),e}(h);f.initClass(),f.version="5.7.0",f.options={},f.optionsForElement=function(t){return t.getAttribute("id")?f.options[g(t.getAttribute("id"))]:void 0},f.instances=[],f.forElement=function(t){if("string"==typeof t&&(t=document.querySelector(t)),null==(null!=t?t.dropzone:void 0))throw new Error("No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.");return t.dropzone},f.autoDiscover=!0,f.discover=function(){var t;if(document.querySelectorAll)t=document.querySelectorAll(".dropzone");else{t=[];var e=function(e){return function(){var i=[],n=!0,o=!1,a=void 0;try{for(var r,s=e[Symbol.iterator]();!(n=(r=s.next()).done);n=!0){var l=r.value;/(^| )dropzone($| )/.test(l.className)?i.push(t.push(l)):i.push(void 0)}}catch(t){o=!0,a=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw a}}return i}()};e(document.getElementsByTagName("div")),e(document.getElementsByTagName("form"))}return function(){var e=[],i=!0,n=!1,o=void 0;try{for(var a,r=t[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var s=a.value;!1!==f.optionsForElement(s)?e.push(new f(s)):e.push(void 0)}}catch(t){n=!0,o=t}finally{try{i||null==r.return||r.return()}finally{if(n)throw o}}return e}()},f.blacklistedBrowsers=[/opera.*(Macintosh|Windows Phone).*version\/12/i],f.isBrowserSupported=function(){var t=!0;if(window.File&&window.FileReader&&window.FileList&&window.Blob&&window.FormData&&document.querySelector)if("classList"in document.createElement("a")){var e=!0,i=!1,n=void 0;try{for(var o,a=f.blacklistedBrowsers[Symbol.iterator]();!(e=(o=a.next()).done);e=!0){var r=o.value;r.test(navigator.userAgent)&&(t=!1)}}catch(t){i=!0,n=t}finally{try{e||null==a.return||a.return()}finally{if(i)throw n}}}else t=!1;else t=!1;return t},f.dataURItoBlob=function(t){for(var e=atob(t.split(",")[1]),i=t.split(",")[0].split(":")[1].split(";")[0],n=new ArrayBuffer(e.length),o=new Uint8Array(n),a=0,r=e.length,s=0<=r;s?a<=r:a>=r;s?a++:a--)o[a]=e.charCodeAt(a);return new Blob([n],{type:i})};var m=function(t,e){return t.filter(function(t){return t!==e}).map(function(t){return t})},g=function(t){return t.replace(/[\-_](\w)/g,function(t){return t.charAt(1).toUpperCase()})};f.createElement=function(t){var e=document.createElement("div");return e.innerHTML=t,e.childNodes[0]},f.elementInside=function(t,e){if(t===e)return!0;for(;t=t.parentNode;)if(t===e)return!0;return!1},f.getElement=function(t,e){var i;if("string"==typeof t?i=document.querySelector(t):null!=t.nodeType&&(i=t),null==i)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector or a plain HTML element."));return i},f.getElements=function(t,e){var i,n;if(t instanceof Array){n=[];try{var o=!0,a=!1,r=void 0;try{for(var s,l=t[Symbol.iterator]();!(o=(s=l.next()).done);o=!0)i=s.value,n.push(this.getElement(i,e))}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}}catch(t){n=null}}else if("string"==typeof t){n=[];var c=!0,d=!1,u=void 0;try{for(var p,h=document.querySelectorAll(t)[Symbol.iterator]();!(c=(p=h.next()).done);c=!0)i=p.value,n.push(i)}catch(t){d=!0,u=t}finally{try{c||null==h.return||h.return()}finally{if(d)throw u}}}else null!=t.nodeType&&(n=[t]);if(null==n||!n.length)throw new Error("Invalid `".concat(e,"` option provided. Please provide a CSS selector, a plain HTML element or a list of those."));return n},f.confirm=function(t,e,i){return window.confirm(t)?e():null!=i?i():void 0},f.isValidFile=function(t,e){if(!e)return!0;e=e.split(",");var i=t.type,n=i.replace(/\/.*$/,""),o=!0,a=!1,r=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done);o=!0){var c=s.value;if(c=c.trim(),"."===c.charAt(0)){if(-1!==t.name.toLowerCase().indexOf(c.toLowerCase(),t.name.length-c.length))return!0}else if(/\/\*$/.test(c)){if(n===c.replace(/\/.*$/,""))return!0}else if(i===c)return!0}}catch(t){a=!0,r=t}finally{try{o||null==l.return||l.return()}finally{if(a)throw r}}return!1},void 0!==t&&null!==t&&(t.fn.dropzone=function(t){return this.each(function(){return new f(this,t)})}),void 0!==p&&null!==p?p.exports=f:window.Dropzone=f,f.ADDED="added",f.QUEUED="queued",f.ACCEPTED=f.QUEUED,f.UPLOADING="uploading",f.PROCESSING=f.UPLOADING,f.CANCELED="canceled",f.ERROR="error",f.SUCCESS="success";var v=function(t){var e=(t.naturalWidth,t.naturalHeight),i=document.createElement("canvas");i.width=1,i.height=e;var n=i.getContext("2d");n.drawImage(t,0,0);for(var o=n.getImageData(1,0,1,e),a=o.data,r=0,s=e,l=e;l>r;){0===a[4*(l-1)+3]?s=l:r=l,l=s+r>>1}var c=l/e;return 0===c?1:c},y=function(t,e,i,n,o,a,r,s,l,c){var d=v(e);return t.drawImage(e,i,n,o,a,r,s,l,c/d)},b=function(){function t(){s(this,t)}return c(t,null,[{key:"initClass",value:function(){this.KEY_STR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}},{key:"encode64",value:function(t){for(var e="",i=void 0,n=void 0,o="",a=void 0,r=void 0,s=void 0,l="",c=0;;)if(i=t[c++],n=t[c++],o=t[c++],a=i>>2,r=(3&i)<<4|n>>4,s=(15&n)<<2|o>>6,l=63&o,isNaN(n)?s=l=64:isNaN(o)&&(l=64),e=e+this.KEY_STR.charAt(a)+this.KEY_STR.charAt(r)+this.KEY_STR.charAt(s)+this.KEY_STR.charAt(l),i=n=o="",a=r=s=l="",!(c<t.length))break;return e}},{key:"restore",value:function(t,e){if(!t.match("data:image/jpeg;base64,"))return e;var i=this.decode64(t.replace("data:image/jpeg;base64,","")),n=this.slice2Segments(i),o=this.exifManipulation(e,n);return"data:image/jpeg;base64,".concat(this.encode64(o))}},{key:"exifManipulation",value:function(t,e){var i=this.getExifArray(e),n=this.insertExif(t,i);return new Uint8Array(n)}},{key:"getExifArray",value:function(t){for(var e=void 0,i=0;i<t.length;){if(e=t[i],255===e[0]&225===e[1])return e;i++}return[]}},{key:"insertExif",value:function(t,e){var i=t.replace("data:image/jpeg;base64,",""),n=this.decode64(i),o=n.indexOf(255,3),a=n.slice(0,o),r=n.slice(o),s=a;return s=s.concat(e),s=s.concat(r)}},{key:"slice2Segments",value:function(t){for(var e=0,i=[];;){var n;if(255===t[e]&218===t[e+1])break;if(255===t[e]&216===t[e+1])e+=2;else{n=256*t[e+2]+t[e+3];var o=e+n+2,a=t.slice(e,o);i.push(a),e=o}if(e>t.length)break}return i}},{key:"decode64",value:function(t){var e=void 0,i=void 0,n="",o=void 0,a=void 0,r=void 0,s="",l=0,c=[],d=/[^A-Za-z0-9\+\/\=]/g;for(d.exec(t)&&console.warn("There were invalid base64 characters in the input text.\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\nExpect errors in decoding."),t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");;)if(o=this.KEY_STR.indexOf(t.charAt(l++)),a=this.KEY_STR.indexOf(t.charAt(l++)),r=this.KEY_STR.indexOf(t.charAt(l++)),s=this.KEY_STR.indexOf(t.charAt(l++)),e=o<<2|a>>4,i=(15&a)<<4|r>>2,n=(3&r)<<6|s,c.push(e),64!==r&&c.push(i),64!==s&&c.push(n),e=i=n="",o=a=r=s="",!(l<t.length))break;return c}}]),t}();b.initClass();return f._autoDiscoverFunction=function(){if(f.autoDiscover)return f.discover()},function(t,e){var i=!1,n=!0,o=t.document,a=o.documentElement,r=o.addEventListener?"addEventListener":"attachEvent",s=o.addEventListener?"removeEventListener":"detachEvent",l=o.addEventListener?"":"on",c=function n(a){if("readystatechange"!==a.type||"complete"===o.readyState)return("load"===a.type?t:o)[s](l+a.type,n,!1),!i&&(i=!0)?e.call(t,a.type||a):void 0};if("complete"!==o.readyState){if(o.createEventObject&&a.doScroll){try{n=!t.frameElement}catch(t){}n&&function t(){try{a.doScroll("left")}catch(e){return void setTimeout(t,50)}return c("poll")}()}o[r](l+"DOMContentLoaded",c,!1),o[r](l+"readystatechange",c,!1),t[r](l+"load",c,!1)}}(window,f._autoDiscoverFunction),p.exports}),define("upload",["jquery","bootstrap","dropzone","template"],function(t,e,i,n){var o={list:{},options:{},config:{container:document.body,classname:".plupload:not([initialized]),.faupload:not([initialized])",previewtpl:'<li class="col-xs-3"><a href="<%=fullurl%>" data-url="<%=url%>" target="_blank" class="thumbnail"><img src="<%=fullurl%>" onerror="this.src=\''+Fast.api.fixurl("ajax/icon")+'?suffix=<%=suffix%>\';this.onerror=null;" class="img-responsive"></a><a href="javascript:;" class="btn btn-danger btn-xs btn-trash"><i class="fa fa-trash"></i></a></li>'},events:{onInit:function(){},onUploadSuccess:function(e,i,n){var a=e.element,r=e.options.onUploadSuccess,s=void 0!==i.data?i.data:null;if(a){var l=t(a).data("input-id")?t(a).data("input-id"):"";if(l){var c=[],d=t("#"+l);t(a).data("multiple")&&""!==d.val()&&c.push(d.val());var u=Config.upload.fullmode?s.fullurl?s.fullurl:Fast.api.cdnurl(s.url):s.url;c.push(u),d.val(c.join(",")).trigger("change").trigger("validate")}var p=t(a).data("upload-success");if(p&&("function"!=typeof p&&"function"==typeof o.api.custom[p]&&(p=o.api.custom[p]),"function"==typeof p)){var h=p.call(a,s,i,e,n);if(!1===h)return}}if("function"==typeof r){var h=r.call(a,s,i,e,n);if(!1===h)return}},onUploadError:function(e,i,n){var a=e.element,r=e.options.onUploadError,s=void 0!==i.data?i.data:null;if(a){var l=t(a).data("upload-error");if(l&&("function"!=typeof l&&"function"==typeof o.api.custom[l]&&(l=o.api.custom[l]),"function"==typeof l)){var c=l.call(a,s,i,e,n);if(!1===c)return}}if("function"==typeof r){var c=r.call(a,s,i,e,n);if(!1===c)return}Toastr.error(i.msg.toString().replace(/(<([^>]+)>)/gi,"")+"(code:"+i.code+")")},onUploadResponse:function(e,i,n){try{var o="object"==typeof e?e:JSON.parse(e);o.hasOwnProperty("code")||t.extend(o,{code:-2,msg:e,data:null})}catch(t){var o={code:-1,msg:t.message,data:null}}return o},onUploadComplete:function(e,i){var n=e.element,a=e.options.onUploadComplete;if(n){var r=t(n).data("upload-complete");if(r&&("function"!=typeof r&&"function"==typeof o.api.custom[r]&&(r=o.api.custom[r]),"function"==typeof r)){var s=r.call(n,i);if(!1===s)return}}if("function"==typeof a){var s=a.call(n,i);if(!1===s)return}}},api:{upload:function(e,a,r,s){e=void 0===e?o.config.classname:e,t(e,o.config.container).each(function(){if(t(this).attr("initialized"))return!0;t(this).attr("initialized",!0);var e=this,l=t(this).prop("id")||t(this).prop("name")||i.uuidv4(),c=t(this).data("url"),d=t(this).data("maxsize"),u=t(this).data("maxcount"),p=t(this).data("mimetype"),h=t(this).data("multipart"),f=t(this).data("multiple"),m=t(e).data("input-id")?t(e).data("input-id"):"",g=t(e).data("preview-id")?t(e).data("preview-id"):"";c=c||Config.upload.uploadurl,c=Fast.api.fixurl(c);var v=Config.upload.chunksize||2097152,y=Config.upload.timeout||6e5;d=void 0!==d?d:Config.upload.maxsize,p=void 0!==p?p:Config.upload.mimetype,h=void 0!==h?h:Config.upload.multipart,f=void 0!==f?f:Config.upload.multiple,p=p.split(",").map(function(t){return t.indexOf("/")>-1?t:t&&"*"!==t&&"."!==t.charAt(0)?"."+t:t}).join(","),p="*"===p?null:p;var b=function(t){var e=t.toString().match(/^([0-9\.]+)(\w+)$/),i=e?parseFloat(e[1]):parseFloat(t),n=e?e[2].toLowerCase():"b",o={b:0,k:1,kb:1,m:2,mb:2,gb:3,g:3,tb:4,t:4},a=void 0!==o[n]?o[n]:0;return i*Math.pow(1024,a)/Math.pow(1024,2)}(d),x=t(this).data()||{};x=t.extend(!0,{},x,t(this).data("upload-options")||{}),delete x.success,delete x.url,h=t.isArray(h)?{}:h;var w=t(this).data("params")||{};void 0!==w.category||t(this).data("category");o.list[l]=new i(this,t.extend({url:c,params:function(e,i,n){var o=h;return n?t.extend({},o,{filesize:n.file.size,filename:n.file.name,chunkid:n.file.upload.uuid,chunkindex:n.index,chunkcount:n.file.upload.totalChunkCount,chunksize:this.options.chunkSize,chunkfilesize:n.dataBlock.data.size,width:n.file.width||0,height:n.file.height||0,type:n.file.type}):o},chunking:!1,chunkSize:v,maxFilesize:b,acceptedFiles:p,maxFiles:u&&parseInt(u)>1?u:f?null:1,timeout:y,parallelUploads:1,previewsContainer:!1,dictDefaultMessage:__("Drop files here to upload"),dictFallbackMessage:__("Your browser does not support drag'n'drop file uploads"),dictFallbackText:__("Please use the fallback form below to upload your files like in the olden days"),dictFileTooBig:__("File is too big (%sMiB), Max filesize: %sMiB","{{filesize}}","{{maxFilesize}}"),dictInvalidFileType:__("You can't upload files of this type"),dictResponseError:__("Server responded with %s code.","{{statusCode}}"),dictCancelUpload:__("Cancel upload"),dictUploadCanceled:__("Upload canceled"),dictCancelUploadConfirmation:__("Are you sure you want to cancel this upload?"),dictRemoveFile:__("Remove file"),dictMaxFilesExceeded:__("You can only upload a maximum of %s files","{{maxFiles}}"),init:function(){o.events.onInit.call(this),t(">i",this.element).addClass("dz-message"),this.options.elementHtml=t(this.element).html()},sending:function(t,e,i){void 0!==t.category&&i.append("category",t.category)},addedfile:function(e){var i=t(this.element).data("params")||{},n=void 0!==i.category?i.category:t(this.element).data("category")||"";e.category="function"==typeof n?n.call(this,e):n},addedfiles:function(e){if(this.options.maxFiles&&(!this.options.maxFiles||this.options.maxFiles>1)&&this.options.inputId){var i=t("#"+this.options.inputId);if(i.length>0){var n=t.trim(i.val()),o=""===n?0:n.split(/\,/).length,a=this.options.maxFiles-o;if(0===a||e.length>a){e=Array.prototype.slice.call(e,a);for(var r=0;r<e.length;r++)this.removeFile(e[r]);Toastr.error(__("You can only upload a maximum of %s files",this.options.maxFiles))}}}},success:function(t,e){var i=o.events.onUploadResponse(e,this,t);t.ret=i,1===i.code?o.events.onUploadSuccess(this,i,t):o.events.onUploadError(this,i,t)},error:function(e,i,n){var a=t("<div>"+(n&&void 0!==n.responseText?n.responseText:i)+"</div>");a.find("style, title, script").remove();var r=a.text()||__("Network error"),s={code:0,data:null,msg:r};o.events.onUploadError(this,s,e)},uploadprogress:function(e,i,n){e.upload.chunked&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e.upload.bytesSent/e.size*100)+"%")},totaluploadprogress:function(e,i){this.getActiveFiles().length>0&&!this.options.chunking&&t(this.element).prop("disabled",!0).html("<i class='fa fa-upload'></i> "+__("Upload")+Math.floor(e)+"%")},queuecomplete:function(){o.events.onUploadComplete(this,this.files),this.removeAllFiles(!0),t(this.element).prop("disabled",!1).html(this.options.elementHtml)},chunkSuccess:function(t,e,i){},chunksUploaded:function(e,i){var n=this;Fast.api.ajax({url:this.options.url,data:t.extend({},h,{action:"merge",filesize:e.size,filename:e.name,chunkid:e.upload.uuid,chunkcount:e.upload.totalChunkCount})},function(t,e){return i(JSON.stringify(e)),!1},function(t,i){e.accepted=!1,n._errorProcessing([e],i.msg)})},onUploadSuccess:a,onUploadError:r,onUploadComplete:s},o.options,x)),g&&f&&require(["dragsort"],function(){t("#"+g).dragsort({dragSelector:"li a:not(.btn-trash)",dragEnd:function(){t("#"+g).trigger("fa.preview.change")},placeHolderTemplate:'<li class="col-xs-3"></li>'})});var _=function(e){var i={},n=t("textarea[name='"+e+"']"),o=n.prev("ul");t.each(t("input,select,textarea",o).serializeArray(),function(t,e){var n=/\[?(\w+)\]?\[(\w+)\]$/g,o=n.exec(e.name);if(!o)return!0;isNaN(o[2])?(o[1]="x"+parseInt(o[1]),void 0===i[o[1]]&&(i[o[1]]={}),i[o[1]][o[2]]=e.value):i[t]=e.value});var a=[];t.each(i,function(t,e){a.push(e)}),n.val(JSON.stringify(a))};g&&m&&(t(document.body).on("keyup change","#"+m,function(i){var a=t("#"+m).val(),r=a.split(/\,/),s=t("#"+g);s.empty();var l=s.data("template")?s.data("template"):"",c=s.next().is("textarea")?s.next("textarea").val():"{}",d={};try{d=JSON.parse(c)}catch(i){}t.each(r,function(i,a){if(!a)return!0;var r=/[\.]?([a-zA-Z0-9]+)$/.exec(a);r=r?r[1]:"file";var c=t(e).data(),u=void 0!==c.cdnurl?Fast.api.cdnurl(a,c.cdnurl):Fast.api.cdnurl(a);a=Config.upload.fullmode?u:a;var p=d&&void 0!==d[i]?d[i]:null,h={url:a,fullurl:u,data:c,key:i,index:i,value:p,row:p,suffix:r},f=l?n(l,h):n.render(o.config.previewtpl,h);s.append(f)}),_(s.data("name"))}),t("#"+m).trigger("change")),g&&(t("#"+g).on("change keyup","input,textarea,select",function(){_(t(this).closest("ul").data("name"))}),t(document.body).on("fa.preview.change","#"+g,function(){var e=[];t("#"+g+" [data-url]").each(function(i,n){e.push(t(this).data("url"))}),m&&t("#"+m).val(e.join(",")),_(t("#"+g).data("name"))}),t(document.body).on("click","#"+g+" .btn-trash",function(){t(this).closest("li").remove(),t("#"+g).trigger("fa.preview.change")})),m&&(t("#"+m).closest("form").on("reset",function(){setTimeout(t.proxy(function(){t("#"+m,this).trigger("change")},this),0)}),t("body").on("paste drop","#"+m,function(e){var i=e.originalEvent,n=t(".plupload[data-input-id='"+t(this).attr("id")+"'],.faupload[data-input-id='"+t(this).attr("id")+"']");if("paste"===e.type&&i.clipboardData&&i.clipboardData.items){var a=i.clipboardData.items;if(!(1===a.length&&a[0].type.indexOf("text")>-1||2===a.length&&a[1].type.indexOf("text")>-1))return o.list[n.attr("id")].paste(i),!1}if("drop"===e.type&&i.dataTransfer&&i.dataTransfer.files)return o.list[n.attr("id")].drop(i),!1}))})},plupload:function(t,e,i,n){return o.api.upload(t,e,i,n)},faupload:function(t,e,i,n){return o.api.upload(t,e,i,n)},send:function(e,n,a,r){var s=Layer.msg(__("Uploading"),{offset:"t",time:0}),l="dropzone-"+i.uuidv4();t('<button type="button" id="'+l+'" class="btn btn-danger hidden faupload" />').appendTo("body"),t("#"+l).data("upload-complete",function(t){Layer.close(s),o.list[l].removeAllFiles(!0)}),
o.api.upload("#"+l,n,a,r),setTimeout(function(){o.list[l].addFile(e)},1)},custom:{afteruploadcallback:function(t){console.log(this,t),alert("Custom Callback,Response URL:"+t.url)}}}};return o}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator",["jquery"],t):t(jQuery)}(function(t,e){"use strict";function i(e,n){function o(){a.$el=t(e),a.$el.length?a._init(a.$el[0],n):O(e)&&(R[e]=n)}var a=this;if(!(a instanceof i))return new i(e,n);i.pending?t(window).on("validatorready",o):o()}function n(e){function i(){var e=this.options;for(var i in e)i in I&&(this[i]=e[i]);t.extend(this,{_valHook:function(){return"true"===this.element.contentEditable?"text":"val"},getValue:function(){var e=this.element;return"number"===e.type&&e.validity&&e.validity.badInput?"NaN":t(e)[this._valHook()]()},setValue:function(e){t(this.element)[this._valHook()](this.value=e)},getRangeMsg:function(t,e,i){function n(t,e){return l?t>e:t>=e}if(e){var o,a=this,r=a.messages[a._r]||"",s=e[0].split("~"),l="false"===e[1],c=s[0],d=s[1],u="rg",p=[""],h=F(t)&&+t==+t;return 2===s.length?c&&d?(h&&n(t,+c)&&n(+d,t)&&(o=!0),p=p.concat(s),u=l?"gtlt":"rg"):c&&!d?(h&&n(t,+c)&&(o=!0),p.push(c),u=l?"gt":"gte"):!c&&d&&(h&&n(+d,t)&&(o=!0),p.push(d),u=l?"lt":"lte"):(t===+c&&(o=!0),p.push(c),u="eq"),r&&(i&&r[u+i]&&(u+=i),p[0]=r[u]),o||a._rules&&(a._rules[a._i].msg=a.renderMsg.apply(null,p))}},renderMsg:function(){var t=arguments,e=t[0],i=t.length;if(e){for(;--i;)e=e.replace("{"+i+"}",t[i]);return e}}})}function n(i,n,o){this.key=i,this.validator=e,t.extend(this,o,n)}return i.prototype=e,n.prototype=new i,n}function o(t,e){if(M(t)){var i,n=e?!0===e?this:e:o.prototype;for(i in t)h(i)&&(n[i]=r(t[i]))}}function a(t,e){if(M(t)){var i,n=e?!0===e?this:e:a.prototype;for(i in t)n[i]=t[i]}}function r(e){switch(t.type(e)){case"function":return e;case"array":var i=function(){return e[0].test(this.value)||e[1]||!1};return i.msg=e[1],i;case"regexp":return function(){return e.test(this.value)}}}function s(e){var i,n,o;if(e&&e.tagName){switch(e.tagName){case"INPUT":case"SELECT":case"TEXTAREA":case"BUTTON":case"FIELDSET":i=e.form||t(e).closest("."+y);break;case"FORM":i=e;break;default:i=t(e).closest("."+y)}for(n in R)if(t(i).is(n)){o=R[n];break}return t(i).data(g)||t(i)[g](o).data(g)}}function l(t,e){var i=F(N(t,b+"-"+e));if(i&&(i=new Function("return "+i)()))return r(i)}function c(t,e,i){var n=e.msg,o=e._r;return M(n)&&(n=n[o]),O(n)||(n=N(t,x+"-"+o)||N(t,x)||(i?O(i)?i:i[o]:"")),n}function d(t){var e;return t&&(e=S.exec(t)),e&&e[0]}function u(t){return"INPUT"===t.tagName&&"checkbox"===t.type||"radio"===t.type}function p(t){return Date.parse(t.replace(/\.|\-/g,"/"))}function h(t){return/^\w+$/.test(t)}function f(t){var e="#"===t.charAt(0);return t=t.replace(/([:.{(|)}/\[\]])/g,"\\$1"),e?t:'[name="'+t+'"]:first'}var m,g="validator",v="."+g,y="nice-"+g,b="data-rule",x="data-msg",w=/(&)?(!)?\b(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?\s*(;|\|)?/g,_=/(\w+)(?:\[\s*(.*?\]?)\s*\]|\(\s*(.*?\)?)\s*\))?/,k=/(?:([^:;\(\[]*):)?(.*)/,C=/[^\x00-\xff]/g,S=/top|right|bottom|left/,T=/(?:(cors|jsonp):)?(?:(post|get):)?(.+)/i,D=/[<>'"`\\]|&#x?\d+[A-F]?;?|%3[A-F]/gim,$=t.noop,E=t.proxy,F=t.trim,A=t.isFunction,O=function(t){return"string"==typeof t},M=function(t){return t&&"[object Object]"===Object.prototype.toString.call(t)},L=document.documentMode||+(navigator.userAgent.match(/MSIE (\d+)/)&&RegExp.$1),N=function(t,i,n){return t&&t.tagName?n===e?t.getAttribute(i):void(null===n?t.removeAttribute(i):t.setAttribute(i,""+n)):null},R={},P={debug:0,theme:"default",ignore:"",focusInvalid:!0,focusCleanup:!1,stopOnError:!1,beforeSubmit:null,valid:null,invalid:null,validation:null,formClass:"n-default",validClass:"n-valid",invalidClass:"n-invalid",bindClassTo:null,remoteDataType:"cors"},I={timely:1,display:null,target:null,ignoreBlank:!1,showOk:!0,dataFilter:function(t){if(O(t)||M(t)&&("error"in t||"ok"in t))return t},msgMaker:function(e){var i;return i='<span role="alert" class="msg-wrap n-'+e.type+'">'+e.arrow,e.result?t.each(e.result,function(t,n){i+='<span class="n-'+n.type+'">'+e.icon+'<span class="n-msg">'+n.msg+"</span></span>"}):i+=e.icon+'<span class="n-msg">'+e.msg+"</span>",i+="</span>"},msgWrapper:"span",msgArrow:"",msgIcon:'<span class="n-icon"></span>',msgClass:"n-right",msgStyle:"",msgShow:null,msgHide:null},j={};return t.fn.validator=function(e){var n=this,o=arguments;return n.is(":verifiable")?n:(n.is("form")||(n=this.find("form")),n.length||(n=this),n.each(function(){var n=t(this).data(g);if(n)if(O(e)){if("_"===e.charAt(0))return;n[e].apply(n,[].slice.call(o,1))}else e&&(n._reset(!0),n._init(this,e));else new i(this,e)}),this)},t.fn.isValid=function(t,i){var n,o,a=s(this[0]),r=A(t);return!a||(r||i!==e||(i=t),a.checkOnly=!!i,o=a.options,n=a._multiValidate(this.is(":verifiable")?this:this.find(":verifiable"),function(e){e||!o.focusInvalid||a.checkOnly||a.$el.find("[aria-invalid]:first").focus(),r&&(t.length?t(e):e&&t()),a.checkOnly=!1}),r?this:n)},t.extend(t.expr.pseudos||t.expr[":"],{verifiable:function(t){var e=t.nodeName.toLowerCase();return("input"===e&&!{submit:1,button:1,reset:1,image:1}[t.type]||"select"===e||"textarea"===e||"true"===t.contentEditable)&&!t.disabled},filled:function(e){return!!F(t(e).val())}}),i.prototype={_init:function(e,i){var r,s,l,c=this;A(i)&&(i={valid:i}),i=c._opt=i||{},l=N(e,"data-"+g+"-option"),l=c._dataOpt=l&&"{"===l.charAt(0)?new Function("return "+l)():{},s=c._themeOpt=j[i.theme||l.theme||P.theme],r=c.options=t.extend({},P,I,s,c.options,i,l),c.rules=new o(r.rules,!0),c.messages=new a(r.messages,!0),c.Field=n(c),c.elements=c.elements||{},c.deferred={},c.errors={},c.fields={},c._initFields(r.fields),c.$el.data(g)||(c.$el.data(g,c).addClass(y+" "+r.formClass).on("form-submit-validate",function(t,e,i,n,o){c.vetoed=o.veto=!c.isValid,c.ajaxFormOptions=n}).on("submit"+v+" validate"+v,E(c,"_submit")).on("reset"+v,E(c,"_reset")).on("showmsg"+v,E(c,"_showmsg")).on("hidemsg"+v,E(c,"_hidemsg")).on("focusin"+v+" click"+v,":verifiable",E(c,"_focusin")).on("focusout"+v+" validate"+v,":verifiable",E(c,"_focusout")).on("keyup"+v+" input"+v+" compositionstart compositionend",":verifiable",E(c,"_focusout")).on("click"+v,":radio,:checkbox","click",E(c,"_focusout")).on("change"+v,'select,input[type="file"]',"change",E(c,"_focusout")),c._NOVALIDATE=N(e,"novalidate"),N(e,"novalidate","novalidate")),O(r.target)&&c.$el.find(r.target).addClass("msg-container")},_guessAjax:function(e){function i(e,i,n){return!!(e&&e[i]&&t.map(e[i],function(t){return~t.namespace.indexOf(n)?1:null}).length)}var n=this;if(!(n.isAjaxSubmit=!!n.options.valid)){var o=(t._data||t.data)(e,"events");n.isAjaxSubmit=i(o,"valid","form")||i(o,"submit","form-plugin")}},_initFields:function(t){function e(t,e){if(null===e||r){var i=a.elements[t];i&&a._resetElement(i,!0),delete a.fields[t]}else a.fields[t]=new a.Field(t,O(e)?{rule:e}:e,a.fields[t])}var i,n,o,a=this,r=null===t;if(r&&(t=a.fields),M(t))for(i in t)if(~i.indexOf(","))for(n=i.split(","),o=n.length;o--;)e(F(n[o]),t[i]);else e(i,t[i]);a.$el.find(":verifiable").each(function(){a._parse(this)})},_parse:function(t){var e,i,n,o=this,a=t.name,r=N(t,b);return r&&N(t,b,null),t.id&&("#"+t.id in o.fields||!a||null!==r&&(e=o.fields[a])&&r!==e.rule&&t.id!==e.key)&&(a="#"+t.id),a||(a="#"+(t.id="N"+String(Math.random()).slice(-12))),e=o.getField(a,!0),e.rule=r||e.rule,(i=N(t,"data-display"))&&(e.display=i),e.rule&&((null!==N(t,"data-must")||/\b(?:match|checked)\b/.test(e.rule))&&(e.must=!0),/\brequired\b/.test(e.rule)&&(e.required=!0),(n=N(t,"data-timely"))?e.timely=+n:e.timely>3&&N(t,"data-timely",e.timely),o._parseRule(e),e.old={}),O(e.target)&&N(t,"data-target",e.target),O(e.tip)&&N(t,"data-tip",e.tip),o.fields[a]=e},_parseRule:function(i){var n=k.exec(i.rule);n&&(i._i=0,n[1]&&(i.display=n[1]),n[2]&&(i._rules=[],n[2].replace(w,function(){var n=arguments;n[4]=n[4]||n[5],i._rules.push({and:"&"===n[1],not:"!"===n[2],or:"|"===n[6],method:n[3],params:n[4]?t.map(n[4].split(", "),F):e})})))},_multiValidate:function(i,n){var o=this,a=o.options;return o.hasError=!1,a.ignore&&(i=i.not(a.ignore)),i.each(function(){if(o._validate(this),o.hasError&&a.stopOnError)return!1}),n&&(o.validating=!0,t.when.apply(null,t.map(o.deferred,function(t){return t})).done(function(){n.call(o,!o.hasError),o.validating=!1})),t.isEmptyObject(o.deferred)?!o.hasError:e},_submit:function(i){var n=this,o=n.options,a=i.target,r="submit"===i.type&&"FORM"===a.tagName&&!i.isDefaultPrevented();i.preventDefault(),m&&~(m=!1)||n.submiting||"validate"===i.type&&n.$el[0]!==a||A(o.beforeSubmit)&&!1===o.beforeSubmit.call(n,a)||(n.isAjaxSubmit===e&&n._guessAjax(a),n._debug("log","\n<<< event: "+i.type),n._reset(),n.submiting=!0,n._multiValidate(n.$el.find(":verifiable"),function(e){var i,s=e||2===o.debug?"valid":"invalid";e||(o.focusInvalid&&n.$el.find("[aria-invalid]:first").focus(),i=t.map(n.errors,function(t){return t})),n.submiting=!1,n.isValid=e,A(o[s])&&o[s].call(n,a,i),n.$el.trigger(s+".form",[a,i]),n._debug("log",">>> "+s),e&&(n.vetoed?t(a).ajaxSubmit(n.ajaxFormOptions):r&&!n.isAjaxSubmit&&document.createElement("form").submit.call(a))}))},_reset:function(t){var e=this;e.errors={},t&&(e.reseting=!0,e.$el.find(":verifiable").each(function(){e._resetElement(this)}),delete e.reseting)},_resetElement:function(t,e){this._setClass(t,null),this.hideMsg(t)},_focusin:function(t){var e,i,n=this,o=n.options,a=t.target;n.validating||"click"===t.type&&document.activeElement===a||(o.focusCleanup&&"true"===N(a,"aria-invalid")&&(n._setClass(a,null),n.hideMsg(a)),i=N(a,"data-tip"),i?n.showMsg(a,{type:"tip",msg:i}):(N(a,b)&&n._parse(a),(e=N(a,"data-timely"))&&(8!==e&&9!==e||n._focusout(t))))},_focusout:function(e){var i,n,o,a,r,s,l,c,d,p=this,h=p.options,f=e.target,m=e.type,g="focusin"===m,v="validate"===m,y=0;if("compositionstart"===m&&(p.pauseValidate=!0),"compositionend"===m&&(p.pauseValidate=!1),!p.pauseValidate&&(n=f.name&&u(f)?p.$el.find('input[name="'+f.name+'"]').get(0):f,(o=p.getField(n))&&o.rule)){if(i=o._e,o._e=m,d=o.timely,!v){if(!d||u(f)&&"click"!==m)return;if(r=o.getValue(),o.ignoreBlank&&!r&&!g)return void p.hideMsg(f);if("focusout"===m){if("change"===i)return;if(2===d||8===d){if(a=o.old,!r||!a)return;o.isValid&&!a.showOk?p.hideMsg(f):p._makeMsg(f,o,a)}}else{if(d<2&&!e.data)return;if((s=+new Date)-(f._ts||0)<100)return;if(f._ts=s,"keyup"===m){if("input"===i)return;if(l=e.keyCode,c={8:1,9:1,16:1,32:1,46:1},9===l&&!r)return;if(l<48&&!c[l])return}g||(y=d<100?"click"===m||"SELECT"===f.tagName?0:400:d)}}h.ignore&&t(f).is(h.ignore)||(clearTimeout(o._t),y?o._t=setTimeout(function(){p._validate(f,o)},y):(v&&(o.old={}),p._validate(f,o)))}},_setClass:function(e,i){var n=t(e),o=this.options;o.bindClassTo&&(n=n.closest(o.bindClassTo)),n.removeClass(o.invalidClass+" "+o.validClass),null!==i&&n.addClass(i?o.validClass:o.invalidClass)},_showmsg:function(t,e,i){var n=this,o=t.target;n.$el.is(o)?M(e)?n.showMsg(e):"tip"===e&&n.$el.find(":verifiable[data-tip]",o).each(function(){n.showMsg(this,{type:e,msg:i})}):n.showMsg(o,{type:e,msg:i})},_hidemsg:function(e){var i=t(e.target);i.is(":verifiable")&&this.hideMsg(i)},_validatedField:function(e,i,n){var o=this,a=o.options,r=i.isValid=n.isValid=!!n.isValid,s=r?"valid":"invalid";n.key=i.key,n.ruleName=i._r,n.id=e.id,n.value=i.value,o.elements[i.key]=n.element=e,o.isValid=o.$el[0].isValid=r?o.isFormValid():r,r?n.type="ok":(o.submiting&&(o.errors[i.key]=n.msg),o.hasError=!0),i.old=n,A(i[s])&&i[s].call(o,e,n),A(a.validation)&&a.validation.call(o,e,n),t(e).attr("aria-invalid",!r||null).trigger(s+".field",[n,o]),o.$el.triggerHandler("validation",[n,o]),o.checkOnly||(o._setClass(e,n.skip||"tip"===n.type?null:r),o._makeMsg.apply(o,arguments))},_makeMsg:function(e,i,n){i.msgMaker&&(n=t.extend({},n),"focusin"===i._e&&(n.type="tip"),this[n.showOk||n.msg||"tip"===n.type?"showMsg":"hideMsg"](e,n,i))},_validatedRule:function(i,n,o,a){n=n||u.getField(i),a=a||{};var r,s,l,d,u=this,p=n._r,h=n.timely,f=9===h||8===h,m=!1;if(null===o)return u._validatedField(i,n,{isValid:!0,skip:!0}),void(n._i=0);if(o===e?l=!0:!0===o||""===o?m=!0:O(o)?r=o:M(o)?o.error?r=o.error:(r=o.ok,m=!0):m=!!o,s=n._rules[n._i],s.not&&(r=e,m="required"===p||!m),s.or)if(m)for(;n._i<n._rules.length&&n._rules[n._i].or;)n._i++;else l=!0;else s.and&&(n.isValid||(l=!0));l?m=!0:(m&&!1!==n.showOk&&(d=N(i,"data-ok"),r=null===d?O(n.ok)?n.ok:r:d,!O(r)&&O(n.showOk)&&(r=n.showOk),O(r)&&(a.showOk=m)),m&&!f||(r=(c(i,n,r||s.msg||u.messages[p])||u.messages.fallback).replace(/\{0\|?([^\}]*)\}/,function(t,e){return u._getDisplay(i,n.display)||e||u.messages[0]})),m||(n.isValid=m),a.msg=r,t(i).trigger((m?"valid":"invalid")+".rule",[p,r])),!f||l&&!s.and||(m||n._m||(n._m=r),n._v=n._v||[],n._v.push({type:m?l?"tip":"ok":"error",msg:r||s.msg})),u._debug("log","   "+n._i+": "+p+" => "+(m||r)),(m||f)&&n._i<n._rules.length-1?(n._i++,u._checkRule(i,n)):(n._i=0,f?(a.isValid=n.isValid,a.result=n._v,a.msg=n._m||"",n.value||"focusin"!==n._e||(a.type="tip")):a.isValid=m,u._validatedField(i,n,a),delete n._m,delete n._v)},_checkRule:function(i,n){var o,a,r,s=this,c=n.key,d=n._rules[n._i],u=d.method,p=d.params;s.submiting&&s.deferred[c]||(r=n.old,n._r=u,r&&!n.must&&!d.must&&d.result!==e&&r.ruleName===u&&r.id===i.id&&n.value&&r.value===n.value?o=d.result:(a=l(i,u)||s.rules[u]||$,o=a.call(n,i,p,n),a.msg&&(d.msg=a.msg)),M(o)&&A(o.then)?(s.deferred[c]=o,n.isValid=e,!s.checkOnly&&s.showMsg(i,{type:"loading",msg:s.messages.loading},n),o.then(function(o,a,r){var l,c=F(r.responseText),u=n.dataFilter;/jsonp?/.test(this.dataType)?c=o:"{"===c.charAt(0)&&(c=t.parseJSON(c)),l=u.call(this,c,n),l===e&&(l=u.call(this,c.data,n)),d.data=this.data,d.result=n.old?l:e,s._validatedRule(i,n,l)},function(t,e){s._validatedRule(i,n,s.messages[e]||e)}).always(function(){delete s.deferred[c]})):s._validatedRule(i,n,o))},_validate:function(t,e){var i=this;if(!t.disabled&&null===N(t,"novalidate")&&(e=e||i.getField(t))&&(e._rules||i._parse(t),e._rules))return i._debug("info",e.key),e.isValid=!0,e.element=t,e.value=e.getValue(),e.required||e.must||e.value||u(t)?(i._checkRule(t,e),e.isValid):(i._validatedField(t,e,{isValid:!0}),!0)},_debug:function(t,e){window.console&&this.options.debug&&console[t](e)},test:function(t,i){var n,o,a,r,s=this,l=_.exec(i);return l&&(a=l[1])in s.rules&&(r=l[2]||l[3],r=r?r.split(", "):e,o=s.getField(t,!0),o._r=a,o.value=o.getValue(),n=s.rules[a].call(o,t,r)),!0===n||n===e||null===n},_getDisplay:function(t,e){return O(e)?e:A(e)?e.call(this,t):""},_getMsgOpt:function(e,i){var n=i||this.options;return t.extend({type:"error",pos:d(n.msgClass),target:n.target,wrapper:n.msgWrapper,style:n.msgStyle,cls:n.msgClass,arrow:n.msgArrow,icon:n.msgIcon},O(e)?{msg:e}:e)},_getMsgDOM:function(i,n){var o,a,r,s,l=t(i);if(l.is(":verifiable")?(r=n.target||N(i,"data-target"),r&&(r=A(r)?r.call(this,i):"#"===r.charAt(0)?t(r):this.$el.find(r),r.length&&(r.is(":verifiable")?(l=r,i=r.get(0)):r.hasClass("msg-box")?o=r:s=r)),o||(a=u(i)&&i.name||!i.id?i.name:i.id,o=(s||this.$el).find(n.wrapper+'.msg-box[for="'+a+'"]'))):o=l,!n.hide&&!o.length)if(o=t("<"+n.wrapper+">").attr({class:"msg-box"+(n.cls?" "+n.cls:""),style:n.style||e,for:a}),s)o.appendTo(s);else if(u(i)){var c=l.parent();o.appendTo(c.is("label")?c.parent():c)}else o[n.pos&&"right"!==n.pos?"insertBefore":"insertAfter"](l);return o},showMsg:function(e,i,n){if(e){var o,a,r,s,l=this,c=l.options;if(M(e)&&!e.jquery&&!i)return void t.each(e,function(t,e){var i=l.elements[t]||l.$el.find(f(t))[0];l.showMsg(i,e)});t(e).is(":verifiable")&&(n=n||l.getField(e)),(a=(n||c).msgMaker)&&(i=l._getMsgOpt(i,n),e=(e.name&&u(e)?l.$el.find('input[name="'+e.name+'"]'):t(e)).get(0),i.msg||"error"===i.type||null!==(r=N(e,"data-"+i.type))&&(i.msg=r),O(i.msg)&&(s=l._getMsgDOM(e,i),!S.test(s[0].className)&&s.addClass(i.cls),6===L&&"bottom"===i.pos&&(s[0].style.marginTop=t(e).outerHeight()+"px"),s.html(a.call(l,i))[0].style.display="",A(o=n&&n.msgShow||c.msgShow)&&o.call(l,s,i.type)))}},hideMsg:function(e,i,n){var o,a,r=this,s=r.options;e=t(e).get(0),t(e).is(":verifiable")&&(n=n||r.getField(e))&&(n.isValid||r.reseting)&&N(e,"aria-invalid",null),i=r._getMsgOpt(i,n),i.hide=!0,a=r._getMsgDOM(e,i),a.length&&(A(o=n&&n.msgHide||s.msgHide)?o.call(r,a,i.type):(a[0].style.display="none",a[0].innerHTML=""))},getField:function(t,i){var n,o,a=this;if(O(t))n=t,t=e;else{if(N(t,b))return a._parse(t);n=t.id&&"#"+t.id in a.fields||!t.name?"#"+t.id:t.name}return((o=a.fields[n])||i&&(o=new a.Field(n)))&&(o.element=t),o},setField:function(t,e){var i={};t&&(O(t)?i[t]=e:i=t,this._initFields(i))},isFormValid:function(){var t,e,i=this.fields;for(t in i)if(e=i[t],e._rules&&(e.required||e.must||e.value)&&!e.isValid)return!1;return!0},holdSubmit:function(t){this.submiting=t===e||t},cleanUp:function(){this._reset(1)},destroy:function(){this._reset(1),this.$el.off(v).removeData(g),N(this.$el[0],"novalidate",this._NOVALIDATE)}},t(window).on("beforeunload",function(){this.focus()}),t(document).on("click",":submit",function(){var t,e=this;e.form&&((t=e.getAttributeNode("formnovalidate"))&&null!==t.nodeValue||null!==N(e,"novalidate"))&&(m=!0)}).on("focusin submit validate","form,."+y,function(e){if(null===N(this,"novalidate")){var i,n=t(this);!n.data(g)&&(i=s(this))&&(t.isEmptyObject(i.fields)?(N(this,"novalidate","novalidate"),n.off(v).removeData(g)):"focusin"===e.type?i._focusin(e):i._submit(e))}}),new a({fallback:"This field is not valid.",loading:"Validating..."}),new o({required:function(e,i){var n=this,o=F(n.value),a=!0;if(i)if(1===i.length){if(h(i[0])){if(n.rules[i[0]]){if(!o&&!n.test(e,i[0]))return null;n._r="required"}}else if(!o&&!t(i[0],n.$el).length)return null}else if("not"===i[0])t.each(i.slice(1),function(){return a=o!==F(this)});else if("from"===i[0]){var r,s=n.$el.find(i[1]);return a=s.filter(function(){var t=n.getField(this);return t&&!!F(t.getValue())}).length>=(i[2]||1),a?o||(r=null):r=c(s[0],n)||!1,t(e).data("_validated_")||s.data("_validated_",1).each(function(){e!==this&&n._validate(this)}).removeData("_validated_"),r}return a&&!!o},integer:function(t,e){var i,n="0|",o="[1-9]\\d*",a=e?e[0]:"*";switch(a){case"+":i=o;break;case"-":i="-"+o;break;case"+0":i=n+o;break;case"-0":i="0|-"+o;break;default:i="0|-?"+o}return i="^(?:"+i+")$",new RegExp(i).test(this.value)||this.messages.integer&&this.messages.integer[a]},match:function(e,i){if(i){var n,o,a,r,s,l,c,d=this,u=!0,h="eq";if(1===i.length?a=i[0]:(h=i[0],a=i[1]),s=f(a),l=d.$el.find(s)[0]){if(c=d.getField(l),n=d.value,o=c.getValue(),d._match||(d.$el.on("valid.field"+v,s,function(){t(e).trigger("validate")}),d._match=c._match=1),!d.required&&""===n&&""===o)return null;if(r=i[2],r&&(/^date(time)?$/i.test(r)?(n=p(n),o=p(o)):"time"===r&&(n=+n.replace(/:/g,""),o=+o.replace(/:/g,""))),"eq"!==h&&!isNaN(+n)&&isNaN(+o))return!0;switch(h){case"lt":u=+n<+o;break;case"lte":u=+n<=+o;break;case"gte":u=+n>=+o;break;case"gt":u=+n>+o;break;case"neq":u=n!==o;break;default:u=n===o}return u||M(d.messages.match)&&d.messages.match[h].replace("{1}",d._getDisplay(l,c.display||a))}}},range:function(t,e){return this.getRangeMsg(this.value,e)},checked:function(t,e){if(u(t)){var i,n,o=this;return t.name?n=o.$el.find('input[name="'+t.name+'"]').filter(function(){var t=this;return!i&&u(t)&&(i=t),!t.disabled&&t.checked}).length:(i=t,n=i.checked),e?o.getRangeMsg(n,e):!!n||c(i,o,"")||o.messages.required||!1}},length:function(t,e){var i=this.value,n=("true"===e[1]?i.replace(C,"xx"):i).length;return this.getRangeMsg(n,e,e[1]?"_2":"")},remote:function(e,i){if(i){var n,o=this,a=T.exec(i[0]),r=o._rules[o._i],s={},l="",c=a[3],d=a[2]||"POST",u=(a[1]||this.validator.options.remoteDataType||"").toLowerCase();return r.must=!0,(s[e.name]=o.value,i[1]&&t.map(i.slice(1),function(t){var e,i;~t.indexOf("=")?l+="&"+t:(e=t.split(":"),t=F(e[0]),i=F(e[1])||t,s[t]=o.$el.find(f(i)).val())}),s=t.param(s)+l,!o.must&&r.data&&r.data===s)?r.result:("cors"!==u&&/^https?:/.test(c)&&!~c.indexOf(location.host)&&(n="jsonp"),t.ajax({url:c,type:d,data:s,dataType:n}))}},filter:function(t,e){var i=this.value,n=i.replace(e?new RegExp("["+e[0]+"]","gm"):D,"");n!==i&&this.setValue(n)}}),i.config=function(e,i){function n(t,e){"rules"===t?new o(e):"messages"===t?new a(e):t in I?I[t]=e:P[t]=e}M(e)?t.each(e,n):O(e)&&n(e,i)},i.setTheme=function(e,i){M(e)?t.extend(!0,j,e):O(e)&&M(i)&&(j[e]=t.extend(j[e],i))},i.load=function(e){if(e){var n,o,a,r=document,s={},l=r.scripts[0];e.replace(/([^?=&]+)=([^&#]*)/g,function(t,e,i){s[e]=i}),n=s.dir||i.dir,i.css||""===s.css||(o=r.createElement("link"),o.rel="stylesheet",o.href=i.css=n+"jquery.validator.css",l.parentNode.insertBefore(o,l)),!i.local&&~e.indexOf("local")&&""!==s.local&&(i.local=(s.local||r.documentElement.lang||"en").replace("_","-"),i.pending=1,o=r.createElement("script"),o.src=n+"local/"+i.local+".js",a="onload"in o?"onload":"onreadystatechange",o[a]=function(){o.readyState&&!/loaded|complete/.test(o.readyState)||(o=o[a]=null,delete i.pending,t(window).triggerHandler("validatorready"))},l.parentNode.insertBefore(o,l))}},function(){for(var t,e,n=document.scripts,o=n.length,a=/(.*validator(?:\.min)?.js)(\?.*(?:local|css|dir)(?:=[\w\-]*)?)?/;o--&&!e;)t=n[o],e=(t.hasAttribute?t.src:t.getAttribute("src",4)||"").match(a);e&&(i.dir=e[1].split("/").slice(0,-1).join("/")+"/",i.load(e[2]))}(),t[g]=i}),function(t){"object"==typeof module&&module.exports?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define("validator-lang",["jquery"],t):t(jQuery)}(function(t){t.validator.config({rules:{digits:[/^\d+$/,"请填写数字"],letters:[/^[a-z]+$/i,"请填写字母"],date:[/^\d{4}-\d{2}-\d{2}$/,"请填写有效的日期，格式:yyyy-mm-dd"],time:[/^([01]\d|2[0-3])(:[0-5]\d){1,2}$/,"请填写有效的时间，00:00到23:59之间"],email:[/^[\w\+\-]+(\.[\w\+\-]+)*@[a-z\d\-]+(\.[a-z\d\-]+)*\.([a-z]{2,4})$/i,"请填写有效的邮箱"],url:[/^(https?|s?ftp):\/\/\S+$/i,"请填写有效的网址"],qq:[/^[1-9]\d{4,}$/,"请填写有效的QQ号"],IDcard:[/^\d{6}(19|2\d)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)?$/,"请填写正确的身份证号码"],tel:[/^(?:(?:0\d{2,3}[\- ]?[1-9]\d{6,7})|(?:[48]00[\- ]?[1-9]\d{6}))$/,"请填写有效的电话号码"],mobile:[/^1[3-9]\d{9}$/,"请填写有效的手机号"],zipcode:[/^\d{6}$/,"请检查邮政编码格式"],chinese:[/^[\u0391-\uFFE5]+$/,"请填写中文字符"],username:[/^\w{3,12}$/,"请填写3-12位数字、字母、下划线"],password:[/^[\S]{6,16}$/,"请填写6-16位字符，不能包含空格"],accept:function(e,i){if(!i)return!0;var n=i[0],o=t(e).val();return"*"===n||new RegExp(".(?:"+n+")$","i").test(o)||this.renderMsg("只接受{1}后缀的文件",n.replace(/\|/g,","))}},messages:{0:"此处",fallback:"{0}格式不正确",loading:"正在验证...",error:"网络异常",timeout:"请求超时",required:"{0}不能为空",remote:"{0}已被使用",integer:{"*":"请填写整数","+":"请填写正整数","+0":"请填写正整数或0","-":"请填写负整数","-0":"请填写负整数或0"},match:{eq:"{0}与{1}不一致",neq:"{0}与{1}不能相同",lt:"{0}必须小于{1}",gt:"{0}必须大于{1}",lte:"{0}不能大于{1}",gte:"{0}不能小于{1}"},range:{rg:"请填写{1}到{2}的数",gte:"请填写不小于{1}的数",lte:"请填写最大{1}的数",gtlt:"请填写{1}到{2}之间的数",gt:"请填写大于{1}的数",lt:"请填写小于{1}的数"},checked:{eq:"请选择{1}项",rg:"请选择{1}到{2}项",gte:"请至少选择{1}项",lte:"请最多选择{1}项"},length:{eq:"请填写{1}个字符",rg:"请填写{1}到{2}个字符",gte:"请至少填写{1}个字符",lte:"请最多填写{1}个字符",eq_2:"",rg_2:"",gte_2:"",lte_2:""}}});var e='<span class="n-arrow"><b>◆</b><i>◆</i></span>';t.validator.setTheme({simple_right:{formClass:"n-simple",msgClass:"n-right"},simple_bottom:{formClass:"n-simple",msgClass:"n-bottom"},yellow_top:{formClass:"n-yellow",msgClass:"n-top",msgArrow:e},yellow_right:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e},yellow_right_effect:{formClass:"n-yellow",msgClass:"n-right",msgArrow:e,msgShow:function(t,e){var i=t.children();i.is(":animated")||("error"===e?i.css({left:"20px",opacity:0}).delay(100).show().stop().animate({left:"-4px",opacity:1},150).animate({left:"3px"},80).animate({left:0},80):i.css({left:0,opacity:1}).fadeIn(200))},msgHide:function(t,e){t.children().stop().delay(100).show().animate({left:"20px",opacity:0},300,function(){t.hide()})}}})}),define("form",["jquery","bootstrap","upload","validator","validator-lang"],function(t,e,i,n,e){var o={config:{fieldlisttpl:'<dd class="form-inline"><input type="text" name="<%=name%>[<%=index%>][key]" class="form-control" value="<%=key%>" placeholder="<%=options.keyPlaceholder||\'\'%>" size="10" /> <input type="text" name="<%=name%>[<%=index%>][value]" class="form-control" value="<%=value%>" placeholder="<%=options.valuePlaceholder||\'\'%>" /> <span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></dd>'},events:{validator:function(e,i,n,a){e.is("form")&&(e.validator(t.extend({rules:{username:[/^\w{3,30}$/,__("Username must be 3 to 30 characters")],password:[/^[\S]{6,30}$/,__("Password must be 6 to 30 characters")]},validClass:"has-success",invalidClass:"has-error",bindClassTo:".form-group",formClass:"n-default n-bootstrap",msgClass:"n-right",stopOnError:!0,display:function(e){return t(e).closest(".form-group").find(".control-label").text().replace(/\:/,"")},dataFilter:function(t){return 1===t.code?t.msg?{ok:t.msg}:"":t.msg},target:function(e){var i=t(e).data("target");if(i&&t(i).length>0)return t(i);var n=t(e).closest(".form-group"),o=n.find("span.msg-box");return o.length?o:[]},valid:function(r){var s=this,l=t(".layer-footer [type=submit]",e);return s.holdSubmit(!0),l.addClass("disabled"),o.api.submit(t(r),function(e,n){if(s.holdSubmit(!1),l.removeClass("disabled"),!1===t(this).triggerHandler("success.form",[e,n]))return!1;if("function"==typeof i&&!1===i.call(t(this),e,n))return!1;var o=n.hasOwnProperty("msg")&&""!==n.msg?n.msg:__("Operation completed");if(parent.Toastr.success(o),parent.$(".btn-refresh").trigger("click"),window.name){var a=parent.Layer.getFrameIndex(window.name);parent.Layer.close(a)}return!1},function(e,i){return s.holdSubmit(!1),!1!==t(this).triggerHandler("error.form",[e,i])&&(l.removeClass("disabled"),("function"!=typeof n||!1!==n.call(t(this),e,i))&&void 0)},a)||(s.holdSubmit(!1),l.removeClass("disabled")),!1}},e.data("validator-options")||{})),t(".layer-footer [type=submit],.fixed-footer [type=submit],.normal-footer [type=submit]",e).removeClass("disabled"),e.on("click",".layer-close",function(){if(window.name){var t=parent.Layer.getFrameIndex(window.name);parent.Layer.close(t)}return!1}))},selectpicker:function(e){t(".selectpicker",e).length>0&&require(["bootstrap-select","bootstrap-select-lang"],function(){t.fn.selectpicker.Constructor.BootstrapVersion="3",t(".selectpicker",e).selectpicker(),t(e).on("reset",function(){setTimeout(function(){t(".selectpicker").selectpicker("refresh").trigger("change")},1)})})},selectpage:function(e){t(".selectpage",e).length>0&&(require(["selectpage"],function(){t(".selectpage",e).selectPage({eAjaxSuccess:function(t){return t.list=void 0!==t.rows?t.rows:void 0!==t.list?t.list:[],t.totalRow=void 0!==t.total?t.total:void 0!==t.totalRow?t.totalRow:t.list.length,t}})}),t(document).on("change",".sp_hidden",function(){t(this).trigger("validate")}),t(document).on("change",".sp_input",function(){t(this).closest(".sp_container").find(".sp_hidden").trigger("change")}),t(e).on("reset",function(){setTimeout(function(){t(".selectpage",e).each(function(){t(this).data("selectPageObject").elem.hidden.val(t(this).val()),t(this).selectPageRefresh()})},1)}))},cxselect:function(e){t("[data-toggle='cxselect']",e).length>0&&require(["cxselect"],function(){t.cxSelect.defaults.jsonName="name",t.cxSelect.defaults.jsonValue="value",t.cxSelect.defaults.jsonSpace="data",t("[data-toggle='cxselect']",e).cxSelect()})},citypicker:function(e){t("[data-toggle='city-picker']",e).length>0&&require(["citypicker"],function(){t(e).on("reset",function(){setTimeout(function(){t("[data-toggle='city-picker']").citypicker("refresh")},1)})})},datetimepicker:function(e){t(".datetimepicker",e).length>0&&require(["bootstrap-datetimepicker"],function(){var i={format:"YYYY-MM-DD HH:mm:ss",icons:{time:"fa fa-clock-o",date:"fa fa-calendar",up:"fa fa-chevron-up",down:"fa fa-chevron-down",previous:"fa fa-chevron-left",next:"fa fa-chevron-right",today:"fa fa-history",clear:"fa fa-trash",close:"fa fa-remove"},showTodayButton:!0,showClose:!0};t(".datetimepicker",e).parent().css("position","relative"),t(".datetimepicker",e).datetimepicker(i).on("dp.change",function(e){t(this,document).trigger("changed")})})},daterangepicker:function(e){t(".datetimerange",e).length>0&&require(["bootstrap-daterangepicker"],function(){var i={};i[__("Today")]=[Moment().startOf("day"),Moment().endOf("day")],i[__("Yesterday")]=[Moment().subtract(1,"days").startOf("day"),Moment().subtract(1,"days").endOf("day")],i[__("Last 7 Days")]=[Moment().subtract(6,"days").startOf("day"),Moment().endOf("day")],i[__("Last 30 Days")]=[Moment().subtract(29,"days").startOf("day"),Moment().endOf("day")],i[__("This Month")]=[Moment().startOf("month"),Moment().endOf("month")],i[__("Last Month")]=[Moment().subtract(1,"month").startOf("month"),Moment().subtract(1,"month").endOf("month")];var n={timePicker:!1,autoUpdateInput:!1,timePickerSeconds:!0,timePicker24Hour:!0,autoApply:!0,locale:{format:"YYYY-MM-DD HH:mm:ss",customRangeLabel:__("Custom Range"),applyLabel:__("Apply"),cancelLabel:__("Clear")},ranges:i},o=function(e,i){t(this.element).val(e.format(this.locale.format)+" - "+i.format(this.locale.format)),t(this.element).trigger("change").trigger("validate")};t(".datetimerange",e).each(function(){var e="function"==typeof t(this).data("callback")?t(this).data("callback"):o;t(this).on("apply.daterangepicker",function(t,i){e.call(i,i.startDate,i.endDate)}),t(this).on("cancel.daterangepicker",function(e,i){t(this).val("").trigger("change").trigger("validate")}),t(this).daterangepicker(t.extend(!0,{},n,t(this).data()||{},t(this).data("daterangepicker-options")||{}))})})},plupload:function(t){o.events.faupload(t)},faupload:function(e){t(".plupload,.faupload",e).length>0&&i.api.upload(t(".plupload,.faupload",e))},faselect:function(e){t(".faselect,.fachoose",e).length>0&&t(".faselect,.fachoose",e).off("click").on("click",function(){var e=this,i=!!t(this).data("multiple")&&t(this).data("multiple"),n=t(this).data("mimetype")?t(this).data("mimetype"):"",o=t(this).data("admin-id")?t(this).data("admin-id"):"",a=t(this).data("user-id")?t(this).data("user-id"):"";n=n.replace(/\/\*/gi,"/");var r=t(this).data("url")?t(this).data("url"):"undefined"!=typeof Backend?"general/attachment/select":"user/attachment";return parent.Fast.api.open(r+"?element_id="+t(this).attr("id")+"&multiple="+i+"&mimetype="+n+"&admin_id="+o+"&user_id="+a,__("Choose"),{callback:function(i){var n=t(e),o=t(n).data("maxcount"),a=t(n).data("input-id")?t(n).data("input-id"):"";if(o=void 0!==o?o:0,a&&i.multiple){var r=[],s=t("#"+a),l=t.trim(s.val());""!==l&&r.push(s.val());var c=""===l?0:l.split(/\,/).length,d=""!==i.url?i.url.split(/\,/):[];if(t.each(d,function(t,e){var i=Config.upload.fullmode?Fast.api.cdnurl(e):e;r.push(i)}),o>0){var u=o-c;if(d.length>u)return Toastr.error(__("You can choose up to %d file%s",u)),!1}var p=r.join(",");s.val(p).trigger("change").trigger("validate")}else if(a){var h=Config.upload.fullmode?Fast.api.cdnurl(i.url):i.url;t("#"+a).val(h).trigger("change").trigger("validate")}n.trigger("fa.event.selectedfile",i)}}),!1})},fieldlist:function(e){t(".fieldlist",e).length>0&&require(["dragsort","template"],function(i,n){var a=function(i){var n={},o=i.data("name"),a=t("textarea[name='"+o+"']",e),r=i.data("template");t.each(t("input,select,textarea",i).serializeArray(),function(t,e){var i=/\[(\w+)\]\[(\w+)\]$/g,o=i.exec(e.name);if(!o)return!0;o[1]="x"+parseInt(o[1]),void 0===n[o[1]]&&(n[o[1]]={}),n[o[1]][o[2]]=e.value});var s=i.data("usearray")||!1,l=i.data("keepempty")||!1,c=r||s?[]:{},d=Object.keys(Object.values(n)[0]||{}),u=!s&&d.indexOf("value")>-1&&(1===d.length||2===d.length&&d.indexOf("key")>-1);u&&2===d.length&&(c={}),t.each(n,function(t,e){e&&(u?2===d.length?(""!=e.key||l)&&(c["__PLACEHOLDKEY__"+e.key]=e.value):c.push(e.value):c.push(e))}),a.val(JSON.stringify(c).replace(/__PLACEHOLDKEY__/g,""))},r=function(e,i,a){
var r=e.data("tag")||(e.is("table")?"tr":"dd"),s=e.data("index"),l=e.data("name"),c=e.data("template"),d=e.data();s=s?parseInt(s):0,e.data("index",s+1),i=i||{},i=void 0===i.key||void 0===i.value?{key:"",value:i}:i;var u=e.data("fieldlist-options")||{},p={index:s,name:l,data:d,options:u,key:i.key,value:i.value,row:i.value},h=c?n(c,p):n.render(o.config.fieldlisttpl,p),f=t(h);return!1!==u.deleteBtn&&!1!==u.removeBtn||!a||f.find(".btn-remove").remove(),!1===u.dragsortBtn&&a&&f.find(".btn-dragsort").remove(),!0!==u.readonlyKey&&!0!==u.disableKey||!a||f.find("input[name$='[key]']").prop("readonly",!0),f.attr("fieldlist-item",!0),f.insertAfter(t(r+"[fieldlist-item]",e).length>0?t(r+"[fieldlist-item]:last",e):t(r+":first",e)),t(".btn-append,.append",e).length>0?t(".btn-append,.append",e).trigger("fa.event.appendfieldlist",f):e.trigger("fa.event.appendfieldlist",f),f},s=t(".fieldlist",e);e.on("reset",function(){setTimeout(function(){s.trigger("fa.event.refreshfieldlist")})}),t(document).on("change keyup changed",".fieldlist input,.fieldlist textarea,.fieldlist select",function(){var e=t(this).closest(".fieldlist");a(e)}),s.on("click",".btn-append,.append",function(e,i){var n=t(this).closest(".fieldlist");r(n,i),a(n)}),s.on("click",".btn-remove",function(){var e=t(this).closest(".fieldlist"),i=e.data("tag")||(e.is("table")?"tr":"dd");t(this).closest(i).remove(),a(e)}),s.on("fa.event.appendtofieldlist",function(e,i){var n=t(this);r(n,i),a(n)}),s.on("fa.event.refreshfieldlist",function(){var i=t(this),n=t("textarea[name='"+i.data("name")+"']",e);t("[fieldlist-item]",i).remove();var o={};try{var a=n.val().replace(/"(\d+)"\:/g,'"__PLACEHOLDERKEY__$1":');o=JSON.parse(a)}catch(t){}t.each(o,function(t,e){r(i,{key:t.toString().replace("__PLACEHOLDERKEY__",""),value:e},!0)})}),s.each(function(){var i=t(this),n=i.data("tag")||(i.is("table")?"tr":"dd");i.dragsort({itemSelector:n,dragSelector:".btn-dragsort",dragEnd:function(){a(i)},placeHolderTemplate:t("<"+n+"/>")}),"object"==typeof i.data("options")&&!1===i.data("options").appendBtn&&t(".btn-append,.append",i).hide(),t("textarea[name='"+i.data("name")+"']",e).on("fa.event.refreshfieldlist",function(){t(this).closest(".fieldlist").trigger("fa.event.refreshfieldlist")})}),s.trigger("fa.event.refreshfieldlist")})},switcher:function(e){e.on("click","[data-toggle='switcher']",function(){if(t(this).hasClass("disabled"))return!1;var e=t.proxy(function(){var e=t(this).prev("input");if(e=t(this).data("input-id")?t("#"+t(this).data("input-id")):e,e.length>0){var i=t(this).data("yes"),n=t(this).data("no");e.val()==i?(e.val(n),t("i",this).addClass("fa-flip-horizontal text-gray")):(e.val(i),t("i",this).removeClass("fa-flip-horizontal text-gray")),e.trigger("change")}},this);return void 0!==t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){e(),Layer.close(t)}):e(),!1})},bindevent:function(t){},slider:function(e){t("[data-role='slider'],input.slider",e).length>0&&require(["bootstrap-slider"],function(){t("[data-role='slider'],input.slider").removeClass("hidden").css("width",function(e,i){return t(this).parents(".form-control").width()}).slider().on("slide",function(e){var i=t(this).data();void 0!==i.unit&&t(this).parents(".form-control").siblings(".value").text(e.value+i.unit)})})},tagsinput:function(e){t("[data-role='tagsinput']",e).length>0&&require(["tagsinput","autocomplete"],function(){t("[data-role='tagsinput']").tagsinput(),e.on("reset",function(){setTimeout(function(){t("[data-role='tagsinput']").tagsinput("reset")},0)})})},autocomplete:function(e){t("[data-role='autocomplete']",e).length>0&&require(["autocomplete"],function(){t("[data-role='autocomplete']").autocomplete({onSelect:function(){t(this).trigger("change").trigger("validate")}})})},favisible:function(e){if(0!=t("[data-favisible]",e).length){var i=function(i){var n,o,a=i.split(/&&/),r=0,s=/^([a-z0-9\_]+)([>|<|=|\!]=?)(.*)$/i,l=/^('|")(.*)('|")$/,c=/^regex:(.*)$/,d={">":function(t,e){return t>e},">=":function(t,e){return t>=e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},"==":function(t,e){return t==e},"!=":function(t,e){return t!=e},in:function(t,e){return e.split(/\,/).indexOf(t)>-1},regex:function(t,e){var i=e.match(/^\/(.*?)\/([gim]*)$/);return(i?new RegExp(i[1],i[2]):new RegExp(e)).test(t)}},u=e.find(":disabled").removeAttr("disabled"),p=e.serializeArray(),h={};return u.attr("disabled","disabled"),t(p).each(function(t,e){n=e.name,o=e.value,n="[]"===n.substr(-2)?n.substr(0,n.length-2):n,h[n]=void 0!==h[n]?[h[n],o].join(","):o}),t.each(a,function(e,i){var n=s.exec(i);if(n){var o=n[1],a=n[2],u=n[3].toString();if("="===a){var p=l.exec(u);a=p?"==":"in",u=p?p[2]:u}var f=c.exec(u);f&&(a="regex",u=f[1]);var m="row["+o+"]";if(void 0===h[m])return!1;var g=h[m];t.isArray(g)&&(g=h[m].join(",")),[">",">=","<","<="].indexOf(a)>-1&&(g=parseFloat(g),u=parseFloat(u));var v=d[a](g,u);r+=v?1:0}}),r===a.length};e.on("keyup change click configchange","input,textarea,select",function(){t("[data-favisible][data-favisible!='']",e).each(function(){var e=t(this).data("favisible"),n=e?e.toString().split(/\|\|/):[],o=0;t.each(n,function(t,e){i(e)&&o++}),o>0?t(this).removeClass("hidden"):t(this).addClass("hidden")})}),setTimeout(function(){var t=e.data("validator");t&&(t.options.ignore+=(t.options.ignore?",":"")+".hidden[data-favisible] :hidden,.hidden[data-favisible]:hidden")},0),t("input,textarea,select",e).trigger("configchange")}}},api:{submit:function(e,i,n,o){if(0===e.length)return Toastr.error("表单未初始化完成,无法提交"),!1;if("function"==typeof o&&!1===o.call(e,i,n))return!1;var a=e.attr("method")?e.attr("method").toUpperCase():"GET";a=!a||"GET"!==a&&"POST"!==a?"GET":a,url=e.attr("action"),url=url||location.href;var r={},s=t("[name$='[]']",e);if(s.length>0){var l=e.serializeArray().map(function(e){return t(e).prop("name")});t.each(s,function(e,i){l.indexOf(t(this).prop("name"))<0&&(r[t(this).prop("name")]="")})}return Fast.api.ajax({type:a,url:url,data:e.serialize()+(Object.keys(r).length>0?"&"+t.param(r):""),dataType:"json",complete:function(e){var i=e.getResponseHeader("__token__");i&&t("input[name='__token__']").val(i)}},function(n,o){if(t(".form-group",e).removeClass("has-feedback has-success has-error"),n&&"object"==typeof n&&(void 0!==n.token&&t("input[name='__token__']").val(n.token),void 0!==n.callback&&"function"==typeof n.callback&&n.callback.call(e,n)),"function"==typeof i&&!1===i.call(e,n,o))return!1},function(i,o){if(i&&"object"==typeof i&&void 0!==i.token&&t("input[name='__token__']").val(i.token),"function"==typeof n&&!1===n.call(e,i,o))return!1}),!0},bindevent:function(e,i,n,a){e="object"==typeof e?e:t(e);var r=o.events;r.bindevent(e),r.validator(e,i,n,a),r.selectpicker(e),r.daterangepicker(e),r.selectpage(e),r.cxselect(e),r.citypicker(e),r.datetimepicker(e),r.faupload(e),r.faselect(e),r.fieldlist(e),r.slider(e),r.switcher(e),r.tagsinput(e),r.autocomplete(e),r.favisible(e)},custom:{}}};return o}),function(t){"use strict";var e=[],i=t.fn.bootstrapTable.utils.sprintf,n=function(e,n){var a=o(e,n),r=i('<div class="commonsearch-table %s">',n.options.searchFormVisible?"":"hidden");r+=a,r+="</div>",n.$container.prepend(t(r)),n.$commonsearch=t(".commonsearch-table",n.$container);var s=t("form.form-commonsearch",n.$commonsearch);require(["form"],function(t){t.api.bindevent(s),s.validator("destroy")}),s.on("submit",function(t){return t.preventDefault(),n.onCommonSearch(),!1}),s.on("click","button[type=reset]",function(t){s[0].reset(),setTimeout(function(){n.onCommonSearch()},1)})},o=function(n,o){if(o.options.searchFormTemplate)return Template(o.options.searchFormTemplate,{columns:n,table:o});var s=[];s.push(i('<form class="form-horizontal form-commonsearch" novalidate method="post" action="%s" >',o.options.actionForm)),s.push("<fieldset>"),o.options.titleForm.length>0&&s.push(i("<legend>%s</legend>",o.options.titleForm)),s.push('<div class="row">');for(var l in n){var c=n[l];if(!c.checkbox&&!c.radio&&c.field&&"operate"!==c.field&&c.searchable&&!1!==c.operate){var d=Fast.api.query(c.field),u=Fast.api.query(c.field+"-operate"),p=o.options.renderDefault&&(void 0===c.renderDefault||c.renderDefault);c.defaultValue=p&&d?d:void 0===c.defaultValue?"":c.defaultValue,c.operate=p&&u?u:void 0===c.operate?"=":c.operate,e.push(c),s.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),s.push(i('<label for="%s" class="control-label col-xs-4">%s</label>',c.field,c.title)),s.push('<div class="col-xs-8">'),c.operate=c.operate?c.operate.toUpperCase():"=",s.push(i('<input type="hidden" class="form-control operate" name="%s-operate" data-name="%s" value="%s" readonly>',c.field,c.field,c.operate));var h=void 0===c.addClass?void 0===c.addclass?"form-control":"form-control "+c.addclass:"form-control "+c.addClass,f=void 0===c.extend?"":c.extend,m=void 0===c.style?"":i('style="%s"',c.style);if(f=void 0!==c.data&&""==f?c.data:f,f=void 0!==c.autocomplete?f+' autocomplete="'+(!1===c.autocomplete||"off"===c.autocomplete?"off":"on")+'"':f,c.searchList)if("function"==typeof c.searchList)s.push(c.searchList.call(this,c));else{var g=[i('<option value="">%s</option>',o.options.formatCommonChoose())];"object"==typeof c.searchList&&"function"==typeof c.searchList.then?function(e,i){t.when(e.searchList).done(function(n){var o=[];n.data&&n.data.searchlist&&t.isArray(n.data.searchlist)?o=n.data.searchlist:n.constructor!==Array&&n.constructor!==Object||(o=n);var a=r(o,e,i);t("form.form-commonsearch select[name='"+e.field+"']",i.$container).html(a.join("")).trigger("change")})}(c,o):g=r(c.searchList,c,o),s.push(i('<select class="%s" name="%s" %s %s>%s</select>',h,c.field,m,f,g.join("")))}else{var v=void 0===c.placeholder?c.title:c.placeholder,y=void 0===c.type?"text":c.type,b=void 0===c.defaultValue?"":c.defaultValue;if(/BETWEEN$/.test(c.operate)){var x=b.toString().match(/\|/)?b.split("|"):["",""],w=v.toString().match(/\|/)?v.split("|"):[v,v];s.push('<div class="row row-between">'),s.push(i('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-min" data-index="%s" %s %s></div>',y,h,c.field,x[0],w[0],c.field,l,m,f)),s.push(i('<div class="col-xs-6"><input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s-max" data-index="%s" %s %s></div>',y,h,c.field,x[1],w[1],c.field,l,m,f)),s.push("</div>")}else s.push(i('<input type="%s" class="%s" name="%s" value="%s" placeholder="%s" id="%s" data-index="%s" %s %s>',y,h,c.field,b,v,c.field,l,m,f))}s.push("</div>"),s.push("</div>")}}return s.push('<div class="form-group col-xs-12 col-sm-6 col-md-4 col-lg-3">'),s.push(a(o).join("")),s.push("</div>"),s.push("</div>"),s.push("</fieldset>"),s.push("</form>"),s.join("")},a=function(t){var e=[],n=t.options.formatCommonSubmitButton(),o=t.options.formatCommonResetButton();return e.push('<div class="col-sm-8 col-xs-offset-4">'),e.push(i('<button type="submit" class="btn btn-success" formnovalidate>%s</button> ',n)),e.push(i('<button type="reset" class="btn btn-default" >%s</button> ',o)),e.push("</div>"),e},r=function(e,n,o){var a=e.constructor===Array,r=[];return r.push(i('<option value="">%s</option>',o.options.formatCommonChoose())),t.each(e,function(t,e){e.constructor===Object?(t=e.id,e=e.name):t=a?e:t,r.push(i("<option value='"+Fast.api.escape(t)+"' %s>"+Fast.api.escape(e)+"</option>",t==n.defaultValue?"selected":""))}),r},s=function(t){return!(!t.options.commonSearch||"server"!=t.options.sidePagination||!t.options.url)},l=function(i,n){var o={},a={},r="";return t("form.form-commonsearch .operate",i.$commonsearch).each(function(s){var l=t(this).data("name"),c=t(this).is("select")?t("option:selected",this).val():t(this).val().toUpperCase(),d=t("[name='"+l+"']",i.$commonsearch);if(0==d.length)return!0;var u=e[s],p=!i.options.searchFormTemplate&&u&&"function"==typeof u.process?u.process:null;if(d.length>1)if(/BETWEEN$/.test(c)){var h=t.trim(t("[name='"+l+"']:first",i.$commonsearch).val()),f=t.trim(t("[name='"+l+"']:last",i.$commonsearch).val());h.length||f.length?(p&&(h=p(h,"begin"),f=p(f,"end")),r=h+","+f):r="",t("[name='"+l+"']:first",i.$commonsearch).hasClass("datetimepicker")&&(c="RANGE")}else r=t("[name='"+l+"']:checked",i.$commonsearch).val(),r=p?p(r):r;else r=p?p(d.val()):d.val();if(n&&(""===r||null==r||t.isArray(r)&&0===r.length)&&!c.match(/null/i))return!0;o[l]=c,a[l]=r}),{op:o,filter:a}},c=function(e,i,n){return e.filter="Object"==typeof e.filter?e.filter:e.filter?JSON.parse(e.filter):{},e.op="Object"==typeof e.op?e.op:e.op?JSON.parse(e.op):{},e.filter=t.extend({},e.filter,i.filter),e.op=t.extend({},e.op,i.op),n&&t.each(e.filter,function(i,n){(""==n||null==n||t.isArray(n)&&0==n.length)&&!e.op[i].match(/null/i)&&(delete e.filter[i],delete e.op[i])}),e.filter=JSON.stringify(e.filter),e.op=JSON.stringify(e.op),e};t.extend(t.fn.bootstrapTable.defaults,{commonSearch:!1,titleForm:"Common search",actionForm:"",searchFormTemplate:"",searchFormVisible:!0,searchClass:"searchit",showSearch:!0,renderDefault:!0,onCommonSearch:function(t,e){return!1},onPostCommonSearch:function(t){return!1}}),t.extend(t.fn.bootstrapTable.defaults.icons,{commonSearchIcon:"glyphicon-search"}),t.extend(t.fn.bootstrapTable.Constructor.EVENTS,{"common-search.bs.table":"onCommonSearch","post-common-search.bs.table":"onPostCommonSearch"}),t.extend(t.fn.bootstrapTable.locales[t.fn.bootstrapTable.defaults.locale],{formatCommonSearch:function(){return"Common search"},formatCommonSubmitButton:function(){return"Search"},formatCommonResetButton:function(){return"Reset"},formatCommonCloseButton:function(){return"Close"},formatCommonChoose:function(){return"Choose"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var d=t.fn.bootstrapTable.Constructor,u=d.prototype.initHeader,p=d.prototype.initToolbar,h=d.prototype.load,f=d.prototype.initSearch;d.prototype.initHeader=function(){u.apply(this,Array.prototype.slice.apply(arguments)),this.$header.find("th[data-field]").each(function(e){var i=t(this).data();void 0!==i.width&&-1===i.width.toString().indexOf("%")&&(t(".th-inner",this).outerWidth(i.width),t(this).css("max-width",i.width))}),this.options.stateField=this.header.stateField},d.prototype.initToolbar=function(){if(p.apply(this,Array.prototype.slice.apply(arguments)),s(this)){var e=this,o=[];e.options.showSearch&&(o.push(i('<div class="columns-%s pull-%s" style="margin-top:10px;margin-bottom:10px;">',this.options.buttonsAlign,this.options.buttonsAlign)),o.push(i('<button class="btn btn-default%s" type="button" name="commonSearch" title="%s">',void 0===e.options.iconSize?"":" btn-"+e.options.iconSize,e.options.formatCommonSearch())),o.push(i('<i class="%s %s"></i>',e.options.iconsPrefix,e.options.icons.commonSearchIcon)),o.push("</button></div>")),e.$toolbar.find(".pull-right").length>0?t(o.join("")).insertBefore(e.$toolbar.find(".pull-right:first")):e.$toolbar.append(o.join("")),n(e.columns,e),e.$toolbar.find('button[name="commonSearch"]').off("click").on("click",function(){e.$commonsearch.toggleClass("hidden")}),e.$container.on("click","."+e.options.searchClass,function(){var i=t(this).data("value"),n=t(this).data("field"),o=e.$container.closest(".panel-intro").find("ul[data-field='"+n+"']");if(o.length>0)return void t('li a[data-value="'+i+'"][data-toggle="tab"]',o).trigger("click");var a=t("form [name='"+n+"']",e.$commonsearch);a.length>0&&(a.is("select")?t("option[value='"+i+"']",a).prop("selected",!0):a.length>1?t("form [name='"+n+"'][value='"+i+"']",e.$commonsearch).prop("checked",!0):a.val(i+""),a.trigger("change"),t("form",e.$commonsearch).trigger("submit"))});var a=e.options.queryParams;this.options.queryParams=function(t){return a(c(t,l(e,!0)))},this.trigger("post-common-search",e)}},d.prototype.onCommonSearch=function(){var t=l(this);this.trigger("common-search",this,t),this.options.pageNumber=1,this.refresh({})},d.prototype.load=function(t){h.apply(this,Array.prototype.slice.apply(arguments)),s(this)},d.prototype.initSearch=function(){if(f.apply(this,Array.prototype.slice.apply(arguments)),s(this)){var e=this,i=t.isEmptyObject(this.filterColumnsPartial)?null:this.filterColumnsPartial;this.data=i?t.grep(this.data,function(n,o){for(var a in i){var r=i[a].toLowerCase(),s=n[a];if(s=t.fn.bootstrapTable.utils.calculateObjectValue(e.header,e.header.formatters[t.inArray(a,e.header.fields)],[s,n,o],s),-1===t.inArray(a,e.header.fields)||"string"!=typeof s&&"number"!=typeof s||-1===(s+"").toLowerCase().indexOf(r))return!1}return!0}):this.data}}}(jQuery),define("bootstrap-table-commonsearch",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";t.extend(t.fn.bootstrapTable.defaults,{templateView:!1,templateFormatter:"itemtpl",templateParentClass:"row row-flex",templateTableClass:"table-template"});var e=t.fn.bootstrapTable.Constructor,i=e.prototype.initContainer,n=e.prototype.initBody,o=e.prototype.initRow;e.prototype.initContainer=function(){i.apply(this,Array.prototype.slice.apply(arguments));var t=this;t.options.templateView&&(t.options.cardView=!0)},e.prototype.initBody=function(){var e=this;t.extend(e.options,{showHeader:!e.options.templateView&&t.fn.bootstrapTable.defaults.showHeader,showFooter:!e.options.templateView&&t.fn.bootstrapTable.defaults.showFooter}),t(e.$el).toggleClass(e.options.templateTableClass,e.options.templateView),n.apply(this,Array.prototype.slice.apply(arguments)),e.options.templateView&&t("> *:not(.no-records-found)",e.$body).wrapAll(t("<div />").addClass(e.options.templateParentClass))},e.prototype.initRow=function(t,e,i,n){var a=this;if(!a.options.templateView)return o.apply(a,Array.prototype.slice.apply(arguments));var r="";if("function"==typeof a.options.templateFormatter)r=a.options.templateFormatter.call(a,t,e,i);else{r=require("template")(a.options.templateFormatter,{item:t,i:e,data:i})}return r}}(jQuery),define("bootstrap-table-template",["bootstrap-table","template"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";var e=t.fn.bootstrapTable.utils.sprintf;t.extend(t.fn.bootstrapTable.defaults,{showJumpto:!1,exportOptions:{}}),t.extend(t.fn.bootstrapTable.locales,{formatJumpto:function(){return"GO"}}),t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales);var i=t.fn.bootstrapTable.Constructor,n=i.prototype.initPagination;i.prototype.initPagination=function(){if(this.showToolbar=this.options.showExport,n.apply(this,Array.prototype.slice.apply(arguments)),this.options.showJumpto){var i=this,o=this.$pagination.find("ul.pagination"),a=o.find("li.jumpto");a.length||(a=t(['<li class="jumpto">','<input type="text" class="form-control">','<button class="btn'+e(" btn-%s",this.options.buttonsClass)+e(" btn-%s",this.options.iconSize)+'" title="'+this.options.formatJumpto()+'"  type="button">'+this.options.formatJumpto(),"</button>","</li>"].join("")).appendTo(o),a.find("button").click(function(){i.selectPage(parseInt(a.find("input").val()))}))}}}(jQuery),define("bootstrap-table-jumpto",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),function(t){"use strict";function e(t){var e=0,a=0,r=0,s=0;return"detail"in t&&(a=t.detail),"wheelDelta"in t&&(a=-t.wheelDelta/120),"wheelDeltaY"in t&&(a=-t.wheelDeltaY/120),"wheelDeltaX"in t&&(e=-t.wheelDeltaX/120),"axis"in t&&t.axis===t.HORIZONTAL_AXIS&&(e=a,a=0),r=e*i,s=a*i,"deltaY"in t&&(s=t.deltaY),"deltaX"in t&&(r=t.deltaX),(r||s)&&t.deltaMode&&(1===t.deltaMode?(r*=n,s*=n):(r*=o,s*=o)),r&&!e&&(e=r<1?-1:1),s&&!a&&(a=s<1?-1:1),{spinX:e,spinY:a,pixelX:r,pixelY:s}}var i=10,n=40,o=800,a=null,r=function(){if(null===a){var e=t("<p/>").addClass("fixed-table-scroll-inner"),i=t("<div/>").addClass("fixed-table-scroll-outer"),n=void 0,o=void 0;i.append(e),t("body").append(i),n=e[0].offsetWidth,i.css("overflow","scroll"),o=e[0].offsetWidth,n===o&&(o=i[0].clientWidth),i.remove(),a=n-o}return a},s=function(t){return t[0].scrollHeight>t[0].clientHeight?15:0};t.extend(t.fn.bootstrapTable.defaults,{fixedColumns:!1,fixedNumber:0,fixedRightNumber:0});var l=t.fn.bootstrapTable.Constructor,c=l.prototype.initBody,d=l.prototype.initContainer,u=l.prototype.trigger,p=l.prototype.hideLoading,h=l.prototype.updateSelected;l.prototype.fixedColumnsSupported=function(){var t=this;return t.options.fixedColumns&&!t.options.detailView&&!t.options.cardView},l.prototype.initFixedContainer=function(){this.options.fixedNumber&&(0==this.$tableContainer.find(".fixed-columns").length&&this.$tableContainer.append('<div class="fixed-columns"></div>'),this.$fixedColumns=this.$tableContainer.find(".fixed-columns")),this.options.fixedRightNumber&&(0==this.$tableContainer.find(".fixed-columns-right").length&&this.$tableContainer.append('<div class="fixed-columns-right"></div>'),this.$fixedColumnsRight=this.$tableContainer.find(".fixed-columns-right"))},l.prototype.initContainer=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.initFixedContainer()},l.prototype.initBody=function(){c.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&(this.options.showHeader&&this.options.height||(this.initFixedColumnsBody(),this.initFixedColumnsEvents()))},l.prototype.trigger=function(){var t=this;u.apply(this,Array.prototype.slice.apply(arguments)),"pre-body"===arguments[0]&&this.options.cardView&&this.$tableBody.css("height","auto"),"toggle"===arguments[0]&&(arguments[1]?(this.$tableBody.css("height","auto"),this.$fixedColumns&&this.$fixedColumns.hide(),this.$fixedColumnsRight&&this.$fixedColumnsRight.hide()):(this.$tableBody.css("height","100%"),this.$fixedColumns&&this.$fixedColumns.show(),this.$fixedColumnsRight&&this.$fixedColumnsRight.show(),this.$fixedHeaderRight&&this.$fixedHeaderRight.scrollLeft(this.$tableBody.find("table").width()),this.$fixedBodyRight&&this.$fixedBodyRight.scrollLeft(this.$tableBody.find("table").width()))),t.fixedColumnsSupported()&&("post-header"===arguments[0]?this.initFixedColumnsHeader():"scroll-body"===arguments[0]?(this.needFixedColumns&&this.options.fixedNumber&&this.$fixedBody&&this.$fixedBody.scrollTop(this.$tableBody.scrollTop()),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedBodyRight&&this.$fixedBodyRight.scrollTop(this.$tableBody.scrollTop())):"load-success"===arguments[0]&&this.hideLoading())},l.prototype.updateSelected=function(){var e=this;h.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&this.$tableBody.find("tr").each(function(i,n){var o=t(n),a=o.data("index"),r=o.attr("class"),s='[name="'+e.options.selectItemName+'"]',l=o.find(s);if(void 0!==a){var c=function(t,i){var n=i.find('tr[data-index="'+a+'"]');n.attr("class",r),l.length&&n.find(s).prop("checked",l.prop("checked")),e.$selectAll.length&&t.add(i).find('[name="btSelectAll"]').prop("checked",e.$selectAll.prop("checked"))};e.$fixedBody&&e.options.fixedNumber&&c(e.$fixedHeader,e.$fixedBody),e.$fixedBodyRight&&e.options.fixedRightNumber&&c(e.$fixedHeaderRight,e.$fixedBodyRight)}})},l.prototype.hideLoading=function(){p.apply(this,Array.prototype.slice.apply(arguments)),this.needFixedColumns&&this.options.fixedNumber&&this.$fixedColumns.find(".fixed-table-loading").hide(),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedColumnsRight.find(".fixed-table-loading").hide()},l.prototype.initFixedColumnsHeader=function(){var t=this;this.options.height?this.needFixedColumns=this.$tableHeader.outerWidth(!0)<this.$tableHeader.find("table").outerWidth(!0):this.needFixedColumns=this.$tableBody.outerWidth(!0)<this.$tableBody.find("table").outerWidth(!0);var e=function(e,i){return e.find(".fixed-table-header").remove(),e.append(t.$tableHeader.clone(!0)),e.find(".fixed-table-header").css("margin-right",""),e.css({width:t.getFixedColumnsWidth(i)}),e.find(".fixed-table-header")};this.needFixedColumns&&this.options.fixedNumber?(this.$fixedHeader=e(this.$fixedColumns),this.$fixedHeader.css("margin-right","")):this.$fixedColumns&&this.$fixedColumns.html("").css("width",""),this.needFixedColumns&&this.options.fixedRightNumber?(this.$fixedHeaderRight=e(this.$fixedColumnsRight,!0),this.$fixedHeaderRight.scrollLeft(this.$fixedHeaderRight.find("table").width())):this.$fixedColumnsRight&&this.$fixedColumnsRight.html("").css("width",""),this.initFixedColumnsBody(),this.initFixedColumnsEvents()},l.prototype.initFixedColumnsBody=function(){var e=this,i=function(i,n){i.find(".fixed-table-body").remove(),i.append(e.$tableBody.clone(!0));var o=i.find(".fixed-table-body"),a=e.$tableBody.get(0),s=function(){var s=a.scrollWidth>a.clientWidth?r():0,l=t(".fixed-table-pagination",e.$tableContainer).height();void 0!==e.options.height&&(l=0),i.css({height:"calc(100% - "+(l+s)+"px)"}),o.css({height:"calc(100% - "+n.height()+"px)",overflow:"hidden"})};return t(window).on("resize",s),s(),o};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody=i(this.$fixedColumns,this.$fixedHeader)),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight=i(this.$fixedColumnsRight,this.$fixedHeaderRight),this.$fixedBodyRight.scrollLeft(this.$fixedBodyRight.find("table").width()),this.$fixedBodyRight.css("overflow-y","hidden"))},l.prototype.getFixedColumnsWidth=function(t){var e=this.getVisibleFields(),i=0,n=this.options.fixedNumber;t&&(e=e.reverse(),n=this.options.fixedRightNumber,this.$fixedColumnsRight.css("right",s(this.$tableBody)));for(var o=0;o<n;o++)i+=this.$header.find('th[data-field="'+e[o]+'"]').outerWidth();return i+1},l.prototype.initFixedColumnsEvents=function(){var i=this,n=function(e,n){var o='tr[data-index="'+t(e.currentTarget).data("index")+'"]',a=i.$tableBody.find(o);i.$fixedBody&&(a=a.add(i.$fixedBody.find(o))),i.$fixedBodyRight&&(a=a.add(i.$fixedBodyRight.find(o))),a.css("background-color",n?t(e.currentTarget).css("background-color"):"")};this.$tableBody.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)});var o="undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().indexOf("firefox")>-1,a=o?"DOMMouseScroll":"mousewheel",r=function(t,n){var o=e(t),a=Math.ceil(o.pixelY),r=i.$tableBody.scrollTop()+a;(a<0&&r>0||a>0&&r<n.scrollHeight-n.clientHeight)&&t.preventDefault(),i.$tableBody.scrollTop(r),i.$fixedBody&&i.$fixedBody.scrollTop(r),i.$fixedBodyRight&&i.$fixedBodyRight.scrollTop(r)};this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)}),this.$fixedBody[0].addEventListener(a,function(t){r(t,i.$fixedBody[0])}),this.$fixedBody.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var n=t(e.target).data("index");t(i.$selectItem[n]).trigger("click")}),this.$fixedBody.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var n=t(this).closest("tr[data-index]").data("index");t(i.$selectItem[n]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),t("div.fixed-table-body").off("scroll"),this.$tableBody.off("scroll").on("scroll",function(t){i.$tableHeader.scrollLeft(0),i.$tableBody.scrollLeft()>0&&(i.$tableHeader.scrollLeft(i.$tableBody.scrollLeft()),i.options.showFooter&&!i.options.cardView&&i.$tableFooter.scrollLeft(i.$tableBody.scrollLeft()));var e=i.$tableBody.scrollTop();i.$fixedBody&&i.$fixedBody.scrollTop(e),i.$fixedBodyRight&&i.$fixedBodyRight.scrollTop(e)}),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight.find("tr").hover(function(t){n(t,!0)},function(t){n(t,!1)}),this.$fixedBodyRight[0].addEventListener(a,function(t){r(t,i.$fixedBodyRight[0])}),this.$fixedBodyRight.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(e){e.stopImmediatePropagation();var n=t(e.target).data("index");t(i.$selectItem[n]).trigger("click")}),this.$fixedBodyRight.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(e){var n=t(this).closest("tr[data-index]").data("index");t(i.$selectItem[n]).closest("tr[data-index]").find(">td:eq("+t(this).index()+")").trigger("click")})),this.options.filterControl&&t(this.$fixedColumns).off("keyup change").on("keyup change",function(e){var n=t(e.target),o=n.val(),a=n.parents("th").data("field"),r=i.$header.find('th[data-field="'+a+'"]');if(n.is("input"))r.find("input").val(o);else if(n.is("select")){var s=r.find("select");s.find("option[selected]").removeAttr("selected"),s.find('option[value="'+o+'"]').attr("selected",!0)}i.triggerSearch()})}}(jQuery),define("bootstrap-table-fixed-columns",["bootstrap-table"],function(t){return function(){return t.$.fn.bootstrapTable.defaults}}(this)),define("table",["jquery","bootstrap","moment","moment/locale/zh-cn","bootstrap-table","bootstrap-table-lang","bootstrap-table-export","bootstrap-table-commonsearch","bootstrap-table-template","bootstrap-table-jumpto","bootstrap-table-fixed-columns"],function(t,e,i){var n={list:{},defaults:{url:"",sidePagination:"server",method:"get",toolbar:".toolbar",search:!0,cache:!1,commonSearch:!0,searchFormVisible:!1,titleForm:"",idTable:"commonTable",showExport:!0,exportDataType:"auto",exportTypes:["json","xml","csv","txt","doc","excel"],exportOptions:{fileName:"export_"+i().format("YYYY-MM-DD"),preventInjection:!1,mso:{onMsoNumberFormat:function(e,i,n){return isNaN(t(e).text())?"":"\\@"}},ignoreColumn:[0,"operate"]},pageSize:Config.pagesize||localStorage.getItem("pagesize")||10,pageList:[10,15,20,25,50,"All"],pagination:!0,clickToSelect:!0,dblClickToEdit:!0,singleSelect:!1,showRefresh:!1,showJumpto:!0,locale:"zh-cn"===Config.language?"zh-CN":"en-US",showToggle:!0,showColumns:!0,pk:"id",sortName:"id",sortOrder:"desc",paginationFirstText:__("First"),paginationPreText:__("Previous"),paginationNextText:__("Next"),paginationLastText:__("Last"),cardView:!1,iosCardView:!0,checkOnInit:!0,escape:!0,fixDropdownPosition:!0,dragCheckboxMultiselect:!0,selectedIds:[],selectedData:[],extend:{index_url:"",add_url:"",edit_url:"",del_url:"",import_url:"",multi_url:"",dragsort_url:"ajax/weigh"}},columnDefaults:{align:"center",valign:"middle"},config:{checkboxtd:"tbody>tr>td.bs-checkbox",toolbar:".toolbar",refreshbtn:".btn-refresh",addbtn:".btn-add",editbtn:".btn-edit",delbtn:".btn-del",importbtn:".btn-import",multibtn:".btn-multi",disabledbtn:".btn-disabled",editonebtn:".btn-editone",restoreonebtn:".btn-restoreone",destroyonebtn:".btn-destroyone",restoreallbtn:".btn-restoreall",destroyallbtn:".btn-destroyall",dragsortfield:"weigh"},button:{edit:{name:"edit",icon:"fa fa-pencil",title:__("Edit"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-success btn-editone"},del:{name:"del",icon:"fa fa-trash",title:__("Del"),extend:'data-toggle="tooltip" data-container="body"',classname:"btn btn-xs btn-danger btn-delone"},dragsort:{name:"dragsort",icon:"fa fa-arrows",title:__("Drag to sort"),extend:'data-toggle="tooltip"',classname:"btn btn-xs btn-primary btn-dragsort"}},api:{init:function(e,i,o){e=e||{},i=i||{},o=o||{},t.fn.bootstrapTable.Constructor.prototype.getSelectItem=function(){return this.$selectItem};var a=t.fn.bootstrapTable.Constructor.prototype.onPageListChange;t.fn.bootstrapTable.Constructor.prototype.onPageListChange=function(){return a.apply(this,Array.prototype.slice.apply(arguments)),localStorage.setItem("pagesize",this.options.pageSize),!1},t.extend(!0,t.fn.bootstrapTable.defaults,n.defaults,e),t.extend(t.fn.bootstrapTable.columnDefaults,n.columnDefaults,i),t.extend(t.fn.bootstrapTable.locales[n.defaults.locale],{formatCommonSearch:function(){return __("Common search")},formatCommonSubmitButton:function(){return __("Search")},formatCommonResetButton:function(){return __("Reset")},formatCommonCloseButton:function(){return __("Close")},formatCommonChoose:function(){return __("Choose")},formatJumpto:function(){return __("Go")}},o),t.fn.bootstrapTable.defaults.iosCardView&&navigator.userAgent.match(/(iPod|iPhone|iPad)/)&&(n.defaults.cardView=!0,
t.fn.bootstrapTable.defaults.cardView=!0),void 0!==e.exportTypes&&(t.fn.bootstrapTable.defaults.exportTypes=e.exportTypes)},bindevent:function(e){var i=e.closest(".bootstrap-table"),o=e.bootstrapTable("getOptions"),a=t(o.toolbar,i),r=t(".btn-selected-tips",i);0===r.length&&(r=t('<a href="javascript:" class="btn btn-warning-light btn-selected-tips hide" data-animation="false" data-toggle="tooltip" data-title="'+__("Click to uncheck all")+'"><i class="fa fa-info-circle"></i> '+__("Multiple selection mode: %s checked","<b>0</b>")+"</a>").appendTo(a)),r.off("click").on("click",function(t){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),e.on("uncheckbox",function(t,e,i){o.selectedIds=[],o.selectedData=[],r.tooltip("hide"),r.addClass("hide")}),e.on("load-error.bs.table",function(t,e,i){0!==i.status&&Toastr.error(__("Unknown data format"))}),e.on("load-success.bs.table",function(t,e){void 0===e.rows&&void 0!==e.code&&Toastr.error(e.msg)}),e.on("refresh.bs.table",function(e,i,o){t(n.config.refreshbtn,a).find(".fa").addClass("fa-spin"),t(".layui-layer-autocontent").remove()}),e.on("search.bs.table common-search.bs.table",function(t,i,n){e.trigger("uncheckbox")}),o.dblClickToEdit&&e.on("dbl-click-row.bs.table",function(e,i,o,a){t(n.config.editonebtn,o).trigger("click")}),e.on("pre-body.bs.table",function(e,i){o.maintainSelected&&t.each(i,function(e,i){i[o.stateField]=t.inArray(i[o.pk],o.selectedIds)>-1})}),e.on("post-body.bs.table",function(i,r){if(t(n.config.refreshbtn,a).find(".fa").removeClass("fa-spin"),t(n.config.checkboxtd+":first",e).find("input[type='checkbox'][data-index]").length>0){var s,l,c,d=!1,u=!1,p=function(i){if(d){var o=Math.min(i.pageX,s),a=Math.min(i.pageY,l),r=Math.abs(s-i.pageX),u=Math.abs(l-i.pageY);c.css({left:o+"px",top:a+"px",width:r+"px",height:u+"px"});var p={x:o,y:a,width:r,height:u};t(n.config.checkboxtd,e).each(function(){var e=t("input:checkbox",this),i=this.getBoundingClientRect();i.x+=document.documentElement.scrollLeft,i.y+=document.documentElement.scrollTop;var n=i.x,o=i.y,a=i.x+i.width,r=i.y+i.height,s=p.x,l=p.y,c=p.x+p.width,d=p.y+p.height;n<=c&&a>=s&&o<=d&&r>=l?t(this).hasClass("overlaped")||(t(this).addClass("overlaped"),e.trigger("click")):t(this).hasClass("overlaped")&&(t(this).removeClass("overlaped"),e.trigger("click"))})}},h=function(){return!1},f=function(){d&&(t(document).off("mousemove",p),t(document).off("selectstart",h),c.remove()),d=!1,u=!1,t(document.body).css({MozUserSelect:"",webkitUserSelect:""}).attr("unselectable","off")};t(n.config.checkboxtd,e).on("mousedown",function(e){if(2===e.button||t(e.target).is("input"))return!1;s=e.pageX,l=e.pageY,u=!0}).on("mousemove",function(i){u&&!d&&(d=!0,c=t("<div />"),c.css({position:"absolute",width:0,height:0,border:"1px dashed blue",background:"#0029ff",left:i.pageX+"px",top:i.pageY+"px",opacity:.1}),c.appendTo(document.body),t(document.body).css({MozUserSelect:"none",webkitUserSelect:"none"}).attr("unselectable","on"),t(document).on("mousemove",p).on("mouseup",f).on("selectstart",h),o.dragCheckboxMultiselect&&t(n.config.checkboxtd,e).removeClass("overlaped"))})}});var s=o.exportDataType;if(e.on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table post-body.bs.table",function(i){var l=[];t.each(e.bootstrapTable("getData"),function(t,e){l.push(void 0!==e[o.pk]?e[o.pk]:"")});var c=n.api.selectedids(e,!0),d=n.api.selecteddata(e,!0);o.maintainSelected?(o.selectedIds=o.selectedIds.filter(function(e,i,n){return-1===t.inArray(e,l)}).concat(c),o.selectedData=o.selectedData.filter(function(e,i,n){return-1===t.inArray(e[o.pk],l)}).concat(d),o.selectedIds.length>c.length?(t("b",r).text(o.selectedIds.length),r.removeClass("hide")):r.addClass("hide")):(o.selectedIds=c,o.selectedData=d),"auto"===s&&(o.exportDataType=c.length>0?"selected":"all",0===t(".export .exporttips").length&&t(".export .dropdown-menu").prepend("<li class='exporttips alert alert-warning-light mb-0 no-border p-2'></li>"),t(".export .exporttips").html("导出记录："+(c.length>0?"选中":"全部"))),t(n.config.disabledbtn,a).toggleClass("disabled",!o.selectedIds.length)}),e.on("common-search.bs.table",function(i,n,o){var a=t(".panel-heading [data-field]",e.closest(".panel-intro")),r=a.data("field"),s=t("li.active > a",a).data("value");o.filter&&void 0!==o.filter[r]&&o.filter[r]!=s&&(t("li",a).removeClass("active"),t("li > a[data-value='"+o.filter[r]+"']",a).parent().addClass("active"))}),t('.panel-heading [data-field] a[data-toggle="tab"]',e.closest(".panel-intro")).on("shown.bs.tab",function(i){var n=t(this).closest("[data-field]").data("field"),o=t(this).data("value"),a=t("[name='"+n+"']",e.closest(".bootstrap-table").find(".commonsearch-table"));return"SELECT"===a.prop("tagName")?t("option[value='"+o+"']",a).prop("selected",!0):a.val(o),e.trigger("uncheckbox"),e.bootstrapTable("getOptions").totalRows=0,e.bootstrapTable("refresh",{pageNumber:1}),!1}),t("form",e.closest(".bootstrap-table").find(".commonsearch-table")).on("reset",function(){setTimeout(function(){},0),t(".panel-heading [data-field] li",e.closest(".panel-intro")).removeClass("active"),t(".panel-heading [data-field] li:first",e.closest(".panel-intro")).addClass("active")}),a.on("click",n.config.refreshbtn,function(){e.bootstrapTable("refresh")}),a.on("click",n.config.addbtn,function(){var i=n.api.selectedids(e),a=o.extend.add_url;-1!==a.indexOf("{ids}")&&(a=n.api.replaceurl(a,{ids:i.length>0?i.join(","):0},e)),Fast.api.open(a,t(this).data("original-title")||t(this).attr("title")||__("Add"),t(this).data()||{})}),t(n.config.importbtn,a).length>0&&require(["upload"],function(i){i.api.upload(t(n.config.importbtn,a),function(t,i){Fast.api.ajax({url:o.extend.import_url,data:{file:t.url}},function(t,i){e.trigger("uncheckbox"),e.bootstrapTable("refresh")})})}),a.on("click",n.config.editbtn,function(){var i=this;if(!(n.api.selectedids(e).length>10)){var a=t(i).data("title")||t(i).attr("title")||__("Edit"),r=t(i).data()||{};delete r.title,t.each(n.api.selecteddata(e),function(i,s){var l=o.extend.edit_url;s=t.extend({},s||{},{ids:s[o.pk]}),l=n.api.replaceurl(l,s,e),Fast.api.open(l,"function"==typeof a?a.call(e,s):a,r)})}}),t(document).on("click",n.config.destroyallbtn,function(){var i=this;return Layer.confirm(__("Are you sure you want to truncate?"),function(){var n=t(i).data("url")?t(i).data("url"):t(i).attr("href");Fast.api.ajax(n,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()})}),!1}),t(document).on("click",n.config.restoreallbtn,function(){var i=this,n=t(i).data("url")?t(i).data("url"):t(i).attr("href");return Fast.api.ajax(n,function(){Layer.closeAll(),e.trigger("uncheckbox"),e.bootstrapTable("refresh")},function(){Layer.closeAll()}),!1}),t(document).on("click",n.config.restoreonebtn+","+n.config.destroyonebtn,function(){var i=this,a=t(i).data("url")?t(i).data("url"):t(i).attr("href"),r=n.api.getrowbyindex(e,t(i).data("row-index"));return Fast.api.ajax({url:a,data:{ids:r[o.pk]}},function(){e.trigger("uncheckbox"),e.bootstrapTable("refresh")}),!1}),a.on("click",n.config.multibtn,function(){var i=n.api.selectedids(e);n.api.multi(t(this).data("action"),i,e,this)}),a.on("click",n.config.delbtn,function(){var t=this,i=n.api.selectedids(e);Layer.confirm(__("Are you sure you want to delete the %s selected item?",i.length),{icon:3,title:__("Warning"),offset:0,shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(o){n.api.multi("del",i,e,t),Layer.close(o)})}),require(["dragsort"],function(){t("tbody",e).dragsort({itemSelector:"tr:visible",dragSelector:"a.btn-dragsort",dragEnd:function(i,o){var a=t("a.btn-dragsort",this),r=e.bootstrapTable("getData"),s=r[parseInt(t(this).data("index"))],l=e.bootstrapTable("getOptions"),c=t.map(t("tbody tr:visible",e),function(e){return r[parseInt(t(e).data("index"))][l.pk]}),d=s[l.pk],u=void 0!==s.pid?s.pid:"",p={url:e.bootstrapTable("getOptions").extend.dragsort_url,data:{ids:c.join(","),changeid:d,pid:u,field:n.config.dragsortfield,orderway:l.sortOrder,table:l.extend.table,pk:l.pk}};Fast.api.ajax(p,function(i,n){var o=t(a).data("success")||t.noop;if("function"==typeof o&&!1===o.call(a,i,n))return!1;e.bootstrapTable("refresh")},function(i,n){var o=t(a).data("error")||t.noop;if("function"==typeof o&&!1===o.call(a,i,n))return!1;e.bootstrapTable("refresh")})},placeHolderTemplate:""})}),e.on("click","input[data-id][name='checkbox']",function(i){var n=t(this).data("id");e.bootstrapTable(t(this).prop("checked")?"checkBy":"uncheckBy",{field:o.pk,values:[n]})}),e.on("click","[data-id].btn-change",function(i){i.preventDefault();var o=t.proxy(function(){n.api.multi(t(this).data("action")?t(this).data("action"):"",[t(this).data("id")],e,this)},this);void 0!==t(this).data("confirm")?Layer.confirm(t(this).data("confirm"),function(t){o(),Layer.close(t)}):o()}),e.on("click","[data-id].btn-edit",function(i){i.preventDefault();var a=t(this).data("id"),r=n.api.getrowbyid(e,a);r.ids=a;var s=n.api.replaceurl(o.extend.edit_url,r,e);Fast.api.open(s,t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})}),e.on("click","[data-id].btn-del",function(i){i.preventDefault();var o=t(this).data("id"),a=this;Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(t){n.api.multi("del",o,e,a),Layer.close(t)})}),e.on("mouseenter mouseleave",".autocontent",function(e){var i=t(".autocontent-item",this).get(0);i&&("mouseenter"===e.type?i.scrollWidth>i.offsetWidth&&0===t(".autocontent-caret",this).length&&t(this).append("<div class='autocontent-caret'><i class='fa fa-chevron-down'></div>"):t(".autocontent-caret",this).remove())}),e.on("click mouseenter",".autocontent-caret",function(e){var i=t(this).prev().hasClass("autocontent-hover");if(i||"mouseenter"!==e.type){var n=t(this).prev().text(),o=t(this).parent().get(0).getBoundingClientRect(),a=Layer.open({id:"autocontent",skin:"layui-layer-fast layui-layer-autocontent",title:!1,content:n,btn:!1,anim:!1,shade:0,isOutAnim:!1,area:"auto",maxWidth:450,maxHeight:350,offset:[o.y,o.x]});i&&t(document).one("mouseleave","#layui-layer"+a,function(){Layer.close(a)});var r=function(e){0===t(e.target).closest(".layui-layer").length&&(Layer.close(a),t(document).off("mousedown",r))};t(document).off("mousedown",r).on("mousedown",r)}}),o.fixDropdownPosition){var l=e.closest(".fixed-table-body");e.on("show.bs.dropdown fa.event.refreshdropdown",".btn-group",function(e){var i,n,o,a=t(".dropdown-menu",this),r=t(this),s=a.hasClass("pull-right")||a.hasClass("dropdown-menu-right");o="fixed",n=r.offset().top-t(window).scrollTop()+r.outerHeight(),n+a.outerHeight()>t(window).height()&&(n=r.offset().top-a.outerHeight()-5),i=s?r.offset().left+r.outerWidth()-a.outerWidth():r.offset().left,(i||n)&&a.css({position:o,left:i,top:n,right:"inherit"})});var c=function(){t(".btn-group.open",e).length>0&&"fixed"==t(".btn-group.open .dropdown-menu",e).css("position")&&t(".btn-group.open",e).trigger("fa.event.refreshdropdown")};t(window).on("scroll",function(){c()}),l.on("scroll",function(){c()})}var d=e.attr("id");return n.list[d]=e,e},multi:function(e,i,n,o){var a=n.bootstrapTable("getOptions"),r=o?t(o).data():{};i=t.isArray(i)?i.join(","):i,a={url:void 0!==r.url?r.url:"del"==e?a.extend.del_url:a.extend.multi_url,data:{action:e,ids:i,params:void 0!==r.params?"object"==typeof r.params?t.param(r.params):r.params:""}},Fast.api.ajax(a,function(e,i){n.trigger("uncheckbox");var a=t(o).data("success")||t.noop;if("function"==typeof a&&!1===a.call(o,e,i))return!1;n.bootstrapTable("refresh")},function(e,i){var n=t(o).data("error")||t.noop;if("function"==typeof n&&!1===n.call(o,e,i))return!1})},events:{operate:{"click .btn-editone":function(e,i,o,a){e.stopPropagation(),e.preventDefault();var r=t(this).closest("table"),s=r.bootstrapTable("getOptions"),l=o[s.pk];o=t.extend({},o||{},{ids:l});var c=s.extend.edit_url;Fast.api.open(n.api.replaceurl(c,o,r),t(this).data("original-title")||t(this).attr("title")||__("Edit"),t(this).data()||{})},"click .btn-delone":function(i,o,a,r){i.stopPropagation(),i.preventDefault();var s=this,l=t(s).offset().top-t(window).scrollTop(),c=t(s).offset().left-t(window).scrollLeft()-260;l+154>t(window).height()&&(l-=154),t(window).width()<480&&(l=c=e),Layer.confirm(__("Are you sure you want to delete this item?"),{icon:3,title:__("Warning"),offset:[l,c],shadeClose:!0,btn:[__("OK"),__("Cancel")]},function(e){var i=t(s).closest("table"),o=i.bootstrapTable("getOptions");n.api.multi("del",a[o.pk],i,s),Layer.close(e)})}},image:{"click .img-center":function(e,i,n,o){var a=[];i=null===i?"":i.toString();var r,s=""!=i?i.split(","):[];t.each(s,function(t,e){r=Fast.api.cdnurl(e),a.push({src:r,thumb:r.match(/^(\/|data:image\\)/)?r:r+Config.upload.thumbstyle})}),Layer.photos({photos:{start:t(this).parent().index(),data:a},anim:5})}}},formatter:{icon:function(t,e,i){return t=null===t?"":t.toString(),'<i class="'+(t=t.indexOf(" ")>-1?t:"fa fa-"+t)+'"></i> '+t},image:function(t,e,i){return n.api.formatter.images.call(this,t,e,i)},images:function(e,i,n){e=null==e||0===e.length?"":e.toString();var o,a=void 0!==this.classname?this.classname:"img-sm img-center",r=""!==e?-1===e.indexOf("data:image/")?e.split(","):[e]:[],s=[];return t.each(r,function(t,e){e=e||"/assets/img/blank.gif",o=Fast.api.cdnurl(e,!0),o=!Config.upload.thumbstyle||o.match(/^(\/|data:image\/)/)||o.indexOf(Config.upload.thumbstyle.substring(0,1))>-1?o:o+Config.upload.thumbstyle,s.push('<a href="javascript:"><img class="'+a+'" src="'+o+'" /></a>')}),s.join(" ")},file:function(t,e,i){return n.api.formatter.files.call(this,t,e,i)},files:function(e,i,n){e=null==e||0===e.length?"":e.toString();var o,a,r=void 0!==this.classname?this.classname:"img-sm img-center",s=""!==e?-1===e.indexOf("data:image/")?e.split(","):[e]:[],l=[];return t.each(s,function(t,e){e=Fast.api.cdnurl(e,!0),o=/[\.]?([a-zA-Z0-9]+)$/.exec(e),o=o?o[1]:"file",a=Fast.api.fixurl("ajax/icon?suffix="+o),l.push('<a href="'+e+'" target="_blank"><img src="'+a+'" class="'+r+'" width="30" height="30"></a>')}),l.join(" ")},content:function(t,i,n){var o=this.width!=e?this.width.toString().match(/^\d+$/)?this.width+"px":this.width:"250px";return"<div class='autocontent-item "+(this.hover!=e&&this.hover?"autocontent-hover":"")+"' style='white-space: nowrap; text-overflow:ellipsis; overflow: hidden; max-width:"+o+";'>"+t+"</div>"},status:function(e,i,o){var a={normal:"success",hidden:"gray",deleted:"danger",locked:"info"};return void 0!==this.custom&&(a=t.extend(a,this.custom)),this.custom=a,this.icon="fa fa-circle",n.api.formatter.normal.call(this,e,i,o)},normal:function(e,i,n){var o=["primary","success","danger","warning","info","gray","red","yellow","aqua","blue","navy","teal","olive","lime","fuchsia","purple","maroon"],a={};void 0!==this.custom&&(a=t.extend(a,this.custom)),e=i[this.field]||e,e=null==e||0===e.length?"":e.toString();var r="object"==typeof this.searchList?Object.keys(this.searchList):[],n=r.indexOf(e),s=e&&void 0!==a[e]?a[e]:null,l=n>-1?this.searchList[e]:null,c=void 0!==this.icon?this.icon:null;s||(s=n>-1&&void 0!==o[n]?o[n]:"primary"),l||(l=__(e.charAt(0).toUpperCase()+e.slice(1))),e=Fast.api.escape(e),l=Fast.api.escape(l);var d='<span class="text-'+s+'">'+(c?'<i class="'+c+'"></i> ':"")+l+"</span>";return 0!=this.operate&&(d='<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",l)+'" data-field="'+this.field+'" data-value="'+e+'">'+d+"</a>"),d},toggle:function(t,e,i){var n=this.table,o=n?n.bootstrapTable("getOptions"):{},a=o.pk||"id",r=void 0!==this.color?this.color:"success",s=void 0!==this.yes?this.yes:1,l=void 0!==this.no?this.no:0,c=void 0!==this.url?this.url:"",d="",u=!1;return void 0!==this.confirm&&(d="function"==typeof this.confirm?this.confirm.call(this,t,e,i):this.confirm),void 0!==this.disable&&(u="function"==typeof this.disable?this.disable.call(this,t,e,i):this.disable),"<a href='javascript:;' data-toggle='tooltip' title='"+__("Click to toggle")+"' class='btn-change "+(u?"btn disabled no-padding":"")+"' data-index='"+i+"' data-id='"+e[a]+"' "+(c?"data-url='"+c+"'":"")+(d?"data-confirm='"+d+"'":"")+" data-params='"+this.field+"="+(t==s?l:s)+"'><i class='fa fa-toggle-on text-success text-"+r+" "+(t==s?"":"fa-flip-horizontal text-gray")+" fa-2x'></i></a>"},url:function(t,e,i){return'<div class="input-group input-group-sm" style="width:250px;margin:0 auto;"><input type="text" class="form-control input-sm" value="'+(t=null==t||0===t.length?"":t.toString())+'"><span class="input-group-btn input-group-sm"><a href="'+t+'" target="_blank" class="btn btn-default btn-sm"><i class="fa fa-link"></i></a></span></div>'},search:function(t,i,n){var o=this.field;if(void 0!==this.customField){var a=this.customField.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},i);t=Fast.api.escape(a),o=this.customField}return'<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",t)+'" data-field="'+o+'" data-value="'+t+'">'+t+"</a>"},addtabs:function(t,e,i){var o=n.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("Search %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="addtabsit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},dialog:function(t,e,i){var o=n.api.replaceurl(this.url||"",e,this.table),a=this.atitle?this.atitle:__("View %s",t);return'<a href="'+Fast.api.fixurl(o)+'" class="dialogit" data-value="'+t+'" title="'+a+'">'+t+"</a>"},flag:function(i,n,o){var a=this;i=null==i||0===i.length?"":i.toString();var r={index:"success",hot:"warning",recommend:"danger",new:"info"};void 0!==this.custom&&(r=t.extend(r,this.custom));var s=this.field;if(void 0!==this.customField){var l=this.customField.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},n);i=Fast.api.escape(l),s=this.customField}if("object"==typeof a.searchList&&"function"==typeof a.searchList.then&&t.when(a.searchList).done(function(e){e.data&&e.data.searchlist&&t.isArray(e.data.searchlist)?a.searchList=e.data.searchlist:e.constructor!==Array&&e.constructor!==Object||(a.searchList=e)}),"object"==typeof a.searchList&&void 0===a.custom){var c=0,d=Object.values(r);t.each(a.searchList,function(t,e){void 0===r[t]&&(r[t]=d[c],c=void 0===d[c+1]?0:c+1)})}var u,p,h,f=[],m=t.isArray(i)?i:""!=i?i.split(","):[];return t.each(m,function(t,e){if(""===(e=null==e||0===e.length?"":e.toString()))return!0;u=e&&void 0!==r[e]?r[e]:"primary",p=void 0!==a.searchList&&void 0!==a.searchList[e]?a.searchList[e]:__(e.charAt(0).toUpperCase()+e.slice(1)),e=Fast.api.escape(e),p=Fast.api.escape(p),h='<span class="label label-'+u+'">'+p+"</span>",a.operate?f.push('<a href="javascript:;" class="searchit" data-toggle="tooltip" title="'+__("Click to search %s",p)+'" data-field="'+s+'" data-value="'+e+'">'+h+"</a>"):f.push(h)}),f.join(" ")},label:function(t,e,i){return n.api.formatter.flag.call(this,t,e,i)},datetime:function(t,e,n){var o=void 0===this.datetimeFormat?"YYYY-MM-DD HH:mm:ss":this.datetimeFormat;return isNaN(t)?t?i(t).format(o):__("None"):t?i(1e3*parseInt(t)).format(o):__("None")},operate:function(e,i,o){var a=this.table,r=a?a.bootstrapTable("getOptions"):{},s=t.extend([],this.buttons||[]),l=[];return s.forEach(function(t){l.push(t.name)}),""!==r.extend.dragsort_url&&-1===l.indexOf("dragsort")&&s.push(n.button.dragsort),""!==r.extend.edit_url&&-1===l.indexOf("edit")&&(n.button.edit.url=r.extend.edit_url,s.push(n.button.edit)),""!==r.extend.del_url&&-1===l.indexOf("del")&&s.push(n.button.del),n.api.buttonlink(this,s,e,i,o,"operate")},buttons:function(e,i,o){var a=t.extend([],this.buttons||[]);return n.api.buttonlink(this,a,e,i,o,"buttons")}},buttonlink:function(e,i,o,a,r,s){var l=e.table;e.clickToSelect=!1,s=void 0===s?"buttons":s;var c,d,u,p,h,f,m,g,v,y,b,x,w,_=l?l.bootstrapTable("getOptions"):{},k=[],C=e.fieldIndex,S={};if(t.each(i,function(t,e){if("operate"===s){if("dragsort"===e.name&&void 0===a[n.config.dragsortfield])return!0;if(["add","edit","del","multi","dragsort"].indexOf(e.name)>-1&&!_.extend[e.name+"_url"])return!0}var i=l.data(s+"-"+e.name);if(void 0===i||i){if(c="function"==typeof e.hidden?e.hidden.call(l,a,e):void 0!==e.hidden&&e.hidden)return!0;if(!(d="function"==typeof e.visible?e.visible.call(l,a,e):void 0===e.visible||e.visible))return!0;x=e.dropdown?e.dropdown:"",p=e.url?e.url:"",p="function"==typeof p?p.call(l,a,e):p?Fast.api.fixurl(n.api.replaceurl(p,a,l)):"javascript:;",h=e.classname?e.classname:x?"btn-"+name+"one":"btn-primary btn-"+name+"one",f=e.icon?e.icon:"",m="function"==typeof e.text?e.text.call(l,a,e):e.text?e.text:"",g="function"==typeof e.title?e.title.call(l,a,e):e.title?e.title:m,v=e.refresh?'data-refresh="'+e.refresh+'"':"",y="function"==typeof e.confirm?e.confirm.call(l,a,e):void 0!==e.confirm&&e.confirm,y=y?'data-confirm="'+y+'"':"",b="function"==typeof e.extend?e.extend.call(l,a,e):void 0!==e.extend?e.extend:"",u="function"==typeof e.disable?e.disable.call(l,a,e):void 0!==e.disable&&e.disable,u&&(h+=" disabled"),w='<a href="'+p+'" class="'+h+'" '+(y?y+" ":"")+(v?v+" ":"")+b+' title="'+g+'" data-table-id="'+(l?l.attr("id"):"")+'" data-field-index="'+C+'" data-row-index="'+r+'" data-button-index="'+t+'"><i class="'+f+'"></i>'+(m?" "+m:"")+"</a>",x?(void 0===S[x]&&(S[x]=[]),S[x].push(w)):k.push(w)}}),!t.isEmptyObject(S)){var T=[];t.each(S,function(t,e){T.push('<div class="btn-group"><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown">'+t+'</button><button type="button" class="btn btn-primary dropdown-toggle btn-xs" data-toggle="dropdown"><span class="caret"></span></button><ul class="dropdown-menu dropdown-menu-right"><li>'+e.join("</li><li>")+"</li></ul></div>")}),k.unshift(T.join(" "))}return k.join(" ")},replaceurl:function(t,i,n){var o=n?n.bootstrapTable("getOptions"):null,a=o?i[o.pk]:0;return i.ids=a||(void 0!==i.ids?i.ids:0),t=null==t||0===t.length?"":t.toString(),t=t.match(/(?=([?&]ids=)|(\/ids\/)|(\{ids}))/i)?t:t+(t.match(/(\?|&)+/)?"&ids=":"/ids/")+"{ids}",t=t.replace(/\{(.*?)\}/gi,function(t){t=t.substring(1,t.length-1);var n=t.split(".").reduce(function(t,i){return null===t||t===e?"":t[i]},i);return n=Fast.api.escape(n)})},selectedids:function(e,i){var n=e.bootstrapTable("getOptions");return!i&&n.maintainSelected?n.selectedIds:t.map(e.bootstrapTable("getSelections"),function(t){return t[n.pk]})},selecteddata:function(t,e){var i=t.bootstrapTable("getOptions");return!e&&i.maintainSelected?i.selectedData:t.bootstrapTable("getSelections")},toggleattr:function(e){t("input[type='checkbox']",e).trigger("click")},getrowdata:function(t,e){e=parseInt(e);var i=t.bootstrapTable("getData");return void 0!==i[e]?i[e]:null},getrowbyindex:function(t,e){return n.api.getrowdata(t,e)},getrowbyid:function(e,i){var o={},a=e.bootstrapTable("getOptions");return t.each(n.api.selecteddata(e),function(t,e){if(e[a.pk]==i)return o=e,!1}),o}}};return n}),function(t){t.fn.dragsort=function(e){if("destroy"==e)return void t(this.selector).trigger("dragsort-uninit");var i=t.extend({},t.fn.dragsort.defaults,e),n=[],o=null,a=null;return this.each(function(e,r){t(r).is("table")&&1==t(r).children().length&&t(r).children().is("tbody")&&(r=t(r).children().get(0));var s={draggedItem:null,placeHolderItem:null,pos:null,offset:null,offsetLimit:null,scroll:null,container:r,init:function(){i.tagName=""==i.tagName?0==t(this.container).children().length?"li":t(this.container).children().get(0).tagName.toLowerCase():i.tagName,""==i.itemSelector&&(i.itemSelector=i.tagName),""==i.dragSelector&&(i.dragSelector=i.tagName),""==i.placeHolderTemplate&&(i.placeHolderTemplate="<"+i.tagName+">&nbsp;</"+i.tagName+">"),t(this.container).attr("data-listidx",e).mousedown(this.grabItem).bind("dragsort-uninit",this.uninit),this.styleDragHandlers(!0)},uninit:function(){var e=n[t(this).attr("data-listidx")];t(e.container).unbind("mousedown",e.grabItem).unbind("dragsort-uninit"),e.styleDragHandlers(!1)},getItems:function(){return t(this.container).children(i.itemSelector)},styleDragHandlers:function(e){this.getItems().map(function(){return t(this).is(i.dragSelector)?this:t(this).find(i.dragSelector).get()}).css("cursor",e?"pointer":"")},grabItem:function(e){var o=n[t(this).attr("data-listidx")],a=t(e.target).closest("[data-listidx] > "+i.tagName).get(0),r=o.getItems().filter(function(){return this==a}).length>0;if(!(1!=e.which||t(e.target).is(i.dragSelectorExclude)||t(e.target).closest(i.dragSelectorExclude).length>0)&&r&&(t(e.target).is(i.dragSelector)||t(e.target).closest(i.dragSelector).length)){e.preventDefault();for(var s=e.target;!t(s).is(i.dragSelector);){if(s==this)return;s=s.parentNode}t(s).attr("data-cursor",t(s).css("cursor")),t(s).css("cursor","move");var l=this,c=function(){o.dragStart.call(l,e),t(o.container).unbind("mousemove",c)};t(o.container).mousemove(c).mouseup(function(){t(o.container).unbind("mousemove",c),t(s).css("cursor",t(s).attr("data-cursor"))})}},dragStart:function(e){null!=o&&null!=o.draggedItem&&o.dropItem(),o=n[t(this).attr("data-listidx")],o.draggedItem=t(e.target).closest("[data-listidx] > "+i.tagName),o.draggedItem.attr("data-origpos",t(this).attr("data-listidx")+"-"+t(o.container).children().index(o.draggedItem));var a=parseInt(o.draggedItem.css("marginTop")),r=parseInt(o.draggedItem.css("marginLeft"));if(o.offset=o.draggedItem.offset(),o.offset.top=e.pageY-o.offset.top+(isNaN(a)?0:a)-1,o.offset.left=e.pageX-o.offset.left+(isNaN(r)?0:r)-1,!i.dragBetween){var s=0==t(o.container).outerHeight()?Math.max(1,Math.round(.5+o.getItems().length*o.draggedItem.outerWidth()/t(o.container).outerWidth()))*o.draggedItem.outerHeight():t(o.container).outerHeight();o.offsetLimit=t(o.container).offset(),o.offsetLimit.right=o.offsetLimit.left+t(o.container).outerWidth()-o.draggedItem.outerWidth(),o.offsetLimit.bottom=o.offsetLimit.top+s-o.draggedItem.outerHeight()}var l=o.draggedItem.height(),c=o.draggedItem.width();if("tr"==i.tagName?(o.draggedItem.children().each(function(){t(this).width(t(this).width())}),o.placeHolderItem=o.draggedItem.clone().attr("data-placeholder",!0),o.draggedItem.after(o.placeHolderItem),o.placeHolderItem.children().each(function(){t(this).html("&nbsp;")})):(o.draggedItem.after(i.placeHolderTemplate),o.placeHolderItem=o.draggedItem.next().css({height:l,width:c}).attr("data-placeholder",!0)),"td"==i.tagName){var d=o.draggedItem.closest("table").get(0);t("<table id='"+d.id+"' style='border-width: 0px;' class='dragSortItem "+d.className+"'><tr></tr></table>").appendTo("body").children().append(o.draggedItem)}var u=o.draggedItem.attr("style");o.draggedItem.attr("data-origstyle",u||""),o.draggedItem.css({position:"absolute",opacity:.8,"z-index":999,height:l,width:c}),o.scroll={moveX:0,moveY:0,maxX:t(document).width()-t(window).width(),maxY:t(document).height()-t(window).height()},o.scroll.scrollY=window.setInterval(function(){if(i.scrollContainer!=window)return void t(i.scrollContainer).scrollTop(t(i.scrollContainer).scrollTop()+o.scroll.moveY);var e=t(i.scrollContainer).scrollTop();(o.scroll.moveY>0&&e<o.scroll.maxY||o.scroll.moveY<0&&e>0)&&(t(i.scrollContainer).scrollTop(e+o.scroll.moveY),o.draggedItem.css("top",o.draggedItem.offset().top+o.scroll.moveY+1))},10),o.scroll.scrollX=window.setInterval(function(){if(i.scrollContainer!=window)return void t(i.scrollContainer).scrollLeft(t(i.scrollContainer).scrollLeft()+o.scroll.moveX);var e=t(i.scrollContainer).scrollLeft();(o.scroll.moveX>0&&e<o.scroll.maxX||o.scroll.moveX<0&&e>0)&&(t(i.scrollContainer).scrollLeft(e+o.scroll.moveX),o.draggedItem.css("left",o.draggedItem.offset().left+o.scroll.moveX+1))},10),t(n).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),o.setPos(e.pageX,e.pageY),t(document).bind("mousemove",o.swapItems),t(document).bind("mouseup",o.dropItem),i.scrollContainer!=window&&t(window).bind("wheel",o.wheel)},setPos:function(e,n){var a=n-this.offset.top,r=e-this.offset.left;i.dragBetween||(a=Math.min(this.offsetLimit.bottom,Math.max(a,this.offsetLimit.top)),r=Math.min(this.offsetLimit.right,Math.max(r,this.offsetLimit.left)));var s=this.draggedItem.offsetParent().not("body").offset();if(null!=s&&(a-=s.top,r-=s.left),i.scrollContainer==window)n-=t(window).scrollTop(),e-=t(window).scrollLeft(),n=Math.max(0,n-t(window).height()+5)+Math.min(0,n-5),e=Math.max(0,e-t(window).width()+5)+Math.min(0,e-5);else{var l=t(i.scrollContainer),c=l.offset();n=Math.max(0,n-l.height()-c.top)+Math.min(0,n-c.top),e=Math.max(0,e-l.width()-c.left)+Math.min(0,e-c.left)}o.scroll.moveX=0==e?0:e*i.scrollSpeed/Math.abs(e),o.scroll.moveY=0==n?0:n*i.scrollSpeed/Math.abs(n),this.draggedItem.css({top:a,left:r})},wheel:function(e){if(o&&i.scrollContainer!=window){var n=t(i.scrollContainer),a=n.offset();if(e=e.originalEvent,e.clientX>a.left&&e.clientX<a.left+n.width()&&e.clientY>a.top&&e.clientY<a.top+n.height()){var r=(0==e.deltaMode?1:10)*e.deltaY;n.scrollTop(n.scrollTop()+r),e.preventDefault()}}},buildPositionTable:function(){var e=[];this.getItems().not([o.draggedItem[0],o.placeHolderItem[0]]).each(function(i){var n=t(this).offset();n.right=n.left+t(this).outerWidth(),n.bottom=n.top+t(this).outerHeight(),n.elm=this,e[i]=n}),this.pos=e},dropItem:function(){if(null!=o.draggedItem){var e=o.draggedItem.attr("data-origstyle");if(o.draggedItem.attr("style",e),""==e&&o.draggedItem.removeAttr("style"),o.draggedItem.removeAttr("data-origstyle"),o.styleDragHandlers(!0),o.placeHolderItem.before(o.draggedItem),o.placeHolderItem.remove(),t("[data-droptarget], .dragSortItem").remove(),window.clearInterval(o.scroll.scrollY),window.clearInterval(o.scroll.scrollX),o.draggedItem.attr("data-origpos")!=t(n).index(o)+"-"+t(o.container).children().index(o.draggedItem)&&0==i.dragEnd.apply(o.draggedItem)){var a=o.draggedItem.attr("data-origpos").split("-"),r=t(n[a[0]].container).children().not(o.draggedItem).eq(a[1]);r.length>0?r.before(o.draggedItem):0==a[1]?t(n[a[0]].container).prepend(o.draggedItem):t(n[a[0]].container).append(o.draggedItem)}return o.draggedItem.removeAttr("data-origpos"),o.draggedItem=null,t(document).unbind("mousemove",o.swapItems),t(document).unbind("mouseup",o.dropItem),i.scrollContainer!=window&&t(window).unbind("wheel",o.wheel),!1}},swapItems:function(e){if(null==o.draggedItem)return!1;o.setPos(e.pageX,e.pageY);for(var r=o.findPos(e.pageX,e.pageY),s=o,l=0;-1==r&&i.dragBetween&&l<n.length;l++)r=n[l].findPos(e.pageX,e.pageY),s=n[l];if(-1==r)return!1;var c=function(){return t(s.container).children().not(s.draggedItem)},d=c().not(i.itemSelector).each(function(t){this.idx=c().index(this)});return null==a||a.top>o.draggedItem.offset().top||a.left>o.draggedItem.offset().left?t(s.pos[r].elm).before(o.placeHolderItem):t(s.pos[r].elm).after(o.placeHolderItem),d.each(function(){var e=c().eq(this.idx).get(0);this!=e&&c().index(this)<this.idx?t(this).insertAfter(e):this!=e&&t(this).insertBefore(e)}),t(n).each(function(t,e){e.createDropTargets(),e.buildPositionTable()}),a=o.draggedItem.offset(),!1},findPos:function(t,e){for(var i=0;i<this.pos.length;i++)if(this.pos[i].left<t&&this.pos[i].right>t&&this.pos[i].top<e&&this.pos[i].bottom>e)return i;return-1},createDropTargets:function(){i.dragBetween&&t(n).each(function(){var e=t(this.container).find("[data-placeholder]"),n=t(this.container).find("[data-droptarget]");e.length>0&&n.length>0?n.remove():0==e.length&&0==n.length&&("td"==i.tagName?t(i.placeHolderTemplate).attr("data-droptarget",!0).appendTo(this.container):t(this.container).append(o.placeHolderItem.removeAttr("data-placeholder").clone().attr("data-droptarget",!0)),o.placeHolderItem.attr("data-placeholder",!0))})}};s.init(),n.push(s)}),this},t.fn.dragsort.defaults={tagName:"",itemSelector:"",dragSelector:"",dragSelectorExclude:"input, textarea",dragEnd:function(){},dragBetween:!1,placeHolderTemplate:"",scrollContainer:window,scrollSpeed:5}}(jQuery),define("dragsort",function(){}),function(t){"use strict";function e(e){return this.each(function(){
var i=t(this),n=i.data(d.dataKey),o=t.extend({},c,i.data(),n&&n.option,"object"==typeof e&&e);n||i.data(d.dataKey,n=new d(this,o))})}function i(e){return t(e).closest("div.sp_container").find("input.sp_input")}function n(){return this.each(function(){var t=i(this),e=t.data(d.dataKey);e&&(e.prop.init_set=!0,e.clearAll(e),e.prop.init_set=!1)})}function o(){return this.each(function(){var t=i(this),e=t.data(d.dataKey);e&&e.elem.hidden.val()&&e.setInitRecord(!0)})}function a(e){return this.each(function(){if(e&&t.isArray(e)){var n=i(this),o=n.data(d.dataKey);o&&(o.clearAll(o),o.option.data=e)}})}function r(e){var n=!1;return this.each(function(){var o=i(this),a=o.data(d.dataKey);a&&("undefined"!==t.type(e)?a.disabled(a,e):n=a.disabled(a))}),n}function s(){var e="";return this.each(function(){var n=i(this),o=n.data(d.dataKey);if(o)if(o.option.multiple){var a=[];o.elem.element_box.find("li.selected_tag").each(function(e,i){a.push(t(i).text())}),e+=a.toString()}else e+=o.elem.combo_input.val()}),e}function l(){var e=[];return this.each(function(){var n=i(this),o=n.data(d.dataKey);if(o)if(o.option.multiple)o.elem.element_box.find("li.selected_tag").each(function(i,n){e.push(t(n).data("dataObj"))});else{var a=o.elem.combo_input.data("dataObj");a&&e.push(a)}}),e}var c={data:void 0,lang:"cn",multiple:!1,pagination:!0,dropButton:!0,listSize:10,multipleControlbar:!0,maxSelectLimit:0,selectToCloseList:!1,initRecord:void 0,dbTable:"tbl",keyField:"id",showField:"name",searchField:void 0,escape:!0,andOr:"OR",separator:",",orderBy:void 0,pageSize:10,params:void 0,formatItem:void 0,autoFillResult:!1,autoSelectFirst:!1,noResultClean:!0,selectOnly:!1,inputDelay:.5,eSelect:void 0,eOpen:void 0,eAjaxSuccess:void 0,eTagRemove:void 0,eClear:void 0},d=function(e,i){t.each({data:"source",keyField:"primaryKey",showField:"field",pageSize:"perPage"},function(t,e){void 0!==i[e]&&(i[t]=i[e],delete i[e])}),this.setOption(i),this.setLanguage(),this.setCssClass(),this.setProp(),this.setElem(e),this.setButtonAttrDefault(),this.setInitRecord(),this.eDropdownButton(),this.eInput(),this.eWhole()};d.version="2.19",d.dataKey="selectPageObject",d.prototype.setOption=function(e){e.searchField=e.searchField||e.showField,e.andOr=e.andOr.toUpperCase(),"AND"!==e.andOr&&"OR"!==e.andOr&&(e.andOr="AND");for(var i=["searchField"],n=0;n<i.length;n++)e[i[n]]=this.strToArray(e[i[n]]);if(e.orderBy=e.orderBy||e.showField,!1!==e.orderBy&&(e.orderBy=this.setOrderbyOption(e.orderBy,e.showField)),e.multiple&&!e.selectToCloseList&&(e.autoFillResult=!1,e.autoSelectFirst=!1),e.pagination||(e.pageSize=200),("number"!==t.type(e.listSize)||e.listSize<0)&&(e.listSize=10),"string"==typeof e.formatItem){var o=e.formatItem;e.formatItem=function(t){return"function"==typeof Template&&o.match(/\#([a-zA-Z0-9_\-]+)$/)?Template(o.substring(1),t):o.replace(/\{(.*?)\}/gi,function(e){return e=e.substring(1,e.length-1),void 0!==t[e]?t[e]:""})}}this.option=e},d.prototype.escapeHTML=function(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t},d.prototype.strToArray=function(t){return t?t.replace(/[\s　]+/g,"").split(","):""},d.prototype.setOrderbyOption=function(e,i){var n=[],o=[];if("object"==typeof e)for(var a=0;a<e.length;a++)o=t.trim(e[a]).split(" "),o.length&&n.push(2===o.length?o.concat():[o[0],"ASC"]);else o=t.trim(e).split(" "),n[0]=2===o.length?o.concat():o[0].toUpperCase().match(/^(ASC|DESC)$/i)?[i,o[0].toUpperCase()]:[o[0],"ASC"];return n},d.prototype.setLanguage=function(){var t,e=this.option;switch(e.lang){case"en":t={add_btn:"Add button",add_title:"add a box",del_btn:"Del button",del_title:"delete a box",next:"Next",next_title:"Next"+e.pageSize+" (Right key)",prev:"Prev",prev_title:"Prev"+e.pageSize+" (Left key)",first_title:"First (Shift + Left key)",last_title:"Last (Shift + Right key)",get_all_btn:"Get All (Down key)",get_all_alt:"(button)",close_btn:"Close (Tab key)",close_alt:"(button)",loading:"loading...",loading_alt:"(loading)",page_info:"page_num of page_count",select_ng:"Attention : Please choose from among the list.",select_ok:"OK : Correctly selected.",not_found:"not found",ajax_error:"An error occurred while loading data.",clear:"Clear content",select_all:"Select current page",unselect_all:"Clear current page",clear_all:"Clear all selected",max_selected:"You can only select up to max_selected_limit items"};break;case"cn":default:t={add_btn:"添加按钮",add_title:"添加区域",del_btn:"删除按钮",del_title:"删除区域",next:"下一页",next_title:"下"+e.pageSize+" (→)",prev:"上一页",prev_title:"上"+e.pageSize+" (←)",first_title:"首页 (Shift + ←)",last_title:"尾页 (Shift + →)",get_all_btn:"获得全部 (↓)",get_all_alt:"(按钮)",close_btn:"关闭 (Tab键)",close_alt:"(按钮)",loading:"读取中...",loading_alt:"(读取中)",page_info:"第 page_num 页(共page_count页)",select_ng:"请注意：请从列表中选择.",select_ok:"OK : 已经选择.",not_found:"无查询结果",ajax_error:"加载数据时发生了错误！",clear:"清除内容",select_all:"选择当前页项目",unselect_all:"取消选择当前页项目",clear_all:"清除全部已选择项目",max_selected:"最多只能选择 max_selected_limit 个项目"}}this.message=t},d.prototype.setCssClass=function(){var t={container:"sp_container",container_open:"sp_container_open",re_area:"sp_result_area",result_open:"sp_result_area_open",control_box:"sp_control_box",element_box:"sp_element_box",navi:"sp_navi",results:"sp_results",re_off:"sp_results_off",select:"sp_over",select_ok:"sp_select_ok",select_ng:"sp_select_ng",selected:"sp_selected",input_off:"sp_input_off",message_box:"sp_message_box",disabled:"sp_disabled",button:"sp_button",caret_open:"sp_caret_open",btn_on:"sp_btn_on",btn_out:"sp_btn_out",input:"sp_input",clear_btn:"sp_clear_btn",align_right:"sp_align_right"};this.css_class=t},d.prototype.setProp=function(){this.prop={disabled:!1,current_page:1,max_page:1,is_loading:!1,xhr:!1,key_paging:!1,key_select:!1,prev_value:"",selected_text:"",last_input_time:void 0,init_set:!1},this.template={tag:{content:'<li class="selected_tag" itemvalue="#item_value#">#item_text#<span class="tag_close"><i class="spfont sp-close"></i></span></li>',textKey:"#item_text#",valueKey:"#item_value#"},page:{current:"page_num",total:"page_count"},msg:{maxSelectLimit:"max_selected_limit"}}},d.prototype.elementRealSize=function(e,i){var n,o,a,r={absolute:!1,clone:!1,includeMargin:!1,display:"block"},s=r,l=e.eq(0),c=[],d="";n=function(){a=l.parents().addBack().filter(":hidden"),d+="visibility: hidden !important; display: "+s.display+" !important; ",!0===s.absolute&&(d+="position: absolute !important;"),a.each(function(){var e=t(this),i=e.attr("style");c.push(i),e.attr("style",i?i+";"+d:d)})},o=function(){a.each(function(e){var i=t(this),n=c[e];void 0===n?i.removeAttr("style"):i.attr("style",n)})},n();var u=/(outer)/.test(i)?l[i](s.includeMargin):l[i]();return o(),u},d.prototype.setElem=function(e){var i={},n=this.option,o=this.css_class,a=this.message,r=t(e),s=r.css("width"),l=r.outerWidth();s.indexOf("%")>-1||r.parent().length>0&&r.parent().width()==l?l="100%":(l<=0&&(l=this.elementRealSize(r,"outerWidth")),l<150&&(l=150)),i.combo_input=r.attr({autocomplete:"off"}).addClass(o.input).wrap("<div>"),n.selectOnly&&i.combo_input.prop("readonly",!0),i.container=i.combo_input.parent().addClass(o.container),i.combo_input.prop("disabled")&&(n.multiple?i.container.addClass(o.disabled):i.combo_input.addClass(o.input_off)),i.container.width(l),i.button=t("<div>").addClass(o.button),i.dropdown=t('<span class="sp_caret"></span>'),i.clear_btn=t("<div>").html(t("<i>").addClass("spfont sp-close")).addClass(o.clear_btn).attr("title",a.clear),n.dropButton||i.clear_btn.addClass(o.align_right),i.element_box=t("<ul>").addClass(o.element_box),n.multiple&&n.multipleControlbar&&(i.control=t("<div>").addClass(o.control_box)),i.result_area=t("<div>").addClass(o.re_area),n.pagination&&(i.navi=t("<div>").addClass("sp_pagination").append("<ul>")),i.results=t("<ul>").addClass(o.results);var c=i.combo_input.attr("id")||i.combo_input.attr("name"),d=i.combo_input.attr("name")||"selectPage",u=d,p=c;if(i.hidden=t('<input type="hidden" class="sp_hidden" />').attr({name:u,id:p}).val(""),i.combo_input.attr({name:void 0!==r.data("name")?r.data("name"):d+"_text",id:c+"_text"}),i.hidden.attr("data-rule",i.combo_input.data("rule")||""),i.combo_input.attr("novalidate","novalidate"),i.container.append(i.hidden),n.dropButton&&(i.container.append(i.button),i.button.append(i.dropdown)),t(document.body).append(i.result_area),i.result_area.append(i.results),n.pagination&&i.result_area.append(i.navi),n.multiple){n.multipleControlbar&&(i.control.append('<button type="button" class="btn btn-default sp_clear_all" ><i class="spfont sp-clear"></i></button>'),i.control.append('<button type="button" class="btn btn-default sp_unselect_all" ><i class="spfont sp-unselect-all"></i></button>'),i.control.append('<button type="button" class="btn btn-default sp_select_all" ><i class="spfont sp-select-all"></i></button>'),i.control_text=t("<p>"),i.control.append(i.control_text),i.result_area.prepend(i.control)),i.container.addClass("sp_container_combo"),i.combo_input.addClass("sp_combo_input").before(i.element_box);var h=t("<li>").addClass("input_box");h.append(i.combo_input),i.element_box.append(h),i.combo_input.attr("placeholder")&&i.combo_input.attr("placeholder_bak",i.combo_input.attr("placeholder"))}this.elem=i},d.prototype.setButtonAttrDefault=function(){this.option.dropButton&&this.elem.button.attr("title",this.message.close_btn)},d.prototype.setInitRecord=function(e){var i=this,n=i.option,o=i.elem,a="";if("undefined"!=t.type(o.combo_input.data("init"))&&(n.initRecord=String(o.combo_input.data("init"))),e||n.initRecord||!o.combo_input.val()||(n.initRecord=o.combo_input.val()),o.combo_input.val(""),e||o.hidden.val(n.initRecord),a=e&&o.hidden.val()?o.hidden.val():n.initRecord)if("object"==typeof n.data){var r=new Array,s=a.split(",");t.each(s,function(t,e){for(var i=0;i<n.data.length;i++)if(n.data[i][n.keyField]==e){r.push(n.data[i]);break}}),!n.multiple&&r.length>1&&(r=[r[0]]),i.afterInit(i,r)}else{var l=n.params,c={},d=(n.searchField,{searchTable:n.dbTable,searchKey:n.keyField,searchValue:a,orderBy:n.orderBy,showField:n.showField,keyField:n.keyField,keyValue:a,searchField:n.searchField});if(l){var u=t.isFunction(l)?l(i):l;c=u&&t.isPlainObject(u)?t.extend({},d,u):d}else c=d;t.ajax({dataType:"json",type:"POST",url:n.data,data:c,success:function(e){var o=null;n.eAjaxSuccess&&t.isFunction(n.eAjaxSuccess)&&(o=n.eAjaxSuccess(e)),i.afterInit(i,o.list)},error:function(t,e,n){i.ajaxErrorNotify(i,n)}})}},d.prototype.afterInit=function(e,i){if(i&&(!t.isArray(i)||0!==i.length)){t.isArray(i)||(i=[i]);var n=e.option,o=e.css_class;if(e.data=i,n.multiple)e.prop.init_set=!0,e.clearAll(e),t.each(i,function(t,i){var o=n.escape?e.escapeHTML(i[n.keyField]):i[n.keyField],a=n.escape?e.escapeHTML(i[n.showField]):i[n.showField],r={text:a,value:o};e.isAlreadySelected(e,r)||e.addNewTag(e,i,r)}),e.tagValuesSet(e),e.inputResize(e),e.elem.hidden.blur(),e.prop.init_set=!1;else{var a=i[0],r=n.escape?e.escapeHTML(a[n.keyField]):a[n.keyField],s=n.escape?e.escapeHTML(a[n.showField]):a[n.showField];e.elem.combo_input.val(s),e.elem.hidden.val(r),e.prop.prev_value=s,e.prop.selected_text=s,n.selectOnly&&e.elem.combo_input.attr("title",e.message.select_ok).removeClass(o.select_ng).addClass(o.select_ok),e.putClearButton()}}},d.prototype.eDropdownButton=function(){var t=this;t.option.dropButton&&t.elem.button.mouseup(function(e){e.stopPropagation(),t.elem.result_area.is(":hidden")&&!t.elem.combo_input.prop("disabled")?t.elem.combo_input.focus():t.hideResults(t)})},d.prototype.eInput=function(){var e=this,i=e.option,n=e.elem,o=e.message,a=function(){e.prop.page_move=!1,e.suggest(e),e.setCssFocusedInput(e)};n.combo_input.keyup(function(t){e.processKey(e,t)}).keydown(function(t){e.processControl(e,t)}).focus(function(t){n.result_area.is(":hidden")&&(t.stopPropagation(),e.prop.first_show=!0,a())}),n.container.on("click.SelectPage","div."+e.css_class.clear_btn,function(n){n.stopPropagation(),e.disabled(e)||(e.clearAll(e,!0),e.elem.hidden.change(),i.eClear&&t.isFunction(i.eClear)&&i.eClear(e))}),n.result_area.on("mousedown.SelectPage",function(t){t.stopPropagation()}),i.multiple&&(i.multipleControlbar&&(n.control.find(".sp_select_all").on("click.SelectPage",function(t){e.selectAllLine(e)}).hover(function(){n.control_text.html(o.select_all)},function(){n.control_text.html("")}),n.control.find(".sp_unselect_all").on("click.SelectPage",function(t){e.unSelectAllLine(e)}).hover(function(){n.control_text.html(o.unselect_all)},function(){n.control_text.html("")}),n.control.find(".sp_clear_all").on("click.SelectPage",function(t){e.clearAll(e,!0)}).hover(function(){n.control_text.html(o.clear_all)},function(){n.control_text.html("")})),n.element_box.on("click.SelectPage",function(e){var i=e.target||e.srcElement;t(i).is("ul")&&n.combo_input.focus()}),n.element_box.on("click.SelectPage","span.tag_close",function(){var n=t(this).closest("li"),o=n.data("dataObj");e.removeTag(e,n),a(),i.eTagRemove&&t.isFunction(i.eTagRemove)&&i.eTagRemove([o])}),e.inputResize(e))},d.prototype.eWhole=function(){var e=this,i=e.css_class,n=function(t){t.elem.combo_input.val(""),t.option.multiple||t.elem.hidden.val(""),t.prop.selected_text=""};t(document.body).off("mousedown.selectPage").on("mousedown.selectPage",function(e){var o=e.target||e.srcElement,a=t(o).closest("div."+i.container);t("div."+i.container+"."+i.container_open).each(function(){if(this!=a[0]){var e=t(this),o=e.find("input."+i.input).data(d.dataKey);if(!o.elem.combo_input.val()&&o.elem.hidden.val()&&!o.option.multiple)return o.prop.current_page=1,n(o),o.hideResults(o),!0;o.elem.results.find("li").not("."+i.message_box).length?o.option.autoFillResult?o.elem.hidden.val()?o.hideResults(o):o.elem.results.find("li.sp_over").length?o.selectCurrentLine(o,!0):o.option.autoSelectFirst?(o.nextLine(o),o.selectCurrentLine(o,!0)):o.hideResults(o):o.hideResults(o):(o.option.noResultClean?n(o):o.option.multiple||o.elem.hidden.val(""),o.hideResults(o))}})})},d.prototype.eResultList=function(){var e=this,i=this.css_class;e.elem.results.children("li").hover(function(){if(e.prop.key_select)return void(e.prop.key_select=!1);t(this).hasClass(i.selected)||t(this).hasClass(i.message_box)||(t(this).addClass(i.select),e.setCssFocusedResults(e))},function(){t(this).removeClass(i.select)}).click(function(n){if(e.prop.key_select)return void(e.prop.key_select=!1);n.preventDefault(),n.stopPropagation(),t(this).hasClass(i.selected)||e.selectCurrentLine(e,!1)})},d.prototype.eScroll=function(){var e=this.css_class;t(window).on("scroll.SelectPage",function(i){t("div."+e.container+"."+e.container_open).each(function(){var i=t(this),n=i.find("input."+e.input).data(d.dataKey),o=n.elem.result_area.offset(),a=t(window).scrollTop(),r=t(document).height(),s=t(window).height(),l=n.elem.result_area.outerHeight(),c=o.top+l,u=r>s,p=n.elem.result_area.hasClass("shadowDown");u&&(p?c>s+a&&n.calcResultsSize(n):o.top<a&&n.calcResultsSize(n))})})},d.prototype.ePaging=function(){var t=this;t.option.pagination&&(t.elem.navi.find("li.csFirstPage").off("click").on("click",function(e){e.preventDefault(),t.firstPage(t)}),t.elem.navi.find("li.csPreviousPage").off("click").on("click",function(e){e.preventDefault(),t.prevPage(t)}),t.elem.navi.find("li.csNextPage").off("click").on("click",function(e){e.preventDefault(),t.nextPage(t)}),t.elem.navi.find("li.csLastPage").off("click").on("click",function(e){e.preventDefault(),t.lastPage(t)}))},d.prototype.ajaxErrorNotify=function(t,e){t.showMessage(t,t.message.ajax_error)},d.prototype.showMessage=function(t,e){if(e){var i='<li class="'+t.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e+"</li>";t.elem.results.empty().append(i).show(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.elem.control&&t.elem.control.hide(),t.option.pagination&&t.elem.navi.hide()}},d.prototype.scrollWindow=function(e,i){var n,o=e.getCurrentLine(e),a=o&&!i?o.offset().top:e.elem.container.offset().top;e.prop.size_li=e.elem.results.children("li:first").outerHeight(),n=e.prop.size_li;var r,s=t(window).height(),l=t(window).scrollTop(),c=l+s-n;if(o.length)if(a<l||n>s)r=a-l;else{if(!(a>c))return;r=a-c}else a<l&&(r=a-l);window.scrollBy(0,r)},d.prototype.setOpenStatus=function(t,e){var i=t.elem,n=t.css_class;e?(i.container.addClass(n.container_open),i.result_area.addClass(n.result_open)):(i.container.removeClass(n.container_open),i.result_area.removeClass(n.result_open))},d.prototype.setCssFocusedInput=function(t){},d.prototype.setCssFocusedResults=function(t){},d.prototype.checkValue=function(t){var e=t.elem.combo_input.val();e!=t.prop.prev_value&&(t.prop.prev_value=e,t.prop.first_show=!1,t.prop.current_page=1,t.option.selectOnly&&t.setButtonAttrDefault(),t.option.multiple||e||(t.elem.combo_input.val(""),t.elem.hidden.val(""),t.elem.clear_btn.remove()),t.suggest(t))},d.prototype.processKey=function(e,i){-1===t.inArray(i.keyCode,[37,38,39,40,27,9,13])&&(16!=i.keyCode&&e.setCssFocusedInput(e),e.inputResize(e),"string"===t.type(e.option.data)?(e.prop.last_input_time=i.timeStamp,setTimeout(function(){i.timeStamp-e.prop.last_input_time==0&&e.checkValue(e)},1e3*e.option.inputDelay)):e.checkValue(e))},d.prototype.processControl=function(e,i){if(t.inArray(i.keyCode,[37,38,39,40,27,9])>-1&&e.elem.result_area.is(":visible")||t.inArray(i.keyCode,[13,9])>-1&&e.getCurrentLine(e))switch(i.preventDefault(),i.stopPropagation(),i.cancelBubble=!0,i.returnValue=!1,i.keyCode){case 37:i.shiftKey?e.firstPage(e):e.prevPage(e);break;case 38:e.prop.key_select=!0,e.prevLine(e);break;case 39:i.shiftKey?e.lastPage(e):e.nextPage(e);break;case 40:e.elem.results.children("li").length?(e.prop.key_select=!0,e.nextLine(e)):e.suggest(e);break;case 9:e.prop.key_paging=!0,e.selectCurrentLine(e,!0);break;case 13:e.selectCurrentLine(e,!0);break;case 27:e.prop.key_paging=!0,e.hideResults(e)}},d.prototype.abortAjax=function(t){t.prop.xhr&&(t.prop.xhr.abort(),t.prop.xhr=!1)},d.prototype.suggest=function(e){var i,n=t.trim(e.elem.combo_input.val());i=e.option.multiple?n:n&&n==e.prop.selected_text?"":n,i=i.split(e.option.separator),e.option.eOpen&&t.isFunction(e.option.eOpen)&&e.option.eOpen.call(e),e.abortAjax(e);var o=e.prop.current_page||1;"object"==typeof e.option.data?e.searchForJson(e,i,o):e.searchForDb(e,i,o)},d.prototype.setLoading=function(t){""===t.elem.results.html()&&t.setOpenStatus(t,!0)},d.prototype.searchForDb=function(e,i,n){var o=e.option;o.eAjaxSuccess&&t.isFunction(o.eAjaxSuccess)||e.hideResults(e);var a=o.params,r={},s=o.searchField;i.length&&i[0]&&i.join(e.option.separator)!==e.prop.prev_value&&(n=1);var l={q_word:i,pageNumber:n,pageSize:o.pageSize,andOr:o.andOr,orderBy:o.orderBy,searchTable:o.dbTable,showField:e.option.showField,keyField:e.option.keyField,searchField:e.option.searchField};if(!1!==o.orderBy&&(l.orderBy=o.orderBy),l[s]=i[0],a){var c=t.isFunction(a)?a(e):a;r=c&&t.isPlainObject(c)?t.extend({},l,c):l}else r=l;e.prop.xhr=t.ajax({dataType:"json",url:o.data,type:"POST",data:r,success:function(a){if(!a||!t.isPlainObject(a))return e.hideResults(e),void e.ajaxErrorNotify(e,errorThrown);var r={},s={};try{r=o.eAjaxSuccess(a),s.originalResult=r.list,s.cnt_whole=r.totalRow}catch(t){return void e.showMessage(e,e.message.ajax_error)}if(e.elem.navi&&t(e.elem.navi).toggleClass("hide",s.cnt_whole<=s.originalResult.length),s.candidate=[],s.keyField=[],"object"!=typeof s.originalResult)return e.prop.xhr=null,void e.notFoundSearch(e);s.cnt_page=s.originalResult.length;for(var l=0;l<s.cnt_page;l++)for(var c in s.originalResult[l])c==o.keyField&&s.keyField.push(s.originalResult[l][c]),c==o.showField&&s.candidate.push(s.originalResult[l][c]);e.prepareResults(e,s,i,n)},error:function(t,i,n){"abort"!=i&&(e.hideResults(e),e.ajaxErrorNotify(e,n))},complete:function(){e.prop.xhr=null}})},d.prototype.searchForJson=function(e,i,n){var o=e.option,a=[],r=[],s=[],l={},c=0,d=[];do{r[c]=i[c].replace(/\W/g,"\\$&").toString(),d[c]=new RegExp(r[c],"gi"),c++}while(c<i.length);for(var c=0;c<o.data.length;c++){for(var u,p=!1,h=o.data[c],f=0;f<d.length;f++)if(u=h[o.searchField],o.formatItem&&t.isFunction(o.formatItem)&&(u=o.formatItem(h)),u.match(d[f])){if(p=!0,"OR"==o.andOr)break}else if(p=!1,"AND"==o.andOr)break;p&&a.push(h)}if(!1===o.orderBy)s=a.concat();else{for(var m=new RegExp("^"+r[0]+"$","gi"),g=new RegExp("^"+r[0],"gi"),v=[],y=[],b=[],c=0;c<a.length;c++){var x=o.orderBy[0][0],w=String(a[c][x]);w.match(m)?v.push(a[c]):w.match(g)?y.push(a[c]):b.push(a[c])}o.orderBy[0][1].match(/^asc$/i)?(v=e.sortAsc(e,v),y=e.sortAsc(e,y),b=e.sortAsc(e,b)):(v=e.sortDesc(e,v),y=e.sortDesc(e,y),b=e.sortDesc(e,b)),s=s.concat(v).concat(y).concat(b)}if(l.cnt_whole=s.length,e.prop.page_move)s.length<=(n-1)*o.pageSize&&(n=1,e.prop.current_page=1);else if(!o.multiple){var _=e.elem.hidden.val();if("undefined"!==t.type(_)&&""!==t.trim(_)){var k=0;t.each(s,function(t,e){if(e[o.keyField]==_)return k=t+1,!1}),n=Math.ceil(k/o.pageSize),n<1&&(n=1),e.prop.current_page=n}}var C=(n-1)*o.pageSize,S=C+o.pageSize;l.originalResult=[];for(var c=C;c<S&&void 0!==s[c];c++){l.originalResult.push(s[c]);for(var T in s[c])T==o.keyField&&(void 0===l.keyField&&(l.keyField=[]),l.keyField.push(s[c][T])),T==o.showField&&(void 0===l.candidate&&(l.candidate=[]),l.candidate.push(s[c][T]))}void 0===l.candidate&&(l.candidate=[]),l.cnt_page=l.candidate.length,e.prepareResults(e,l,i,n)},d.prototype.sortAsc=function(e,i){return i.sort(function(i,n){var o=i[e.option.orderBy[0][0]],a=n[e.option.orderBy[0][0]];return"number"===t.type(o)?o-a:String(o).localeCompare(String(a))}),i},d.prototype.sortDesc=function(e,i){return i.sort(function(i,n){var o=i[e.option.orderBy[0][0]],a=n[e.option.orderBy[0][0]];return"number"===t.type(o)?a-o:String(a).localeCompare(String(o))}),i},d.prototype.notFoundSearch=function(t){t.elem.results.empty(),t.calcResultsSize(t),t.setOpenStatus(t,!0),t.setCssFocusedInput(t)},d.prototype.prepareResults=function(t,e,i,n){t.data=e.originalResult,t.option.pagination&&t.setNavi(t,e.cnt_whole,e.cnt_page,n),e.keyField||(e.keyField=!1),t.option.selectOnly&&1===e.candidate.length&&e.candidate[0]==i[0]&&(t.elem.hidden.val(e.keyField[0]),this.setButtonAttrDefault());var o=!1;i&&i.length&&i[0]&&(o=!0),t.displayResults(t,e,o)},d.prototype.setNavi=function(t,e,i,n){var o=t.message,a=t.elem.navi.find("ul"),r=Math.ceil(e/t.option.pageSize);0===r?n=0:r<n?n=r:0===n&&(n=1),t.prop.current_page=n,t.prop.max_page=r,function(t,e,i,n){var a=function(){return o.page_info.replace(t.template.page.current,i).replace(t.template.page.total,n)};if(0===e.find("li").length){e.hide().empty();e.append('<li class="csFirstPage" title="'+o.first_title+'" ><a href="javascript:void(0);"> <i class="spfont sp-first"></i> </a></li>'),e.append('<li class="csPreviousPage" title="'+o.prev_title+'" ><a href="javascript:void(0);"><i class="spfont sp-previous"></i></a></li>'),e.append('<li class="pageInfoBox"><a href="javascript:void(0);"> '+a()+" </a></li>"),e.append('<li class="csNextPage" title="'+o.next_title+'" ><a href="javascript:void(0);"><i class="spfont sp-next"></i></a></li>'),e.append('<li class="csLastPage" title="'+o.last_title+'" ><a href="javascript:void(0);"> <i class="spfont sp-last"></i> </a></li>'),e.show()}else e.find("li.pageInfoBox a").html(a())}(t,a,n,r);var s="disabled",l=a.find("li.csFirstPage"),c=a.find("li.csPreviousPage"),d=a.find("li.csNextPage"),u=a.find("li.csLastPage");1===n||0===n?(l.hasClass(s)||l.addClass(s),c.hasClass(s)||c.addClass(s)):(l.hasClass(s)&&l.removeClass(s),c.hasClass(s)&&c.removeClass(s)),n===r||0===r?(d.hasClass(s)||d.addClass(s),u.hasClass(s)||u.addClass(s)):(d.hasClass(s)&&d.removeClass(s),u.hasClass(s)&&u.removeClass(s)),r>1&&t.ePaging()},d.prototype.displayResults=function(e,i,n){var o=e.option,a=e.elem;if(a.results.hide().empty(),o.multiple&&"number"===t.type(o.maxSelectLimit)&&o.maxSelectLimit>0){var r=a.element_box.find("li.selected_tag").length;if(r>0&&r>=o.maxSelectLimit){var s=e.message.max_selected;return void e.showMessage(e,s.replace(e.template.msg.maxSelectLimit,o.maxSelectLimit))}}if(i.candidate.length)for(var l=i.candidate,c=i.keyField,d=a.hidden.val(),u=d?d.split(","):[],p="",h=0;h<l.length;h++){if(o.formatItem&&t.isFunction(o.formatItem))try{p=o.formatItem(i.originalResult[h])}catch(t){console.error("formatItem内容格式化函数内容设置不正确！"),p=o.escape?e.escapeHTML(l[h]):l[h]}else p=o.escape?e.escapeHTML(l[h]):l[h];var f=t("<li>").html(p).attr({pkey:c[h],index:h});o.formatItem||f.attr("title",p),-1!==t.inArray(c[h].toString(),u)&&f.addClass(e.css_class.selected),f.data("dataObj",i.originalResult[h]),a.results.append(f)}else{var m='<li class="'+e.css_class.message_box+'"><i class="spfont sp-warning"></i> '+e.message.not_found+"</li>";a.results.append(m)}a.results.show(),o.multiple&&o.multipleControlbar&&a.control.show(),o.pagination&&a.navi.show(),e.calcResultsSize(e),e.setOpenStatus(e,!0),e.eResultList(),e.eScroll(),n&&i.candidate.length&&o.autoSelectFirst&&e.nextLine(e)},d.prototype.calcResultsSize=function(e){var i=e.option,n=e.elem,o=function(){if("static"!==n.container.css("position")){if(!i.pagination){var e=n.results.find("li:first").outerHeight(!0),o=e*i.listSize;n.results.css({"max-height":o,"overflow-y":"auto"})}var a=t(document).width(),r=t(document).height(),s=t(window).height(),l=n.container.offset(),c=t(window).scrollTop(),d=n.result_area.outerWidth(),o=n.result_area.outerHeight(),u=l.left,p=n.container.outerHeight(),h=l.left+d>a?u-(d-n.container.outerWidth()):u,f=l.top,m=0,g=f+p+o+5,v=f+o+5,y=r>s;return f-c-5>o&&y&&g>s+c||!y&&g>s&&f>=v?(m=l.top-o-5,n.result_area.removeClass("shadowUp shadowDown").addClass("shadowUp")):(m=l.top+(i.multiple?n.container.outerHeight():p),n.result_area.removeClass("shadowUp shadowDown").addClass("shadowDown"),m+=5),{top:m+"px",left:h+"px"}}var l=n.combo_input.offset();n.result_area.css({top:l.top+n.combo_input.outerHeight()+"px",left:l.left+"px"})};if(n.result_area.is(":visible"))n.result_area.css(o());else{var a=o();n.result_area.css(a).show(1,function(){var t=o();a.top===t.top&&a.left===t.left||n.result_area.css(t)})}},d.prototype.hideResults=function(e){e.prop.key_paging&&(e.scrollWindow(e,!0),e.prop.key_paging=!1),e.setCssFocusedInput(e),e.option.autoFillResult,e.elem.results.empty(),e.elem.result_area.hide(),e.setOpenStatus(e,!1),t(window).off("scroll.SelectPage"),e.abortAjax(e),e.setButtonAttrDefault()},d.prototype.disabled=function(e,i){var n=(e.option,e.elem);if("undefined"===t.type(i))return n.combo_input.prop("disabled");"boolean"===t.type(i)&&(n.combo_input.prop("disabled",i),i?n.container.addClass(e.css_class.disabled):n.container.removeClass(e.css_class.disabled))},d.prototype.firstPage=function(t){t.prop.current_page>1&&(t.prop.current_page=1,t.prop.page_move=!0,t.suggest(t))},d.prototype.prevPage=function(t){t.prop.current_page>1&&(t.prop.current_page--,t.prop.page_move=!0,t.suggest(t))},d.prototype.nextPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page++,t.prop.page_move=!0,t.suggest(t))},d.prototype.lastPage=function(t){t.prop.current_page<t.prop.max_page&&(t.prop.current_page=t.prop.max_page,t.prop.page_move=!0,t.suggest(t))},d.prototype.afterAction=function(t,e){t.inputResize(t),t.elem.combo_input.change(),t.setCssFocusedInput(t),t.prop.init_set||(t.option.multiple?(t.option.selectToCloseList&&(t.hideResults(t),t.elem.combo_input.blur()),!t.option.selectToCloseList&&e&&(t.suggest(t),t.elem.combo_input.focus())):(t.hideResults(t),t.elem.combo_input.blur()))},d.prototype.selectCurrentLine=function(e,i){e.scrollWindow(e,!0);var n=e.option,o=e.getCurrentLine(e);if(o){var a=o.data("dataObj"),r=a[n.showField]||o.text(),s=o.attr("pkey");if(n.multiple){e.elem.combo_input.val("");var l={text:r,value:s};e.isAlreadySelected(e,l)||(e.addNewTag(e,a,l),e.tagValuesSet(e))}else e.elem.combo_input.val(r),e.elem.hidden.val(s);n.selectOnly&&e.setButtonAttrDefault(),n.eSelect&&t.isFunction(n.eSelect)&&n.eSelect(a,e),e.prop.prev_value=e.elem.combo_input.val(),e.prop.selected_text=e.elem.combo_input.val(),e.putClearButton()}e.afterAction(e,!0)},d.prototype.putClearButton=function(){this.option.multiple||this.elem.combo_input.prop("disabled")||this.elem.container.append(this.elem.clear_btn)},d.prototype.selectAllLine=function(e){var i=e.option,n=new Array;e.elem.results.find("li").each(function(o,a){var r=t(a),s=r.data("dataObj"),l=s[i.showField]||r.text(),c=r.attr("pkey"),d={text:l,value:c};if(e.isAlreadySelected(e,d)||(e.addNewTag(e,s,d),e.tagValuesSet(e)),n.push(s),"number"===t.type(i.maxSelectLimit)&&i.maxSelectLimit>0&&i.maxSelectLimit===e.elem.element_box.find("li.selected_tag").length)return!1}),i.eSelect&&t.isFunction(i.eSelect)&&i.eSelect(n,e),e.afterAction(e,!0)},d.prototype.unSelectAllLine=function(e){var i=e.option,n=(e.elem.results.find("li").length,[]);e.elem.results.find("li").each(function(i,o){var a=t(o).attr("pkey"),r=e.elem.element_box.find('li.selected_tag[itemvalue="'+a+'"]');r.length&&n.push(r.data("dataObj")),e.removeTag(e,r)}),e.afterAction(e,!0),i.eTagRemove&&t.isFunction(i.eTagRemove)&&i.eTagRemove(n)},d.prototype.clearAll=function(e,i){var n=e.option,o=[];n.multiple&&(e.elem.element_box.find("li.selected_tag").each(function(e,i){o.push(t(i).data("dataObj")),i.remove()}),e.elem.element_box.find("li.selected_tag").remove()),e.reset(e),e.afterAction(e,i),n.multiple?n.eTagRemove&&t.isFunction(n.eTagRemove)&&n.eTagRemove(o):e.elem.clear_btn.remove()},d.prototype.reset=function(t){t.elem.combo_input.val(""),t.elem.hidden.val(""),t.prop.prev_value="",t.prop.selected_text="",t.prop.current_page=1},d.prototype.getCurrentLine=function(t){if(t.elem.result_area.is(":hidden"))return!1;var e=t.elem.results.find("li."+t.css_class.select);return!!e.length&&e},d.prototype.isAlreadySelected=function(e,i){var n=!1;if(i.value){var o=e.elem.hidden.val();if(o){var a=o.split(",");a&&a.length&&-1!=t.inArray(i.value,a)&&(n=!0)}}return n},d.prototype.addNewTag=function(e,i,n){if(e.option.multiple&&i&&n){var o,a=e.template.tag.content;a=a.replace(e.template.tag.textKey,n.text),a=a.replace(e.template.tag.valueKey,n.value),o=t(a),o.data("dataObj",i),e.elem.combo_input.prop("disabled")&&o.find("span.tag_close").hide(),e.elem.combo_input.closest("li").before(o)}},d.prototype.removeTag=function(e,i){var n=t(i).attr("itemvalue"),o=e.elem.hidden.val();if("undefined"!=t.type(n)&&o){var a=o.split(","),r=t.inArray(n.toString(),a);-1!=r&&(a.splice(r,1),e.elem.hidden.val(a.toString()).trigger("change"))}t(i).remove(),e.inputResize(e)},d.prototype.tagValuesSet=function(e){if(e.option.multiple){var i=e.elem.element_box.find("li.selected_tag");if(i&&i.length){var n=new Array;t.each(i,function(e,i){var o=t(i).attr("itemvalue");"undefined"!==t.type(o)&&n.push(o)}),n.length&&e.elem.hidden.val(n.join(",")).trigger("change")}}},d.prototype.inputResize=function(t){if(t.option.multiple){var e=t.elem.combo_input.closest("li");0===t.elem.element_box.find("li.selected_tag").length?(e.hasClass("full_width")||e.addClass("full_width"),t.elem.combo_input.attr("placeholder_bak")&&t.elem.combo_input.attr("placeholder",t.elem.combo_input.attr("placeholder_bak")).removeAttr("style")):function(t,e){e.removeClass("full_width");var i=t.elem.combo_input.val().length+1,n=.75*i+"em";t.elem.combo_input.css("width",n).removeAttr("placeholder")}(t,e)}},d.prototype.nextLine=function(t){var e,i=t.getCurrentLine(t);if(i?(e=t.elem.results.children("li").index(i),i.removeClass(t.css_class.select)):e=-1,++e<t.elem.results.children("li").length){t.elem.results.children("li").eq(e).addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)},d.prototype.prevLine=function(t){var e,i=t.getCurrentLine(t);if(i?(e=t.elem.results.children("li").index(i),i.removeClass(t.css_class.select)):e=t.elem.results.children("li").length,--e>-1){t.elem.results.children("li").eq(e).addClass(t.css_class.select),t.setCssFocusedResults(t)}else t.setCssFocusedInput(t);t.scrollWindow(t,!1)};var u=t.fn.selectPage;t.fn.selectPage=e,
t.fn.selectPage.Constructor=d,t.fn.selectPageClear=n,t.fn.selectPageRefresh=o,t.fn.selectPageData=a,t.fn.selectPageDisabled=r,t.fn.selectPageText=s,t.fn.selectPageSelectedData=l,t.fn.selectPage.noConflict=function(){return t.fn.selectPage=u,this}}(window.jQuery),define("selectpage",function(){});