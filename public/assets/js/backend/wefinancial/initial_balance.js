define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wefinancial/initial_balance/index',
                    save_url: 'wefinancial/initial_balance/save',
                    trial_balance_url: 'wefinancial/initial_balance/trialBalance',
                    table: 'wefinancial_initial_balance',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'code',
                fixedColumns: true,
                fixedRightNumber: 1,
                pagination: false,
                commonSearch: false,
                search: false,
                showToggle: false,
                showColumns: false,
                showExport: false,
                showRefresh: false,
                columns: [
                    [
                        {
                            field: 'code',
                            title: '科目编码',
                            align: 'left',
                            width: '150px'
                        },
                        {
                            field: 'name',
                            title: '科目名称',
                            align: 'left',
                            width: '300px',
                            formatter: wef.TreeTable.formatter.treeNode
                        },
                        {
                            field: 'balance_direction',
                            title: '科目方向',
                            align: 'center',
                            width: '100px',
                            formatter: function(value, row, index) {
                                return '<span class="label label-' + (value === '借' ? 'primary' : 'success') + '">' + value + '</span>';
                            }
                        },
                        {
                            field: 'begin_direction',
                            title: '余额方向',
                            align: 'center',
                            width: '120px',
                            formatter: Controller.api.directionFormatter
                        },
                        {
                            field: 'begin_balance',
                            title: '期初余额',
                            align: 'left',
                            width: '150px',
                            formatter: Controller.api.balanceFormatter
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定树形表格事件
            wef.TreeTable.bindTreeEvents(table);

            // 表格加载完成后初始化树形结构
            table.on('load-success.bs.table', function() {
                wef.TreeTable.initTreeState('#table');
                Controller.api.bindInputEvents();
                Controller.api.checkPeriodStatus();
            });

            // 绑定按钮事件
            Controller.api.bindButtonEvents(table);
        },

        api: {
            // 余额方向格式化器
            directionFormatter: function(value, row, index) {
                if (row.leaf_flag == 0) {
                    return '-'; // 非末级科目不显示方向选择
                }

                var options = '';
                $.each(window.beginDirectionList, function(key, text) {
                    var selected = (value === key) ? 'selected' : '';
                    options += '<option value="' + key + '" ' + selected + '>' + text + '</option>';
                });

                return '<select class="form-control  direction-select" data-subject-id="' + row.id + '">' + options + '</select>';
            },

            // 期初余额格式化器
            balanceFormatter: function(value, row, index) {
                if (row.leaf_flag == 0) {
                    return '-'; // 非末级科目不显示余额输入
                }

                var displayValue = value && value !== '0.00' ? value : '';
                return '<input type="number" class="form-control  text-right balance-input" ' +
                       'data-subject-id="' + row.id + '" ' +
                       'value="' + displayValue + '" ' +
                       'step="0.01" min="0" placeholder="0.00" style="width: 130px;">';
            },

            // 绑定输入框事件
            bindInputEvents: function() {
                // 余额输入框事件
                $(document).off('input.balance').on('input.balance', '.balance-input', function() {
                    Controller.api.updateTrialBalance();
                });

                // 方向选择框事件
                $(document).off('change.direction').on('change.direction', '.direction-select', function() {
                    Controller.api.updateTrialBalance();
                });
            },

            // 绑定按钮事件
            bindButtonEvents: function(table) {
                // 保存按钮
                $('#btn-save').on('click', function() {
                    Controller.api.saveInitialBalance();
                });

                // 试算平衡按钮
                $('#btn-trial-balance').on('click', function() {
                    Controller.api.performTrialBalance();
                });

                // 展开全部按钮
                $('#btn-expand-all').on('click', function() {
                    wef.TreeTable.expandAll(table);
                });

                // 收起全部按钮
                $('#btn-collapse-all').on('click', function() {
                    wef.TreeTable.collapseAll(table);
                });
            },

            // 收集表单数据
            collectFormData: function() {
                var balances = {};

                $('.balance-input').each(function() {
                    var $this = $(this);
                    var subjectId = $this.data('subject-id');
                    var balance = parseFloat($this.val()) || 0;
                    var direction = $('.direction-select[data-subject-id="' + subjectId + '"]').val() || '借';

                    if (balance > 0) {
                        balances[subjectId] = {
                            begin_balance: balance,
                            begin_direction: direction
                        };
                    }
                });

                return { balances: balances };
            },

            // 保存初始余额
            saveInitialBalance: function(forceUpdate) {
                var data = Controller.api.collectFormData();

                if (forceUpdate) {
                    data.force_update = true;
                }

                if (Object.keys(data.balances).length === 0) {
                    Toastr.warning('请至少输入一个科目的初始余额');
                    return;
                }

                $.ajax({
                    url: 'wefinancial/initial_balance/save',
                    type: 'POST',
                    data: data,
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            Toastr.success(response.msg);
                        } else {
                            Controller.api.handleSaveError(response);
                        }
                    },
                    error: function(xhr, status, error) {
                        Toastr.error('保存失败：' + error);
                    }
                });
            },

            // 处理保存错误和警告
            handleSaveError: function(response) {
                var data = response.data || {};

                // 如果有错误，直接显示错误信息
                if (data.errors && data.errors.length > 0) {
                    var errorMsg = '';
                    $.each(data.errors, function(index, error) {
                        if (error.type === 'balance_error') {
                            errorMsg += error.message + '\n';
                            errorMsg += '借方合计：' + error.debit_total + '\n';
                            errorMsg += '贷方合计：' + error.credit_total + '\n';
                            errorMsg += '差额：' + error.difference + '\n';
                        } else if (error.type === 'period_closed_error') {
                            errorMsg += error.message + '\n';
                            errorMsg += '期间：' + error.period + '\n';
                        } else {
                            errorMsg += error.message + '\n';
                        }
                    });
                    Toastr.error(errorMsg);
                    return;
                }

                // 如果有警告，显示确认对话框
                if (data.warnings && data.warnings.length > 0) {
                    var warningMsg = '';
                    $.each(data.warnings, function(index, warning) {
                        if (warning.type === 'existing_records_warning') {
                            warningMsg += warning.message + '\n\n';
                            if (warning.details.non_initial_records > 0) {
                                warningMsg += '非初始期间记录数：' + warning.details.non_initial_records + '\n';
                            }
                            if (warning.details.posted_vouchers > 0) {
                                warningMsg += '已记账凭证数：' + warning.details.posted_vouchers + '\n';
                            }
                        } else {
                            warningMsg += warning.message + '\n';
                        }
                    });

                    warningMsg += '\n是否确认继续保存？';

                    Layer.confirm(warningMsg, {
                        title: '警告',
                        icon: 0,
                        btn: ['确认保存', '取消']
                    }, function(index) {
                        Layer.close(index);
                        Controller.api.saveInitialBalance(true); // 强制更新
                    });
                    return;
                }

                // 其他错误
                Toastr.error(response.msg || '保存失败');
            },

            // 执行试算平衡
            performTrialBalance: function() {
                var data = Controller.api.collectFormData();

                $.ajax({
                    url: 'wefinancial/initial_balance/trialBalance',
                    type: 'POST',
                    data: data,
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            Controller.api.displayTrialBalanceResult(response.data);
                        } else {
                            Toastr.error(response.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        Toastr.error('试算平衡失败：' + error);
                    }
                });
            },

            // 显示试算平衡结果
            displayTrialBalanceResult: function(result) {
                $('#debit-total').text(result.debit_total);
                $('#credit-total').text(result.credit_total);
                $('#difference').text(result.difference);
                $('#balance-message').text(result.message);

                var $resultDiv = $('#trial-balance-result');
                $resultDiv.removeClass('alert-success alert-danger');

                if (result.is_balanced) {
                    $resultDiv.addClass('alert-success');
                } else {
                    $resultDiv.addClass('alert-danger');
                }

                $resultDiv.show();
            },

            // 实时更新试算平衡（可选功能）
            updateTrialBalance: function() {
                // 这里可以实现实时计算显示，暂时留空
                // 用户可以点击试算平衡按钮来查看结果
            },

            // 检查期间状态并禁用相关功能
            checkPeriodStatus: function() {
                // 检查期间是否已结账
                if (window.periodStatus == 1) {
                    // 禁用保存按钮
                    $('#btn-save').prop('disabled', true).addClass('disabled');

                    // 禁用所有输入框和选择框
                    $('.balance-input, .direction-select').prop('disabled', true);

                    // 添加提示信息
                    $('#btn-save').attr('title', '期间已结账，不允许设置初始余额');
                }
            }
        }
    };

    return Controller;
});
