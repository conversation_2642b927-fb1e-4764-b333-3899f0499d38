define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wefinancial/subject_type/index' + location.search,
          add_url: 'wefinancial/subject_type/add',
          edit_url: 'wefinancial/subject_type/edit',
          del_url: 'wefinancial/subject_type/del',
          multi_url: 'wefinancial/subject_type/multi',
          import_url: 'wefinancial/subject_type/import',
          table: 'wefinancial_subject_type',
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'id',
        pagination: false,
        commonSearch: false,
        search: false,
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {
              field: 'name', title: __('Name'), align: 'left', formatter: wef.TreeTable.formatter.treeNode
            },
            {
              field: 'status',
              title: __('Status'),
              searchList: {"1": __('Status 1'), "0": __('Status 0')},
              formatter: Table.api.formatter.toggle
            },
            {
              field: 'operate',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });



      // 为表格绑定事件
      Table.api.bindevent(table);

      // 绑定树形表格事件
      wef.TreeTable.bindTreeEvents(table);
      table.on('load-success.bs.table', function() {
        wef.TreeTable.initTreeState('#table');
        wef.TreeTable.expandAll(table);
      });
    },
    recyclebin: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          'dragsort_url': ''
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: 'wefinancial/subject_type/recyclebin' + location.search,
        pk: 'id',
        sortName: 'id',
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {field: 'name', title: __('Name'), align: 'left'},
            {
              field: 'deletetime',
              title: __('Deletetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              width: '140px',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              buttons: [
                {
                  name: 'Restore',
                  text: __('Restore'),
                  classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                  icon: 'fa fa-rotate-left',
                  url: 'wefinancial/subject_type/restore',
                  refresh: true
                },
                {
                  name: 'Destroy',
                  text: __('Destroy'),
                  classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                  icon: 'fa fa-times',
                  url: 'wefinancial/subject_type/destroy',
                  refresh: true
                }
              ],
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },

    add: function () {
      Controller.api.bindevent();
    },
    edit: function () {
      Controller.api.bindevent();
    },
    api: {
      bindevent: function () {
        Form.api.bindevent($("form[role=form]"));
      }
    }
  };
  return Controller;
});
