define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wefinancial/report_column/index' + location.search,
                    add_url: 'wefinancial/report_column/add',
                    edit_url: 'wefinancial/report_column/edit',
                    del_url: 'wefinancial/report_column/del',
                    multi_url: 'wefinancial/report_column/multi',
                    import_url: 'wefinancial/report_column/import',
                    table: 'wefinancial_report_column',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'template_id', title: __('Template_id')},
                        {field: 'column_name', title: __('Column_name'), operate: 'LIKE'},
                        {field: 'column_position', title: __('Column_position')},
                        {field: 'data_type', title: __('Data_type'), searchList: {"amount":__('Data_type amount'),"percentage":__('Data_type percentage'),"text":__('Data_type text')}, formatter: Table.api.formatter.normal},
                        {field: 'period_type', title: __('Period_type'), searchList: {"current_month":__('Period_type current_month'),"current_year":__('Period_type current_year'),"last_year":__('Period_type last_year'),"last_month":__('Period_type last_month'),"custom":__('Period_type custom')}, formatter: Table.api.formatter.normal},
                        {field: 'balance_type', title: __('Balance_type'), searchList: {"beginning":__('Balance_type beginning'),"ending":__('Balance_type ending'),"period":__('Balance_type period')}, formatter: Table.api.formatter.normal},
                        {field: 'decimal_places', title: __('Decimal_places')},
                        {field: 'show_unit', title: __('Show_unit'), operate: 'LIKE'},
                        {field: 'width', title: __('Width')},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"0":__('Status 0')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
