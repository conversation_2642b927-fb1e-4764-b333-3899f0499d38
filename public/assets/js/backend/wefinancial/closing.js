define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wefinancial/closing/index' + location.search,
          table: 'wefinancial_period',
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'period',
        sortOrder: 'desc',
        columns: [
          [
            {checkbox: true},
            {field: 'period', title: '会计期间'},
            {
              field: 'status',
              title: '结账状态',
              formatter: function (value, row, index) {
                if (value == 1) {
                  return '<span class="label label-success">已结账</span>';
                } else {
                  return '<span class="label label-warning">未结账</span>';
                }
              }
            },
            {
              field: 'voucher_stats',
              title: '凭证统计',
              formatter: function (value, row, index) {
                var html = '';
                html += '<small>总计: ' + (row.total_vouchers || 0) + '</small><br>';
                html += '<small class="text-danger">草稿: ' + (row.draft_vouchers || 0) + '</small> ';
                html += '<small class="text-warning">已审: ' + (row.audited_vouchers || 0) + '</small> ';
                html += '<small class="text-success">已记: ' + (row.posted_vouchers || 0) + '</small>';
                return html;
              }
            },
            {field: 'remark', title: '备注', width: '200px'},
            {
              field: 'createtime',
              title: '创建时间',
              width: '160px',
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              title: '操作',
              width: '150px',
              formatter: function (value, row, index) {
                var html = '';
                if (row.can_close && row.status == 0) {
                  html += '<a href="javascript:;" class="btn btn-xs btn-success btn-close-single" data-period="' + row.period + '" title="结账"><i class="fa fa-lock"></i> 结账</a> ';
                }
                if (row.can_unclose && row.status == 1) {
                  html += '<a href="javascript:;" class="btn btn-xs btn-warning btn-unclose-single" data-period="' + row.period + '" title="反结账"><i class="fa fa-unlock"></i> 反结账</a>';
                }
                if (!row.can_close && row.status == 0) {
                  html += '<span class="text-muted">不可结账</span>';
                }
                if (!row.can_unclose && row.status == 1) {
                  html += '<span class="text-muted">不可反结账</span>';
                }
                return html;
              }
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);

      // 结账按钮事件
      $(document).on('click', '.btn-close-period', function () {
        var ids = Table.api.selectedids(table);
        if (ids.length === 0) {
          Toastr.error('请选择要结账的期间');
          return;
        }
        if (ids.length > 1) {
          Toastr.error('每次只能选择一个期间进行结账');
          return;
        }

        var row = Table.api.getrowbyid(table, ids[0]);
        Controller.closePeriod(row.period);
      });

      // 反结账按钮事件
      $(document).on('click', '.btn-unclose-period', function () {
        var ids = Table.api.selectedids(table);
        if (ids.length === 0) {
          Toastr.error('请选择要反结账的期间');
          return;
        }
        if (ids.length > 1) {
          Toastr.error('每次只能选择一个期间进行反结账');
          return;
        }

        var row = Table.api.getrowbyid(table, ids[0]);
        Controller.unclosePeriod(row.period);
      });

      // 单行结账按钮事件
      $(document).on('click', '.btn-close-single', function () {
        var period = $(this).data('period');
        Controller.closePeriod(period);
      });

      // 单行反结账按钮事件
      $(document).on('click', '.btn-unclose-single', function () {
        var period = $(this).data('period');
        Controller.unclosePeriod(period);
      });

      // 刷新按钮事件
      $(document).on('click', '.btn-refresh', function () {
        table.bootstrapTable('refresh');
      });
    },

    // 执行结账
    closePeriod: function (period) {
      Layer.confirm('确定要对期间 ' + period + ' 执行结账操作吗？<br><br><span class="text-danger">结账后该期间将被锁定，不可再录入或修改凭证！</span>', {
        icon: 3,
        title: '结账确认',
        btn: ['确定结账', '取消']
      }, function (index) {
        $.ajax({
          url: 'wefinancial/closing/close',
          type: 'POST',
          data: {period: period},
          dataType: 'json',
          success: function (ret) {
            if (ret.code === 1) {
              Layer.msg(ret.msg, {icon: 1});
              $("#table").bootstrapTable('refresh');
            } else {
              Layer.msg(ret.msg, {icon: 2});
            }
          },
          error: function () {
            Layer.msg('网络错误，请重试', {icon: 2});
          }
        });
        Layer.close(index);
      });
    },

    // 执行反结账
    unclosePeriod: function (period) {
      Layer.confirm('确定要对期间 ' + period + ' 执行反结账操作吗？<br><br><span class="text-warning">反结账后可以继续录入和修改该期间的凭证。</span>', {
        icon: 3,
        title: '反结账确认',
        btn: ['确定反结账', '取消']
      }, function (index) {
        $.ajax({
          url: 'wefinancial/closing/unclose',
          type: 'POST',
          data: {period: period},
          dataType: 'json',
          success: function (ret) {
            if (ret.code === 1) {
              Layer.msg(ret.msg, {icon: 1});
              $("#table").bootstrapTable('refresh');
            } else {
              Layer.msg(ret.msg, {icon: 2});
            }
          },
          error: function () {
            Layer.msg('网络错误，请重试', {icon: 2});
          }
        });
        Layer.close(index);
      });
    }
  };

  return Controller;
});
