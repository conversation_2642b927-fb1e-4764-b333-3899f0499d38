define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wefinancial/period_balance/index' + location.search,
          add_url: 'wefinancial/period_balance/add',
          edit_url: 'wefinancial/period_balance/edit',
          del_url: 'wefinancial/period_balance/del',
          multi_url: 'wefinancial/period_balance/multi',
          import_url: 'wefinancial/period_balance/import',
          table: 'wefinancial_period_balance',
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'id',
        fixedColumns: true,
        fixedRightNumber: 1,
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {field: 'subject_id', title: __('Subject_id')},
            {field: 'subject.name', title: __('科目'),align: 'left', formatter: function (_,row){
              return row.subject.code + ' ' + row.subject.name
              }, operate: false},
            {field: 'period', title: __('Period'), operate: 'LIKE'},
            {field: 'begin_balance', title: __('Begin_balance'), operate: 'BETWEEN'},
            {
              field: 'begin_direction',
              title: __('Begin_direction'),
              searchList: {"借": __('借'), "贷": __('贷')},
              formatter: Table.api.formatter.normal
            },
            {field: 'debit_total', title: __('Debit_total'), operate: 'BETWEEN'},
            {field: 'credit_total', title: __('Credit_total'), operate: 'BETWEEN'},
            {field: 'end_balance', title: __('End_balance'), operate: 'BETWEEN'},
            {
              field: 'end_direction',
              title: __('End_direction'),
              searchList: {"借": __('借'), "贷": __('贷')},
              formatter: Table.api.formatter.normal
            },
            // {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
            {
              field: 'updatetime',
              title: __('Updatetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              autocomplete: false,
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },
    recyclebin: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          'dragsort_url': ''
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: 'wefinancial/period_balance/recyclebin' + location.search,
        pk: 'id',
        sortName: 'id',
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {
              field: 'deletetime',
              title: __('Deletetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              width: '140px',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              buttons: [
                {
                  name: 'Restore',
                  text: __('Restore'),
                  classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                  icon: 'fa fa-rotate-left',
                  url: 'wefinancial/period_balance/restore',
                  refresh: true
                },
                {
                  name: 'Destroy',
                  text: __('Destroy'),
                  classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                  icon: 'fa fa-times',
                  url: 'wefinancial/period_balance/destroy',
                  refresh: true
                }
              ],
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },

    add: function () {
      Controller.api.bindevent();
    },
    edit: function () {
      Controller.api.bindevent();
    },
    api: {
      bindevent: function () {
        Form.api.bindevent($("form[role=form]"));
      }
    }
  };
  return Controller;
});
