define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {


  var Controller = {
    index: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          index_url: 'wefinancial/subject/index' + location.search,
          add_url: 'wefinancial/subject/add',
          edit_url: 'wefinancial/subject/edit',
          del_url: 'wefinancial/subject/del',
          multi_url: 'wefinancial/subject/multi',
          import_url: 'wefinancial/subject/import',
          table: 'wefinancial_subject',
        }
      });

      var table = $("#table");


      // 初始化表格
      table.bootstrapTable({
        url: $.fn.bootstrapTable.defaults.extend.index_url,
        pk: 'id',
        sortName: 'id',
        fixedColumns: true,
        fixedRightNumber: 1,
        pagination: false,
        commonSearch: false,
        search: false,
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {field: 'code', title: __('Code'), operate: 'LIKE', align: 'left'},
            {
              field: 'name',
              title: __('Name'),
              align: 'left',
              formatter: wef.TreeTable.formatter.treeNode
            }, {
            field: 'balance_direction',
            title: __('Balance_direction'),
            searchList: {"借": __('借'), "贷": __('贷')},
            formatter: Table.api.formatter.flag
          },
            {
              field: 'type.name',
              title: __('Type'),
              formatter: Table.api.formatter.normal
            },
            {
              field: 'cash_flag',
              title: __('Cash_flag'),
              searchList: {"1": __('Cash_flag 1'), "0": __('Cash_flag 0')},
              formatter: Table.api.formatter.flag
            },
            {
              field: 'aux_type_ids_text',
              title: __('Aux_type_ids'),
              operate: 'LIKE',
              table: table,
              class: 'autocontent',
              formatter: Table.api.formatter.content
            },
            {
              field: 'status',
              title: __('Status'),
              searchList: {"1": __('Status 1'), "0": __('Status 0')},
              formatter: Table.api.formatter.toggle
            },
            {
              field: 'operate',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      })



      // 为表格绑定事件
      Table.api.bindevent(table);

      // 绑定树形表格事件
      wef.TreeTable.bindTreeEvents(table);

      // 表格加载完成后初始化树形结构
      table.on('load-success.bs.table', function() {
        wef.TreeTable.initTreeState('#table');
      });

      // 绑定tab点击事件
      Controller.api.bindTabEvents(table);
    },
    recyclebin: function () {
      // 初始化表格参数配置
      Table.api.init({
        extend: {
          'dragsort_url': ''
        }
      });

      var table = $("#table");

      // 初始化表格
      table.bootstrapTable({
        url: 'wefinancial/subject/recyclebin' + location.search,
        pk: 'id',
        sortName: 'id',
        columns: [
          [
            {checkbox: true},
            {field: 'id', title: __('Id')},
            {field: 'name', title: __('Name'), align: 'left'},
            {
              field: 'deletetime',
              title: __('Deletetime'),
              operate: 'RANGE',
              addclass: 'datetimerange',
              formatter: Table.api.formatter.datetime
            },
            {
              field: 'operate',
              width: '140px',
              title: __('Operate'),
              table: table,
              events: Table.api.events.operate,
              buttons: [
                {
                  name: 'Restore',
                  text: __('Restore'),
                  classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                  icon: 'fa fa-rotate-left',
                  url: 'wefinancial/subject/restore',
                  refresh: true
                },
                {
                  name: 'Destroy',
                  text: __('Destroy'),
                  classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                  icon: 'fa fa-times',
                  url: 'wefinancial/subject/destroy',
                  refresh: true
                }
              ],
              formatter: Table.api.formatter.operate
            }
          ]
        ]
      });

      // 为表格绑定事件
      Table.api.bindevent(table);
    },

    add: function () {
      Controller.api.bindevent();
      setTimeout(function () {
        $("#c-type_id").trigger("change");
      }, 100);
    },
    edit: function () {
      Controller.api.bindevent();
    },
    api: {
      bindevent: function () {
        $(document).on("change", "#c-type_id", function () {
          $("#c-pid option[data-type='all']").prop("selected", true);
          $("#c-pid option").removeClass("hide");
          $("#c-pid option[data-type!='" + $(this).val() + "'][data-type!='all']").addClass("hide");
          $("#c-pid").data("selectpicker") && $("#c-pid").selectpicker("refresh");
        });
        Form.api.bindevent($("form[role=form]"));
      },



      // 绑定tab事件
      bindTabEvents: function(table) {
        // 使用click事件而不是shown.bs.tab，避免与Bootstrap默认行为冲突
        $('.nav-tabs a[data-toggle="tab"]').off('click.customTab').on('click.customTab', function(e) {
          e.preventDefault();
          e.stopPropagation();

          var $this = $(this);
          var typeId = $this.data('value');

          // 更新active状态
          $this.closest('.nav-tabs').find('li').removeClass('active');
          $this.parent('li').addClass('active');

          // 重新加载表格数据
          Controller.api.refreshTableWithFilter(table, typeId);

          return false;
        });
      },





      // 根据筛选条件刷新表格数据
      refreshTableWithFilter: function(table, typeId) {
        // 构建新的URL参数
        var baseUrl = 'wefinancial/subject/index';
        var params = [];

        // 保留现有的location.search参数，但移除type_id
        if (location.search) {
          var searchStr = location.search.substring(1);
          var searchPairs = searchStr.split('&');

          for (var i = 0; i < searchPairs.length; i++) {
            var pair = searchPairs[i].split('=');
            var key = decodeURIComponent(pair[0]);
            var value = pair[1] ? decodeURIComponent(pair[1]) : '';

            // 跳过type_id参数
            if (key !== 'type_id' && key !== '') {
              params.push(key + '=' + encodeURIComponent(value));
            }
          }
        }

        // 添加新的type_id参数
        if (typeId !== '') {
          params.push('type_id=' + encodeURIComponent(typeId));
        }

        // 构建完整URL
        var newUrl = baseUrl;
        if (params.length > 0) {
          newUrl += '?' + params.join('&');
        }

        // 更新表格URL并刷新
        table.bootstrapTable('refresh', {
          url: newUrl
        });
      }
    }
  };
  return Controller;
});
