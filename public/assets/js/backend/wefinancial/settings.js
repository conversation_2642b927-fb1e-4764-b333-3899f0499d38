define(['jquery', 'bootstrap', 'backend', 'table', 'form', '/assets/addons/wefinancial/js/common.js'], function ($, undefined, Backend, Table, Form, wef) {

    var Controller = {
        index: function() {
            // 初始化表单验证 - 使用自定义成功回调
            Form.api.bindevent($("#settings-form"), function(data, ret) {
                // 表单提交成功后的处理
                setTimeout(function() {
                    Layer.confirm('设置保存成功！是否立即进行初始余额设置？', function(index){
                        // 重新加载当前标签页
                        Backend.api.addtabs('wefinancial/initial_balance');
                        Layer.close(index);
                        location.reload()
                    }, function (index){
                        Layer.close(index);
                        location.reload()
                    })
                }, 500);
            });

            // 启用期间输入格式化
            $('#c-initial_period').on('input', function() {
                var value = $(this).val().replace(/[^\d-]/g, '');
                if (value.length > 7) {
                    value = value.substring(0, 7);
                }
                if (value.length >= 4 && value.indexOf('-') === -1) {
                    value = value.substring(0, 4) + '-' + value.substring(4);
                }
                $(this).val(value);
            });

            // 纳税人识别号输入限制
            $('#c-tax_no').on('input', function() {
                var value = $(this).val().replace(/[^A-Za-z0-9]/g, '');
                if (value.length > 18) {
                    value = value.substring(0, 18);
                }
                $(this).val(value.toUpperCase());
            });

            // 公司名称和纳税人名称联动
            $('#c-company_name').on('blur', function() {
                var companyName = $(this).val();
                var taxName = $('#c-tax_name').val();
                if (companyName && !taxName) {
                    $('#c-tax_name').val(companyName);
                }
            });

            // 重置按钮事件 - 危险操作确认
            $('#btn-reset').on('click', function() {
                Controller.api.showResetConfirmation();
            });



            // 输入验证提示
            Controller.api.bindValidationTips();
        },

        api: {
            // 绑定验证提示
            bindValidationTips: function() {
                // 启用期间验证
                $('#c-initial_period').on('blur', function() {
                    var value = $(this).val();
                    var pattern = /^\d{4}-\d{2}$/;
                    if (value && !pattern.test(value)) {
                        $(this).closest('.form-group').addClass('has-error');
                        $(this).siblings('.help-block').html('<span class="text-danger">格式错误，请使用YYYY-MM格式</span>');
                    } else {
                        $(this).closest('.form-group').removeClass('has-error');
                        $(this).siblings('.help-block').html('格式：YYYY-MM，如：2025-01');
                    }
                });

                // 纳税人识别号验证
                $('#c-tax_no').on('blur', function() {
                    var value = $(this).val();
                    if (value && value.length < 15) {
                        $(this).closest('.form-group').addClass('has-error');
                        $(this).siblings('.help-block').html('<span class="text-danger">纳税人识别号长度不足</span>');
                    } else {
                        $(this).closest('.form-group').removeClass('has-error');
                        $(this).siblings('.help-block').html('统一社会信用代码或税务登记号');
                    }
                });
            },

            // 显示重置确认对话框
            showResetConfirmation: function() {
                // 先获取重置信息
                $.get('wefinancial/settings/getResetInfo', function(data) {
                    if (data.code === 1) {
                        var info = data.data;
                        var warningHtml = '<div style="color: #d9534f; font-weight: bold; margin-bottom: 15px;">' +
                            '<i class="fa fa-exclamation-triangle"></i> 危险操作警告</div>' +
                            '<div style="margin-bottom: 15px;">重置后将清空您录入的所有数据，包括：</div>' +
                            '<ul style="margin-bottom: 15px; padding-left: 20px;">' +
                            '<li>凭证数据：' + info.voucher_count + ' 张凭证，' + info.entry_count + ' 条分录</li>' +
                            '<li>期间数据：' + info.period_count + ' 个会计期间</li>' +
                            '<li>余额数据：' + info.balance_count + ' 条期间余额记录</li>' +
                            '<li>会计科目：' + info.user_subject_count + ' 个会计科目</li>' +
                            '<li>配置数据：所有初始化设置</li>' +
                            '</ul>' +
                            '<div style="color: #d9534f; font-weight: bold; margin-bottom: 15px;">' +
                            '此操作不可逆，总计将删除 ' + info.total_records + ' 条记录！</div>' +
                            '<div style="margin-bottom: 15px;">如果确认要重置，请在下方输入框中输入 <strong>CONFIRM_RESET</strong> 确认：</div>' +
                            '<input type="text" id="reset-confirm-input" class="form-control" placeholder="请输入 CONFIRM_RESET" style="margin-bottom: 10px;">';

                        Layer.open({
                            type: 1,
                            title: '系统重置确认',
                            icon: 0,
                            area: ['600px', 'auto'],
                            content: '<div style="padding: 20px;">' + warningHtml + '</div>',
                            btn: ['确认重置', '取消'],
                            btn1: function(index) {
                                var confirmText = $('#reset-confirm-input').val();
                                if (confirmText !== 'CONFIRM_RESET') {
                                    Toastr.error('请正确输入确认文本：CONFIRM_RESET');
                                    return false;
                                }

                                // 执行重置
                                Controller.api.executeReset(index);
                                return false; // 阻止默认关闭
                            },
                            btn2: function(index) {
                                Layer.close(index);
                            }
                        });
                    } else {
                        Toastr.error('获取重置信息失败：' + data.msg);
                    }
                }).fail(function() {
                    Toastr.error('获取重置信息失败，请重试');
                });
            },

            // 执行重置操作
            executeReset: function(layerIndex) {
                var loadingIndex = Layer.load(1, {shade: [0.3, '#000']});

                $.post('wefinancial/settings/reset', {
                    confirm: 'CONFIRM_RESET'
                }, function(data) {
                    Layer.close(loadingIndex);

                    if (data.code === 1) {
                        Layer.close(layerIndex);
                        Toastr.success(data.msg);
                        setTimeout(function() {
                            location.reload();
                        }, 3000);
                    } else {
                        Toastr.error(data.msg);
                    }
                }).fail(function() {
                    Layer.close(loadingIndex);
                    Toastr.error('重置失败，请重试');
                });
            }
        }
    };

    return Controller;
});
