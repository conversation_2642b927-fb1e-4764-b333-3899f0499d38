define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'wefinancial/report_item_subject/index' + location.search,
                    add_url: 'wefinancial/report_item_subject/add',
                    edit_url: 'wefinancial/report_item_subject/edit',
                    del_url: 'wefinancial/report_item_subject/del',
                    multi_url: 'wefinancial/report_item_subject/multi',
                    import_url: 'wefinancial/report_item_subject/import',
                    table: 'wefinancial_report_item_subject',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'item_id', title: __('Item_id')},
                        {field: 'subject_id', title: __('Subject_id')},
                        {field: 'operation_type', title: __('Operation_type'), searchList: {"add":__('Operation_type add'),"subtract":__('Operation_type subtract')}, formatter: Table.api.formatter.normal},
                        {field: 'balance_direction', title: __('Balance_direction'), searchList: {"debit":__('Balance_direction debit'),"credit":__('Balance_direction credit'),"balance":__('Balance_direction balance')}, formatter: Table.api.formatter.normal},
                        {field: 'balance_type', title: __('Balance_type'), searchList: {"beginning":__('Balance_type beginning'),"ending":__('Balance_type ending'),"period":__('Balance_type period')}, formatter: Table.api.formatter.normal},
                        {field: 'weight', title: __('Weight'), operate:'BETWEEN'},
                        {field: 'status', title: __('Status'), searchList: {"1":__('Status 1'),"0":__('Status 0')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
