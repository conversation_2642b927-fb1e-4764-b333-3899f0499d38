<?php

namespace app\admin\model\wefinancial;

use think\Model;


class ReportItem extends Model
{

    

    

    // 表名
    protected $name = 'wefinancial_report_item';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'column_position_text',
        'type_text',
        'calculation_type_text',
        'bold_text',
        'status_text'
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            if (!$row['weigh']) {
                $pk = $row->getPk();
                $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
            }
        });
    }

    
    public function getColumnPositionList()
    {
        return ['left' => __('Column_position left'), 'right' => __('Column_position right'), 'single' => __('Column_position single')];
    }

    public function getTypeList()
    {
        return ['header' => __('Type header'), 'detail' => __('Type detail'), 'subtotal' => __('Type subtotal'), 'total' => __('Type total'), 'section' => __('Type section')];
    }

    public function getCalculationTypeList()
    {
        return ['formula' => __('Calculation_type formula'), ' direct' => __('Calculation_type  direct')];
    }

    public function getBoldList()
    {
        return ['1' => __('Bold 1'), '0' => __('Bold 0')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '0' => __('Status 0')];
    }


    public function getColumnPositionTextAttr($value, $data)
    {
        $value = $value ?: ($data['column_position'] ?? '');
        $list = $this->getColumnPositionList();
        return $list[$value] ?? '';
    }


    public function getTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['type'] ?? '');
        $list = $this->getTypeList();
        return $list[$value] ?? '';
    }


    public function getCalculationTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['calculation_type'] ?? '');
        $list = $this->getCalculationTypeList();
        return $list[$value] ?? '';
    }


    public function getBoldTextAttr($value, $data)
    {
        $value = $value ?: ($data['bold'] ?? '');
        $list = $this->getBoldList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
