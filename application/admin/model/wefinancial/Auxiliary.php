<?php

namespace app\admin\model\wefinancial;

use think\Model;
use traits\model\SoftDelete;

class Auxiliary extends Model
{

    use SoftDelete;



    // 表名
    protected $name = 'wefinancial_auxiliary';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];


    public function type()
    {
        return $this->belongsTo(AuxiliaryType::class, 'aux_type_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }



}
