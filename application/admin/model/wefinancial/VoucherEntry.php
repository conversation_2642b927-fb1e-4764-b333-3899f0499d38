<?php

namespace app\admin\model\wefinancial;

use think\Model;
use traits\model\SoftDelete;

class VoucherEntry extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'wefinancial_voucher_entry';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'aux_names'
    ];
    

    







    /**
     * 关联凭证
     */
    public function voucher()
    {
        return $this->belongsTo('Voucher', 'voucher_id', 'id');
    }

    /**
     * 关联科目
     */
    public function subject()
    {
        return $this->belongsTo('Subject', 'subject_id', 'id');
    }

    /**
     * 获取辅助核算名称
     */
    public function getAuxNamesAttr($value, $data)
    {
        $auxIds = $data['aux_ids'] ?? '';
        if (empty($auxIds)) {
            return '--';
        }

        $auxIdArray = explode(',', $auxIds);
        $auxIdArray = array_filter(array_map('trim', $auxIdArray));

        if (empty($auxIdArray)) {
            return '--';
        }

        $auxiliaries = \app\admin\model\wefinancial\Auxiliary::whereIn('id', $auxIdArray)
            ->field('id,name,code')
            ->select();

        if (empty($auxiliaries)) {
            return '--';
        }

        $names = [];
        foreach ($auxiliaries as $aux) {
            $names[] = $aux->code ? ($aux->code . ' - ' . $aux->name) : $aux->name;
        }

        return implode(', ', $names);
    }

}
