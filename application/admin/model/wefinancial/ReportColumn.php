<?php

namespace app\admin\model\wefinancial;

use think\Model;


class ReportColumn extends Model
{

    

    

    // 表名
    protected $name = 'wefinancial_report_column';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'data_type_text',
        'period_type_text',
        'balance_type_text',
        'status_text'
    ];
    

    
    public function getDataTypeList()
    {
        return ['amount' => __('Data_type amount'), 'percentage' => __('Data_type percentage'), 'text' => __('Data_type text')];
    }

    public function getPeriodTypeList()
    {
        return ['current_month' => __('Period_type current_month'), 'current_year' => __('Period_type current_year'), 'last_year' => __('Period_type last_year'), 'last_month' => __('Period_type last_month'), 'custom' => __('Period_type custom')];
    }

    public function getBalanceTypeList()
    {
        return ['beginning' => __('Balance_type beginning'), 'ending' => __('Balance_type ending'), 'period' => __('Balance_type period')];
    }

    public function getStatusList()
    {
        return ['1' => __('Status 1'), '0' => __('Status 0')];
    }


    public function getDataTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['data_type'] ?? '');
        $list = $this->getDataTypeList();
        return $list[$value] ?? '';
    }


    public function getPeriodTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['period_type'] ?? '');
        $list = $this->getPeriodTypeList();
        return $list[$value] ?? '';
    }


    public function getBalanceTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['balance_type'] ?? '');
        $list = $this->getBalanceTypeList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
