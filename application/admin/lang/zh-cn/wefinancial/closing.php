<?php

return [
    'Closing'           => '期间结账',
    'Period Closing'    => '期间结账',
    'Period'           => '会计期间',
    'Status'           => '结账状态',
    'Status 1'         => '已结账',
    'Status 0'         => '未结账',
    'Voucher Stats'    => '凭证统计',
    'Total Vouchers'   => '凭证总数',
    'Draft Vouchers'   => '草稿凭证',
    'Audited Vouchers' => '已审核凭证',
    'Posted Vouchers'  => '已记账凭证',
    'Can Close'        => '可结账',
    'Can Unclose'      => '可反结账',
    'Close Period'     => '结账',
    'Unclose Period'   => '反结账',
    'Remark'           => '备注',
    'Createtime'       => '创建时间',
    'Updatetime'       => '更新时间',
    
    // 操作提示
    'Close Confirm'    => '结账确认',
    'Unclose Confirm'  => '反结账确认',
    'Close Success'    => '结账成功',
    'Unclose Success'  => '反结账成功',
    'Close Failed'     => '结账失败',
    'Unclose Failed'   => '反结账失败',
    
    // 错误信息
    'Period Not Found'           => '期间不存在',
    'Period Already Closed'      => '该期间已经结账',
    'Period Not Closed'          => '该期间未结账',
    'Previous Period Not Closed' => '上期尚未结账，请先结账上期',
    'Next Period Has Business'   => '下期已有业务发生，不能反结账',
    'Unposted Vouchers Exist'    => '存在未记账凭证，请先完成记账操作',
    'Uncarried Profit Loss'      => '存在未结转的损益科目',

    // 损益结转相关
    'Profit Loss Check'          => '损益结转检查',
    'Uncarried Profit Loss Found' => '发现未结转损益',
    'Manual Carry Forward'       => '手动结转',
    'Force Close'               => '强制结账',
    'Force Close Confirm'       => '强制结账确认',
    'Profit Loss Dialog Title'  => '损益结转检查',
    'Subject Code'              => '科目编码',
    'Subject Name'              => '科目名称',
    'Subject Type'              => '科目类型',
    'End Balance'               => '期末余额',
    'Balance Direction'         => '余额方向',
    
    // 帮助信息
    'Closing Help' => [
        '结账前请确保本期所有凭证已审核并记账',
        '结账操作将锁定当前期间，不可再录入或修改凭证',
        '反结账前请确保下期没有业务发生',
        '建议按期间顺序进行结账操作'
    ]
];
