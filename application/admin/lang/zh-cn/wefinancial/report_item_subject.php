<?php

return [
    'Id'                        => '主键ID',
    'Item_id'                   => '报表项目ID',
    'Subject_id'                => '科目ID',
    'Operation_type'            => '运算类型',
    'Operation_type add'        => '加',
    'Operation_type subtract'   => '减',
    'Balance_direction'         => '取数方向',
    'Balance_direction debit'   => '借方',
    'Balance_direction credit'  => '贷方',
    'Balance_direction balance' => '余额',
    'Balance_type'              => '余额类型',
    'Balance_type beginning'    => '期初',
    'Balance_type ending'       => '期末',
    'Balance_type period'       => '本期发生',
    'Weight'                    => '权重系数',
    'Status'                    => '状态',
    'Status 1'                  => '启用',
    'Set status to 1'           => '设为启用',
    'Status 0'                  => '禁用',
    'Set status to 0'           => '设为禁用',
    'Createtime'                => '创建时间',
    'Updatetime'                => '更新时间'
];
