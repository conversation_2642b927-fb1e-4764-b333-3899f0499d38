<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;
use fast\Tree;
use think\exception\DbException;
use addons\wefinancial\library\WeTree;

/**
 * 科目类别
 *
 * @icon fa fa-circle-o
 */
class SubjectType extends Backend
{

    /**
     * SubjectType模型对象
     * @var \app\admin\model\wefinancial\SubjectType
     */
    protected $model = null;
    protected array $subjectTypeList = [];

    /**
     * @throws DbException
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\SubjectType;

        $tree = Tree::instance();
        $list = $this->model::all(function ($query) {
            $query->order('id asc');
        });
        $tree->init(collection($list)->toArray(), 'pid');
        $this->subjectTypeList = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $parentList = [0 => ['id' => 0, 'name' => '无上级']];
        foreach ($this->subjectTypeList as $v) {
            $parentList[$v['id']] = $v;
        }
        $this->assign('parentList', $parentList);

        $this->view->assign("statusList", $this->model->getStatusList());
    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            $list = $this->model::all(function ($query) {
                $query->order('id asc');
            });

            // 转换为数组以便处理
            $list = collection($list)->toArray();

            // 使用WeTree工具类进行树形排序，确保父节点在子节点之前
            WeTree::sortTreeData($list, 'id', 'pid', 'id', 'asc');

            // 使用WeTree工具类处理树形数据
            WeTree::processTreeData($list);

            $total = count($list);
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->fetch();
    }


}
