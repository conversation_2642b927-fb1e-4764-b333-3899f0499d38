<?php

namespace app\admin\controller\wefinancial;

use app\common\controller\Backend;
use fast\Tree;
use think\exception\DbException;
use addons\wefinancial\library\WeTree;
use PhpOffice\PhpSpreadsheet\IOFactory;
use think\Db;

/**
 * 会计科目
 *
 * @icon fa fa-circle-o
 */
class Subject extends Backend
{

    /**
     * Subject模型对象
     * @var \app\admin\model\wefinancial\Subject
     */
    protected $model = null;
    protected array $subjectList = [];
    protected array $typeList = [];
    protected $selectpageFields = 'code,name,id';

    /**
     * @throws DbException
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\wefinancial\Subject;

        $tree = Tree::instance();

        $list = $this->model::all(function ($query) {
            $query->order('code asc, id asc');
        }, ['type']);

        $tree->init(collection($list)->toArray(), 'pid');
        $this->subjectList = $tree->getTreeList($tree->getTreeArray(0), 'name');

        $parentList = [0 => ['id' => 0, 'type' => 'all', 'name' => '无上级']] + $this->subjectList;
        foreach ($this->subjectList as $v) {
            $parentList[$v['id']] = $v;
        }
        $this->assign('parentList', $parentList);

        $typeTree = Tree::instance();
        $typeList = \app\admin\model\wefinancial\SubjectType::all(function ($query) {
            $query->where('status', 1)->order('id asc');
        });
        $typeTree->init(collection($typeList)->toArray(), 'pid');
        $this->typeList = $typeTree->getTreeList($typeTree->getTreeArray(0), 'name');

        $tagList = \app\admin\model\wefinancial\SubjectType::all(function ($query) {
            $query->where('status', 1)->where('pid', 0)->order('id asc');
        });

        $this->view->assign("balanceDirectionList", $this->model->getBalanceDirectionList());
        $this->assign("cashFlagList", $this->model->getCashFlagList());
        $this->view->assign("tagList", $tagList);
        $this->assign('typeList', $this->typeList);
        $this->view->assign("leafFlagList", $this->model->getLeafFlagList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     * @throws DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是 Selectpage，则转发到 Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            //构造父类select列表选项数据

            $list = $this->model::all(function ($query) {
                // 获取type_id筛选参数
                $type_id = $this->request->get('type_id', '');

                if ($type_id !== '') {
                    // 获取指定类型及其所有子类型的ID
                    $typeIds = $this->getTypeIdsWithChildren($type_id);
                    if (!empty($typeIds)) {
                        $query->whereIn('type_id', $typeIds);
                    }
                }

                $query->order('code asc, id asc');
            }, ['type']);

            // 转换为数组以便处理
            $list = collection($list)->toArray();

            // 使用WeTree工具类进行树形排序，确保父节点在子节点之前
            WeTree::sortTreeData($list, 'id', 'pid', 'code', 'asc');

            // 处理树形数据，计算level和leaf_flag
            WeTree::processTreeData($list);

            $total = count($list);
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->fetch();
    }

    /**
     * 获取指定类型及其所有子类型的ID
     * @param int $typeId 类型ID
     * @return array
     */
    private function getTypeIdsWithChildren($typeId)
    {
        if (empty($typeId)) {
            return [];
        }

        $tree = Tree::instance();
        $typeList = \app\admin\model\wefinancial\SubjectType::all(function ($query) {
            $query->where('status', 1)->order('id asc');
        });
        $tree->init(collection($typeList)->toArray(), 'pid');

        // 获取指定节点及其所有子节点的ID
        $childrenIds = $tree->getChildrenIds($typeId, true);

        return $childrenIds;
    }

    /**
     * 添加
     * 覆写父类方法，实现自动写入level与leaf_flag的功能
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }

        // 自动计算level和leaf_flag
        $this->calculateLevelAndLeafFlag($params);

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);

            // 如果有父级科目，需要将父级科目的leaf_flag设置为0
            if (!empty($params['pid']) && $params['pid'] > 0) {
                $this->model->where('id', $params['pid'])->update(['leaf_flag' => 0]);
            }

            \think\Db::commit();
        } catch (\think\exception\ValidateException|\PDOException|\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     * 覆写父类方法，实现自动写入level与leaf_flag的功能
     *
     * @param $ids
     * @return string
     * @throws \think\exception\DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $oldPid = $row['pid'];
        $newPid = $params['pid'] ?? $oldPid;

        // 如果父级科目发生变化，重新计算level
        if ($oldPid != $newPid) {
            $this->calculateLevelAndLeafFlag($params);
        }

        $result = false;
        \think\Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);

            // 处理父级科目的leaf_flag变化
            if ($oldPid != $newPid) {
                WeTree::updateParentLeafFlag($oldPid, $newPid, $ids, $this->model);
            }

            \think\Db::commit();
        } catch (\think\exception\ValidateException|\PDOException|\Exception $e) {
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 计算科目层级和末级标志
     * @param array $params 参数数组（引用传递）
     */
    private function calculateLevelAndLeafFlag(&$params)
    {
        $pid = $params['pid'] ?? 0;
        $result = WeTree::calculateLevelAndLeafFlag($pid, $this->model);

        $params['level'] = $result['level'];
        $params['leaf_flag'] = $result['leaf_flag'];
    }

    /**
     * Excel导入科目数据
     * @param string $filePath Excel文件路径
     * @return void
     */
    public function importFromExcel($filePath)
    {
        $result = $this->model->importFromExcel($filePath);

        if ($result['success']) {
            $this->success($result['message']);
        } else {
            $this->error($result['message']);
        }
    }

}
