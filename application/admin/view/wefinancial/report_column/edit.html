<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Template_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-template_id" data-rule="required" data-source="template/index" class="form-control selectpage" name="row[template_id]" type="text" value="{$row.template_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_name" data-rule="required" class="form-control" name="row[column_name]" type="text" value="{$row.column_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_position')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-column_position" data-rule="required" class="form-control" name="row[column_position]" type="number" value="{$row.column_position|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Data_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-data_type" class="form-control selectpicker" name="row[data_type]">
                {foreach name="dataTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.data_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Period_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-period_type" data-rule="required" class="form-control selectpicker" name="row[period_type]">
                {foreach name="periodTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.period_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-balance_type" class="form-control selectpicker" name="row[balance_type]">
                {foreach name="balanceTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.balance_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Decimal_places')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-decimal_places" class="form-control" name="row[decimal_places]" type="number" value="{$row.decimal_places|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Show_unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-show_unit" class="form-control" name="row[show_unit]" type="text" value="{$row.show_unit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Width')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-width" class="form-control" name="row[width]" type="number" value="{$row.width|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
