<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs" data-field="type">
            <li class="active"><a href="#t-all" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <!-- 刷新按钮 -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col-xs-12">
                        <button type="button" class="btn btn-success btn-refresh">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 报表模板列表 -->
                <div class="row" id="report-templates">
                    {if condition="empty($templates)"}
                    <div class="col-xs-12">
                        <div class="alert alert-info">暂无可用的报表模板</div>
                    </div>
                    {else}
                    {volist name="templates" id="template"}
                    <div class="col-md-4 col-sm-6 col-xs-12">
                        <div class="report-template-card" data-template-id="{$template.id}" style="cursor: pointer; margin-bottom: 20px;">
                            <div class="panel panel-default">
                                <div class="panel-body text-center">
                                    <i class="fa fa-file-text-o fa-3x" style="color: #337ab7; margin-bottom: 15px;"></i>
                                    <h4 class="template-name">{$template.name}</h4>
                                    <p class="text-muted">{$template.description|default=''}</p>
                                    <span class="label label-primary">{$template.type}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/volist}
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>

