<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Subject_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-subject_id" data-rule="required" data-source="subject/index" class="form-control selectpage" name="row[subject_id]" type="text" value="{$row.subject_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Period')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-period" data-rule="required" class="form-control" name="row[period]" type="text" value="{$row.period|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Begin_balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-begin_balance" class="form-control" step="0.01" name="row[begin_balance]" type="number" value="{$row.begin_balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Begin_direction')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-begin_direction" data-rule="required" class="form-control selectpicker" name="row[begin_direction]">
                {foreach name="beginDirectionList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.begin_direction"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Debit_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-debit_total" class="form-control" step="0.01" name="row[debit_total]" type="number" value="{$row.debit_total|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Credit_total')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-credit_total" class="form-control" step="0.01" name="row[credit_total]" type="number" value="{$row.credit_total|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_balance')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_balance" class="form-control" step="0.01" name="row[end_balance]" type="number" value="{$row.end_balance|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End_direction')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-end_direction" data-rule="required" class="form-control selectpicker" name="row[end_direction]">
                {foreach name="endDirectionList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.end_direction"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
