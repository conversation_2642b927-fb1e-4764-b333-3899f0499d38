<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <!-- 凭证基本信息 - 紧凑布局 -->
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-3">{:__('Voucher_date')}:</label>
                <div class="col-xs-12 col-sm-9">
                    <input id="c-voucher_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[voucher_date]" type="text" value="{$row.voucher_date}">
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-4">{:__('Voucher_type')}:</label>
                <div class="col-xs-12 col-sm-8">

                    <select  id="c-voucher_type" data-rule="required" class="form-control selectpicker" name="row[voucher_type]">
                        {foreach name="voucherTypeList" item="vo"}
                        <option value="{$key}" {in name="key" value="$row.voucher_type"}selected{/in}>{$vo}</option>
                        {/foreach}
                    </select>

                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('Summary')}:</label>
                <div class="col-xs-12 col-sm-10">
                    <input id="c-summary" class="form-control" name="row[summary]" type="text" value="{$row.summary|htmlentities}" placeholder="请输入凭证摘要">
                </div>
            </div>
        </div>
    </div>

    <!-- 凭证分录 -->
    <div class="form-group">
        <div class="col-xs-12 col-sm-12">
            <div class="panel panel-default">
                <div class="panel-heading clearfix">
                    <h4 class="panel-title pull-left panel-title-aligned">
                        分录明细
                    </h4>
                    <button type="button" class="btn btn-success btn-add btn-sm pull-right" id="add-entry">
                        <i class="fa fa-plus"></i> 添加分录
                    </button>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="entries-table">
                            <thead>
                                <tr>
                                    <th width="25%">科目</th>
                                    <th width="20%">摘要</th>
                                    <th width="12%">借方金额</th>
                                    <th width="12%">贷方金额</th>
                                    <th width="25%">辅助核算</th>
                                    <th width="6%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="entries-tbody">
                                {volist name="entries" id="entry" key="i"}
                                <tr class="entry-row">
                                    <td>
                                        <select name="entries[{$i-1}][subject_id]" class="form-control subject-select" data-rule="required">
                                            <option value="">请选择科目</option>
                                            {foreach name="subjectList" item="subject"}
                                            <option value="{$subject.id}" {if $subject.id == $entry.subject_id}selected{/if}>{$subject.code} - {$subject.name}</option>
                                            {/foreach}
                                        </select>
                                    </td>
                                    <td>
                                        <input type="text" name="entries[{$i-1}][summary]" class="form-control" value="{$entry.summary|htmlentities}" placeholder="摘要">
                                    </td>
                                    <td>
                                        <input type="number" name="entries[{$i-1}][debit_amount]" class="form-control debit-amount" value="{if $entry.debit_amount > 0}{$entry.debit_amount}{/if}" step="0.01" min="0" placeholder="0.00">
                                    </td>
                                    <td>
                                        <input type="number" name="entries[{$i-1}][credit_amount]" class="form-control credit-amount" value="{if $entry.credit_amount > 0}{$entry.credit_amount}{/if}" step="0.01" min="0" placeholder="0.00">
                                    </td>
                                    <td class="aux-cell" data-field-name="entries[{$i-1}][aux_ids]" data-subject-id="{$entry.subject_id}" data-aux-ids="{$entry.aux_ids|default=''}">
                                        <input type="hidden" name="entries[{$i-1}][aux_ids]" value="{$entry.aux_ids|default=''}" class="aux-ids-input">
                                        <div class="aux-hint">加载中...</div>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-xs btn-danger remove-entry">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {/volist}
                            </tbody>
                            <tfoot>
                                <tr class="info">
                                    <td colspan="2"><strong>合计</strong></td>
                                    <td><strong id="debit-total">0.00</strong></td>
                                    <td><strong id="credit-total">0.00</strong></td>
                                    <td colspan="2">
                                        <span id="balance-status" class="label label-warning">未平衡</span>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-1">{:__('Attachment_files')}:</label>
        <div class="col-xs-12 col-sm-11">
            <div class="input-group">
                <input id="c-attachment_files" class="form-control" size="50" name="row[attachment_files]" type="text" value="{$row.attachment_files|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-attachment_files" class="btn btn-danger faupload" data-input-id="c-attachment_files" data-multiple="true" data-preview-id="p-attachment_files"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-attachment_files" class="btn btn-primary fachoose" data-input-id="c-attachment_files" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-attachment_files"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-attachment_files"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            {if $row.status == '0' || !isset($row.status)}
            <!-- 草稿状态或新建时显示保存按钮 -->
            <button type="submit" name="action" value="save" class="btn btn-primary btn-embossed disabled">
                <i class="fa fa-save"></i> 保存
            </button>
            <button type="submit" name="action" value="save_and_audit" class="btn btn-success btn-embossed disabled">
                <i class="fa fa-check"></i> 保存并审核
            </button>
            {else}
            <!-- 非草稿状态不允许编辑 -->
            <div class="alert alert-warning">
                <i class="fa fa-warning"></i> 当前凭证状态为"{$row.status_text}"，不允许编辑。
            </div>
            {/if}
        </div>
    </div>
</form>

<script>
// 科目列表数据
var subjectList = {$subjectList|json_encode};
</script>
