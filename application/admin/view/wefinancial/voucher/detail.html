<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">
            <i class="fa fa-file-text-o"></i> 凭证详情 - {$row.voucher_no}
            <span class="pull-right">
                {if $row.status == '0'}
                    <span class="label label-warning"><i class="fa fa-edit"></i> {$row.status_text}</span>
                {elseif $row.status == '1'}
                    <span class="label label-success"><i class="fa fa-check"></i> {$row.status_text}</span>
                {else}
                    <span class="label label-primary"><i class="fa fa-book"></i> {$row.status_text}</span>
                {/if}
            </span>
        </h3>
    </div>
    <div class="panel-body">
        <!-- 基本信息 -->
        <div class="row">
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">{:__('Voucher_date')}</label>
                    <p class="form-control-static">{$row.voucher_date}</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">{:__('Voucher_type')}</label>
                    <p class="form-control-static">{$row.voucher_type}</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">{:__('Voucher_no')}</label>
                    <p class="form-control-static">{$row.voucher_no}</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">{:__('Period')}</label>
                    <p class="form-control-static">{$row.period}</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">创建人</label>
                    <p class="form-control-static">{$row.create_user_nickname}</p>
                </div>
            </div>
            <div class="col-md-2 col-sm-4">
                <div class="form-group">
                    <label class="control-label">审核人</label>
                    <p class="form-control-static">{$row.audit_user_nickname}</p>
                </div>
            </div>
        </div>

        {if $row.audit_time || $row.post_time}
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label class="control-label">记账人</label>
                    <p class="form-control-static">{$row.post_user_nickname}</p>
                </div>
            </div>
            {if $row.audit_time}
            <div class="col-md-3">
                <div class="form-group">
                    <label class="control-label">审核时间</label>
                    <p class="form-control-static">{$row.audit_time}</p>
                </div>
            </div>
            {/if}
            {if $row.post_time}
            <div class="col-md-3">
                <div class="form-group">
                    <label class="control-label">记账时间</label>
                    <p class="form-control-static">{$row.post_time}</p>
                </div>
            </div>
            {/if}
            <div class="col-md-3">
                <div class="form-group">
                    <label class="control-label">创建时间</label>
                    <p class="form-control-static">{$row.createtime|datetime}</p>
                </div>
            </div>
        </div>
        {else}
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">记账人</label>
                    <p class="form-control-static">{$row.post_user_nickname}</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">创建时间</label>
                    <p class="form-control-static">{$row.createtime|datetime}</p>
                </div>
            </div>
        </div>
        {/if}

        <!-- 凭证摘要 -->
        {if $row.summary}
        <div class="form-group">
            <label class="control-label">{:__('Summary')}</label>
            <p class="form-control-static">{$row.summary}</p>
        </div>
        {/if}

        <!-- 分录明细 -->
        <div class="form-group">
            <label class="control-label">分录明细</label>
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="entries-table">
                    <thead>
                        <tr class="info">
                            <th width="20%">科目</th>
                            <th width="25%">摘要</th>
                            <th width="15%" class="text-right">借方金额</th>
                            <th width="15%" class="text-right">贷方金额</th>
                            <th width="25%">辅助核算</th>
                        </tr>
                    </thead>
                    <tbody id="entries-tbody">
                        {volist name="entries" id="entry" key="i"}
                        <tr>
                            <td>
                                {if $entry.subject}
                                    <small class="text-muted">{$entry.subject.code}</small><br>
                                    {$entry.subject.name}
                                {else}
                                    <span class="text-muted">--</span>
                                {/if}
                            </td>
                            <td>{$entry.summary|default='--'}</td>
                            <td class="text-right">
                                {if $entry.debit_amount > 0}
                                    <span class="text-danger">{$entry.debit_amount|number_format=2}</span>
                                {else}
                                    <span class="text-muted">--</span>
                                {/if}
                            </td>
                            <td class="text-right">
                                {if $entry.credit_amount > 0}
                                    <span class="text-success">{$entry.credit_amount|number_format=2}</span>
                                {else}
                                    <span class="text-muted">--</span>
                                {/if}
                            </td>
                            <td>{$entry.aux_names|default='--'}</td>
                        </tr>
                        {/volist}
                    </tbody>
                    <tfoot>
                        <tr class="info">
                            <td colspan="2"><strong>合计</strong></td>
                            <td class="text-right"><strong id="debit-total">0.00</strong></td>
                            <td class="text-right"><strong id="credit-total">0.00</strong></td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <!-- 附件 -->
        {if $row.attachment_files}
        <div class="form-group">
            <label class="control-label">{:__('Attachment_files')}</label>
            <div>
                {php}
                $files = explode(',', $row['attachment_files']);
                foreach($files as $file) {
                    if(trim($file)) {
                        echo '<a href="' . trim($file) . '" target="_blank" class="btn btn-xs btn-default" style="margin: 2px;"><i class="fa fa-download"></i> ' . basename(trim($file)) . '</a> ';
                    }
                }
                {/php}
            </div>
        </div>
        {/if}

    </div>
</div>

<!-- 操作按钮区域 - 移动到layer-footer -->
<div class="form-group layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
        <button type="reset" class="btn btn-primary btn-embossed btn-close" onclick="Layer.closeAll();">{:__('Close')}</button>
    </div>
</div>


