<div class="row">
    <div class="col-xs-12">
        <div class="box">
            <div class="box-header">
                <h3 class="box-title">财务初始余额设置</h3>
                <div class="box-tools">
                    <div class="btn-group">
                        <button type="button" class="btn btn-primary btn-sm" id="btn-save">
                            <i class="fa fa-save"></i> 保存初始余额
                        </button>
                        <button type="button" class="btn btn-info btn-sm" id="btn-trial-balance">
                            <i class="fa fa-calculator"></i> 试算平衡
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="btn-expand-all">
                            <i class="fa fa-plus-square-o"></i> 展开全部
                        </button>
                        <button type="button" class="btn btn-warning btn-sm" id="btn-collapse-all">
                            <i class="fa fa-minus-square-o"></i> 收起全部
                        </button>
                    </div>
                </div>
            </div>
            <div class="box-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    <strong>说明：</strong>
                    <ul class="mb-0 mt-1">
                        <li>当前设置的是 <strong>{$initialPeriod}</strong> 期间的初始余额
                            <span class="label label-{$periodStatus == 1 ? 'danger' : 'success'} ml-2">
                                {$periodStatusText}
                            </span>
                        </li>
                        {if $periodStatus == 1}
                        <li class="text-danger"><strong>注意：该期间已结账，不允许设置或修改初始余额</strong></li>
                        {/if}
                        <li>只需要为有余额的科目输入期初余额，余额为0的科目可以不填</li>
                        <li>余额方向默认为科目的余额方向，可以根据实际情况修改</li>
                        <li>输入完成后请点击"试算平衡"检查借贷是否平衡</li>
                        <li>确认无误后点击"保存初始余额"完成设置</li>
                    </ul>
                </div>

                <!-- 试算平衡结果显示区域 -->
                <div id="trial-balance-result" class="alert" style="display: none;">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>借方合计：</strong> <span id="debit-total">0.00</span>
                        </div>
                        <div class="col-md-3">
                            <strong>贷方合计：</strong> <span id="credit-total">0.00</span>
                        </div>
                        <div class="col-md-3">
                            <strong>差额：</strong> <span id="difference">0.00</span>
                        </div>
                        <div class="col-md-3">
                            <strong>结果：</strong> <span id="balance-message">-</span>
                        </div>
                    </div>
                </div>

                <form id="initial-balance-form">
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                    </table>
                </form>
            </div>
        </div>
    </div>
</div>

{block name="script"}
<script>
    // 传递后端数据到前端
    window.initialPeriod = '{$initialPeriod}';
    window.periodStatus = {$periodStatus};
    window.periodStatusText = '{$periodStatusText}';
    window.beginDirectionList = {$beginDirectionList|json_encode};
</script>
{/block}
