<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Template_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-template_id" data-rule="required" data-source="template/index" class="form-control selectpage" name="row[template_id]" type="text" value="{$row.template_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Item_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-item_name" data-rule="required" class="form-control" name="row[item_name]" type="text" value="{$row.item_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Line_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-line_number" data-rule="required" class="form-control" name="row[line_number]" type="number" value="{$row.line_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Column_position')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-column_position" class="form-control selectpicker" name="row[column_position]">
                {foreach name="columnPositionList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.column_position"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pid" class="form-control" name="row[pid]" type="number" value="{$row.pid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-level" class="form-control" name="row[level]" type="number" value="{$row.level|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-type" class="form-control selectpicker" name="row[type]">
                {foreach name="typeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Calculation_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-calculation_type" class="form-control selectpicker" name="row[calculation_type]">
                {foreach name="calculationTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.calculation_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Calculation_formula')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-calculation_formula" class="form-control " rows="5" name="row[calculation_formula]" cols="50">{$row.calculation_formula|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" data-rule="required" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bold')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-bold" class="form-control selectpicker" name="row[bold]">
                {foreach name="boldList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.bold"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Indent')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-indent" class="form-control" name="row[indent]" type="number" value="{$row.indent|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
